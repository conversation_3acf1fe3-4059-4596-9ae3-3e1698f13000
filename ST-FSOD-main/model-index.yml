Import:
  - configs/classification/baseline/cub/metafile.yml
  - configs/classification/baseline/mini_imagenet/metafile.yml
  - configs/classification/baseline/tiered_imagenet/metafile.yml
  - configs/classification/baseline_plus/cub/metafile.yml
  - configs/classification/baseline_plus/mini_imagenet/metafile.yml
  - configs/classification/baseline_plus/tiered_imagenet/metafile.yml
  - configs/classification/maml/cub/metafile.yml
  - configs/classification/maml/mini_imagenet/metafile.yml
  - configs/classification/maml/tiered_imagenet/metafile.yml
  - configs/classification/matching_net/cub/metafile.yml
  - configs/classification/matching_net/mini_imagenet/metafile.yml
  - configs/classification/matching_net/tiered_imagenet/metafile.yml
  - configs/classification/meta_baseline/cub/metafile.yml
  - configs/classification/meta_baseline/mini_imagenet/metafile.yml
  - configs/classification/meta_baseline/tiered_imagenet/metafile.yml
  - configs/classification/neg_margin/cub/metafile.yml
  - configs/classification/neg_margin/mini_imagenet/metafile.yml
  - configs/classification/neg_margin/tiered_imagenet/metafile.yml
  - configs/classification/proto_net/cub/metafile.yml
  - configs/classification/proto_net/mini_imagenet/metafile.yml
  - configs/classification/proto_net/tiered_imagenet/metafile.yml
  - configs/classification/relation_net/cub/metafile.yml
  - configs/classification/relation_net/mini_imagenet/metafile.yml
  - configs/classification/relation_net/tiered_imagenet/metafile.yml
  - configs/detection/attention_rpn/coco/metafile.yml
  - configs/detection/attention_rpn/voc/split1/metafile.yml
  - configs/detection/attention_rpn/voc/split2/metafile.yml
  - configs/detection/attention_rpn/voc/split3/metafile.yml
  - configs/detection/fsce/coco/metafile.yml
  - configs/detection/fsce/voc/split1/metafile.yml
  - configs/detection/fsce/voc/split2/metafile.yml
  - configs/detection/fsce/voc/split3/metafile.yml
  - configs/detection/fsdetview/coco/metafile.yml
  - configs/detection/fsdetview/voc/split1/metafile.yml
  - configs/detection/fsdetview/voc/split2/metafile.yml
  - configs/detection/fsdetview/voc/split3/metafile.yml
  - configs/detection/meta_rcnn/coco/metafile.yml
  - configs/detection/meta_rcnn/voc/split1/metafile.yml
  - configs/detection/meta_rcnn/voc/split2/metafile.yml
  - configs/detection/meta_rcnn/voc/split3/metafile.yml
  - configs/detection/mpsr/coco/metafile.yml
  - configs/detection/mpsr/voc/split1/metafile.yml
  - configs/detection/mpsr/voc/split2/metafile.yml
  - configs/detection/mpsr/voc/split3/metafile.yml
  - configs/detection/tfa/coco/metafile.yml
  - configs/detection/tfa/voc/split1/metafile.yml
  - configs/detection/tfa/voc/split2/metafile.yml
  - configs/detection/tfa/voc/split3/metafile.yml
