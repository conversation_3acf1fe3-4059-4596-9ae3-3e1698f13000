python tools/detection/test.py configs/detection/st_tfa/isaid/split3/seed0/st-tfa/st-tfa_maskrcnn_r50_isaid-split3_seed0_10shot-fine-tuning.py work_dirs/st-tfa_maskrcnn_r50_isaid-split3_seed0_10shot-fine-tuning/iter_3000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split3/seed0/st-tfa/st-tfa_maskrcnn_r50_isaid-split3_seed0_50shot-fine-tuning.py work_dirs/st-tfa_maskrcnn_r50_isaid-split3_seed0_50shot-fine-tuning/iter_9000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split3/seed0/st-tfa/st-tfa_maskrcnn_r50_isaid-split3_seed0_100shot-fine-tuning.py work_dirs/st-tfa_maskrcnn_r50_isaid-split3_seed0_100shot-fine-tuning/iter_7000.pth --eval='bbox'
