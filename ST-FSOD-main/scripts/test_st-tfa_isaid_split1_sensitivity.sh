# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning.py work_dirs/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning/iter_1000.pth --eval='bbox'
# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning.py work_dirs/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning/iter_8000.pth --eval='bbox'
# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning.py work_dirs/st-tfa_roi-thre60_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning/iter_7000.pth --eval='bbox'
# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning.py work_dirs/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning/iter_1000.pth --eval='bbox'
# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning.py work_dirs/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning/iter_3000.pth --eval='bbox'
# python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning.py work_dirs/st-tfa_roi-thre90_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning/iter_5000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning.py work_dirs/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning/iter_1000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning.py work_dirs/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning/iter_2000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning.py work_dirs/st-tfa_rpn-thre60_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning/iter_5000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning.py work_dirs/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning/iter_1000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning.py work_dirs/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning/iter_2000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning.py work_dirs/st-tfa_rpn-thre90_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning/iter_5000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning.py work_dirs/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_10shot-fine-tuning/iter_1000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning.py work_dirs/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_50shot-fine-tuning/iter_2000.pth --eval='bbox'
python tools/detection/test.py configs/detection/st_tfa/isaid/split1/seed0/st-tfa_sensitivity/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning.py work_dirs/st-tfa_alpha99_maskrcnn_r50_isaid-split1_seed0_100shot-fine-tuning/iter_5000.pth --eval='bbox'
