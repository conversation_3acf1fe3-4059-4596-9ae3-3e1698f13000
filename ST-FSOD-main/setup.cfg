[isort]
line_length = 79
multi_line_output = 0
extra_standard_library = setuptools
known_first_party = rsifewshot
known_third_party = cv2,mmcls,mmcv,mmdet,numpy,pytest,pytorch_sphinx_theme,terminaltables,torch,typing_extensions

no_lines_before = STDLIB,LOCALFOLDER
default_section = THIRDPARTY

[yapf]
BASED_ON_STYLE = pep8
BLANK_LINE_BEFORE_NESTED_CLASS_OR_DEF = true
SPLIT_BEFORE_EXPRESSION_AFTER_OPENING_PAREN = true
