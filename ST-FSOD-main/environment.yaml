name: st_fsod
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - ca-certificates=2024.3.11=h06a4308_0
  - certifi=2022.12.7=py37h06a4308_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - ncurses=6.4=h6a678d5_0
  - openssl=1.1.1w=h7f8727e_0
  - pip=22.3.1=py37h06a4308_0
  - python=3.7.16=h7a1cb2a_0
  - readline=8.2=h5eee18b_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - wheel=0.38.4=py37h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
    - addict==2.4.0
    - aliyun-python-sdk-core==2.15.1
    - aliyun-python-sdk-kms==2.16.3
    - cffi==1.15.1
    - charset-normalizer==3.3.2
    - click==8.1.7
    - colorama==0.4.6
    - crcmod==1.7
    - cryptography==42.0.7
    - cycler==0.11.0
    - docker-pycreds==0.4.0
    - fonttools==4.38.0
    - gitdb==4.0.11
    - gitpython==3.1.43
    - h5py==3.8.0
    - idna==3.7
    - importlib-metadata==6.7.0
    - jmespath==0.10.0
    - kiwisolver==1.4.5
    - markdown==3.4.4
    - markdown-it-py==2.2.0
    - matplotlib==3.5.3
    - mdurl==0.1.2
    - mmcls==0.25.0
    - mmcv-full==1.7.1
    - model-index==0.1.11
    - numpy==1.21.6
    - nvidia-cublas-cu11==**********
    - nvidia-cuda-nvrtc-cu11==11.7.99
    - nvidia-cuda-runtime-cu11==11.7.99
    - nvidia-cudnn-cu11==********
    - opencv-python==********
    - opendatalab==0.0.10
    - openmim==0.3.9
    - openxlab==0.0.10
    - ordered-set==4.1.0
    - oss2==2.17.0
    - packaging==24.0
    - pandas==1.3.5
    - pillow==9.5.0
    - platformdirs==4.0.0
    - portalocker==2.7.0
    - protobuf==4.24.4
    - psutil==5.9.8
    - pycocotools==2.0.7
    - pycparser==2.21
    - pycryptodome==3.20.0
    - pygments==2.17.2
    - pyparsing==3.1.2
    - python-dateutil==2.9.0.post0
    - pytz==2023.4
    - pyyaml==6.0.1
    - requests==2.28.2
    - rich==13.7.1
    - seaborn==0.12.2
    - sentry-sdk==2.2.1
    - setproctitle==1.3.3
    - setuptools==60.2.0
    - six==1.16.0
    - smmap==5.0.1
    - tabulate==0.9.0
    - terminaltables==3.1.10
    - tomli==2.0.1
    - torch==1.13.0
    - torchdata==0.5.0
    - torchvision==0.14.0
    - tqdm==4.65.2
    - typing-extensions==4.7.1
    - urllib3==1.26.18
    - wandb==0.17.0
    - yapf==0.40.1
    - zipp==3.15.0
prefix: /data/fahong/Software/Anaconda/envs/earthnet
