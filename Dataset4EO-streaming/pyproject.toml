[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "StreamingDataset4EO"
version = "0.1.0"
description = "A Python package for Earth Observation datasets."
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "xiong<PERSON><EMAIL>" }
]
license = { file = "LICENSE" }
keywords = ["Earth Observation", "datasets", "remote sensing", "machine learning"]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "torch==2.1.2",
    "torchvision==0.16.2",
    "numpy==1.26.4",
    "torchgeo",
    "litdata==0.2.35",
    "fastparquet",
    "h5py",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/Dataset4EO"
"Source" = "https://github.com/yourusername/Dataset4EO"
"Documentation" = "https://github.com/yourusername/Dataset4EO/docs"

[tool.setuptools.packages.find]
where = ["."]
exclude = ["tests", "datasets"]
