# 数据集路径配置文件

# DIOR数据集路径配置
# 请将此路径修改为你的DIOR数据集实际存放位置
DIOR_DATA_ROOT = "E:/Datasets/DIOR"  # Windows路径示例
# DIOR_DATA_ROOT = "/path/to/your/datasets/DIOR"  # Linux路径示例

# iSAID数据集路径配置  
# 请将此路径修改为你的iSAID数据集实际存放位置
ISAID_DATA_ROOT = "E:/Datasets/iSAID_patches"  # Windows路径示例
# ISAID_DATA_ROOT = "/path/to/your/datasets/iSAID_patches"  # Linux路径示例

# NWPU数据集路径配置
# 请将此路径修改为你的NWPU数据集实际存放位置
NWPU_DATA_ROOT = "E:/Datasets/NWPU-VHR10"  # Windows路径示例
# NWPU_DATA_ROOT = "/path/to/your/datasets/NWPU-VHR10"  # Linux路径示例

# 工作目录配置
WORK_DIR = "./work_dirs"  # 训练输出目录

# GPU配置
GPUS = 1  # 使用的GPU数量
SAMPLES_PER_GPU = 8  # 每个GPU的batch size (根据显存调整)
WORKERS_PER_GPU = 4  # 每个GPU的数据加载进程数

print(f"DIOR数据集路径: {DIOR_DATA_ROOT}")
print(f"iSAID数据集路径: {ISAID_DATA_ROOT}")
print(f"NWPU数据集路径: {NWPU_DATA_ROOT}")
print(f"工作目录: {WORK_DIR}")