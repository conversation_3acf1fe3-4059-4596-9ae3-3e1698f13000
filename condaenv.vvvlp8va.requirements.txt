mmcv-full==1.7.1 -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.0.0/index.html
addict==2.4.0
numpy==1.21.6
matplotlib==3.5.3
opencv-python==4.9.0.80
pillow==9.5.0
pandas==1.3.5
seaborn==0.12.2
mmcls==0.25.0
pycocotools==2.0.7
torchdata==0.5.0
pyyaml==6.0.1
tqdm==4.65.2
terminaltables==3.1.10
tabulate==0.9.0
click==8.1.7
colorama==0.4.6
rich==13.7.1
yapf==0.40.1
wandb==0.17.0
openmim==0.3.9
opendatalab==0.0.10
openxlab==0.0.10
requests==2.28.2
urllib3==1.26.18
psutil==5.9.8
setproctitle==1.3.3
packaging==24.0
setuptools==60.2.0
cryptography==42.0.7
pycryptodome==3.20.0
cffi==1.15.1
pycparser==2.21
six==1.16.0
typing-extensions==4.7.1
python-dateutil==2.9.0.post0
pytz==2023.4
importlib-metadata==6.7.0
zipp==3.15.0
tomli==2.0.1
ordered-set==4.1.0
platformdirs==4.0.0
portalocker==2.7.0
protobuf==4.24.4
aliyun-python-sdk-core==2.15.1
aliyun-python-sdk-kms==2.16.3
oss2==2.17.0
crcmod==1.7
gitpython==3.1.43
gitdb==4.0.11
docker-pycreds==0.4.0
smmap==5.0.1
markdown==3.4.4
markdown-it-py==2.2.0
mdurl==0.1.2
pygments==2.17.2
fonttools==4.38.0
cycler==0.11.0
kiwisolver==1.4.5
pyparsing==3.1.2
h5py==3.8.0
jmespath==0.10.0
charset-normalizer==3.3.2
idna==3.7
model-index==0.1.11
sentry-sdk==2.2.1