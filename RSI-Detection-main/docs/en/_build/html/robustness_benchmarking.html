


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Corruption Benchmarking &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Dataset Preparation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="dataset_prepare.html">Prepare datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Model Zoo</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>Corruption Benchmarking</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/robustness_benchmarking.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="corruption-benchmarking">
<h1>Corruption Benchmarking<a class="headerlink" href="#corruption-benchmarking" title="Permalink to this heading">¶</a></h1>
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h2>
<p>We provide tools to test object detection and instance segmentation models on the image corruption benchmark defined in <a class="reference external" href="https://arxiv.org/abs/1907.07484">Benchmarking Robustness in Object Detection: Autonomous Driving when Winter is Coming</a>.
This page provides basic tutorials how to use the benchmark.</p>
<div class="highlight-latex notranslate"><div class="highlight"><pre><span></span>@article<span class="nb">{</span>michaelis2019winter,
  title=<span class="nb">{</span>Benchmarking Robustness in Object Detection:
    Autonomous Driving when Winter is Coming<span class="nb">}</span>,
  author=<span class="nb">{</span>Michaelis, Claudio and Mitzkus, Benjamin and
    Geirhos, Robert and Rusak, Evgenia and
    Bringmann, Oliver and Ecker, Alexander S. and
    Bethge, Matthias and Brendel, Wieland<span class="nb">}</span>,
  journal=<span class="nb">{</span>arXiv:1907.07484<span class="nb">}</span>,
  year=<span class="nb">{</span>2019<span class="nb">}</span>
<span class="nb">}</span>
</pre></div>
</div>
<p><img alt="image corruption example" src="../resources/corruptions_sev_3.png" /></p>
</section>
<section id="about-the-benchmark">
<h2>About the benchmark<a class="headerlink" href="#about-the-benchmark" title="Permalink to this heading">¶</a></h2>
<p>To submit results to the benchmark please visit the <a class="reference external" href="https://github.com/bethgelab/robust-detection-benchmark">benchmark homepage</a></p>
<p>The benchmark is modelled after the <a class="reference external" href="https://github.com/hendrycks/robustness">imagenet-c benchmark</a> which was originally
published in <a class="reference external" href="https://arxiv.org/abs/1903.12261">Benchmarking Neural Network Robustness to Common Corruptions and Perturbations</a> (ICLR 2019) by Dan Hendrycks and Thomas Dietterich.</p>
<p>The image corruption functions are included in this library but can be installed separately using:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>pip install imagecorruptions
</pre></div>
</div>
<p>Compared to imagenet-c a few changes had to be made to handle images of arbitrary size and greyscale images.
We also modified the ‘motion blur’ and ‘snow’ corruptions to remove dependency from a linux specific library,
which would have to be installed separately otherwise. For details please refer to the <a class="reference external" href="https://github.com/bethgelab/imagecorruptions">imagecorruptions repository</a>.</p>
</section>
<section id="inference-with-pretrained-models">
<h2>Inference with pretrained models<a class="headerlink" href="#inference-with-pretrained-models" title="Permalink to this heading">¶</a></h2>
<p>We provide a testing script to evaluate a models performance on any combination of the corruptions provided in the benchmark.</p>
<section id="test-a-dataset">
<h3>Test a dataset<a class="headerlink" href="#test-a-dataset" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>[x] single GPU testing</p></li>
<li><p>[ ] multiple GPU testing</p></li>
<li><p>[ ] visualize detection results</p></li>
</ul>
<p>You can use the following commands to test a models performance under the 15 corruptions used in the benchmark.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># single-gpu testing</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span>
</pre></div>
</div>
<p>Alternatively different group of corruptions can be selected.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># noise</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --corruptions noise

<span class="c1"># blur</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --corruptions blur

<span class="c1"># wetaher</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --corruptions weather

<span class="c1"># digital</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --corruptions digital
</pre></div>
</div>
<p>Or a costom set of corruptions e.g.:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># gaussian noise, zoom blur and snow</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --corruptions gaussian_noise zoom_blur snow
</pre></div>
</div>
<p>Finally the corruption severities to evaluate can be chosen.
Severity 0 corresponds to clean data and the effect increases from 1 to 5.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># severity 1</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --severities <span class="m">1</span>

<span class="c1"># severities 0,2,4</span>
python tools/analysis_tools/test_robustness.py <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="o">[</span>--out <span class="si">${</span><span class="nv">RESULT_FILE</span><span class="si">}</span><span class="o">]</span> <span class="o">[</span>--eval <span class="si">${</span><span class="nv">EVAL_METRICS</span><span class="si">}</span><span class="o">]</span> --severities <span class="m">0</span> <span class="m">2</span> <span class="m">4</span>
</pre></div>
</div>
</section>
</section>
<section id="results-for-modelzoo-models">
<h2>Results for modelzoo models<a class="headerlink" href="#results-for-modelzoo-models" title="Permalink to this heading">¶</a></h2>
<p>The results on COCO 2017val are shown in the below table.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th style="text-align: center;">Model</th>
<th style="text-align: center;">Backbone</th>
<th style="text-align: center;">Style</th>
<th style="text-align: center;">Lr schd</th>
<th style="text-align: center;">box AP clean</th>
<th style="text-align: center;">box AP corr.</th>
<th style="text-align: center;">box %</th>
<th style="text-align: center;">mask AP clean</th>
<th style="text-align: center;">mask AP corr.</th>
<th style="text-align: center;">mask %</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">R-50-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">36.3</td>
<td style="text-align: center;">18.2</td>
<td style="text-align: center;">50.2</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">R-101-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">38.5</td>
<td style="text-align: center;">20.9</td>
<td style="text-align: center;">54.2</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">X-101-32x4d-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">40.1</td>
<td style="text-align: center;">22.3</td>
<td style="text-align: center;">55.5</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">X-101-64x4d-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">41.3</td>
<td style="text-align: center;">23.4</td>
<td style="text-align: center;">56.6</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">R-50-FPN-DCN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">40.0</td>
<td style="text-align: center;">22.4</td>
<td style="text-align: center;">56.1</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;">X-101-32x4d-FPN-DCN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">43.4</td>
<td style="text-align: center;">26.7</td>
<td style="text-align: center;">61.6</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Mask R-CNN</td>
<td style="text-align: center;">R-50-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">37.3</td>
<td style="text-align: center;">18.7</td>
<td style="text-align: center;">50.1</td>
<td style="text-align: center;">34.2</td>
<td style="text-align: center;">16.8</td>
<td style="text-align: center;">49.1</td>
</tr>
<tr>
<td style="text-align: center;">Mask R-CNN</td>
<td style="text-align: center;">R-50-FPN-DCN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">41.1</td>
<td style="text-align: center;">23.3</td>
<td style="text-align: center;">56.7</td>
<td style="text-align: center;">37.2</td>
<td style="text-align: center;">20.7</td>
<td style="text-align: center;">55.7</td>
</tr>
<tr>
<td style="text-align: center;">Cascade R-CNN</td>
<td style="text-align: center;">R-50-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">40.4</td>
<td style="text-align: center;">20.1</td>
<td style="text-align: center;">49.7</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Cascade Mask R-CNN</td>
<td style="text-align: center;">R-50-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">41.2</td>
<td style="text-align: center;">20.7</td>
<td style="text-align: center;">50.2</td>
<td style="text-align: center;">35.7</td>
<td style="text-align: center;">17.6</td>
<td style="text-align: center;">49.3</td>
</tr>
<tr>
<td style="text-align: center;">RetinaNet</td>
<td style="text-align: center;">R-50-FPN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">35.6</td>
<td style="text-align: center;">17.8</td>
<td style="text-align: center;">50.1</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
<td style="text-align: center;">-</td>
</tr>
<tr>
<td style="text-align: center;">Hybrid Task Cascade</td>
<td style="text-align: center;">X-101-64x4d-FPN-DCN</td>
<td style="text-align: center;">pytorch</td>
<td style="text-align: center;">1x</td>
<td style="text-align: center;">50.6</td>
<td style="text-align: center;">32.7</td>
<td style="text-align: center;">64.7</td>
<td style="text-align: center;">43.8</td>
<td style="text-align: center;">28.1</td>
<td style="text-align: center;">64.0</td>
</tr>
</tbody>
</table>
<p>Results may vary slightly due to the stochastic application of the corruptions.</p>
</section>
</section>


              </article>
              
            </div>
            <footer>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Corruption Benchmarking</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#about-the-benchmark">About the benchmark</a></li>
<li><a class="reference internal" href="#inference-with-pretrained-models">Inference with pretrained models</a><ul>
<li><a class="reference internal" href="#test-a-dataset">Test a dataset</a></li>
</ul>
</li>
<li><a class="reference internal" href="#results-for-modelzoo-models">Results for modelzoo models</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>