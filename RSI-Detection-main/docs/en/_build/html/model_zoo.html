


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Benchmark and Model Zoo &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="1: Inference and train with existing models and standard datasets" href="1_exist_data_model.html" />
  <link rel="prev" title="Prerequisites" href="get_started.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>Benchmark and Model Zoo</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/model_zoo.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="benchmark-and-model-zoo">
<h1>Benchmark and Model Zoo<a class="headerlink" href="#benchmark-and-model-zoo" title="Permalink to this heading">¶</a></h1>
<section id="mirror-sites">
<h2>Mirror sites<a class="headerlink" href="#mirror-sites" title="Permalink to this heading">¶</a></h2>
<p>We only use aliyun to maintain the model zoo since MMDetection V2.0. The model zoo of V1.x has been deprecated.</p>
</section>
<section id="common-settings">
<h2>Common settings<a class="headerlink" href="#common-settings" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>All models were trained on <code class="docutils literal notranslate"><span class="pre">coco_2017_train</span></code>, and tested on the <code class="docutils literal notranslate"><span class="pre">coco_2017_val</span></code>.</p></li>
<li><p>We use distributed training.</p></li>
<li><p>All pytorch-style pretrained backbones on ImageNet are from PyTorch model zoo, caffe-style pretrained backbones are converted from the newly released model from detectron2.</p></li>
<li><p>For fair comparison with other codebases, we report the GPU memory as the maximum value of <code class="docutils literal notranslate"><span class="pre">torch.cuda.max_memory_allocated()</span></code> for all 8 GPUs. Note that this value is usually less than what <code class="docutils literal notranslate"><span class="pre">nvidia-smi</span></code> shows.</p></li>
<li><p>We report the inference time as the total time of network forwarding and post-processing, excluding the data loading time. Results are obtained with the script <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/analysis_tools/benchmark.py">benchmark.py</a> which computes the average time on 2000 images.</p></li>
</ul>
</section>
<section id="imagenet-pretrained-models">
<h2>ImageNet Pretrained Models<a class="headerlink" href="#imagenet-pretrained-models" title="Permalink to this heading">¶</a></h2>
<p>It is common to initialize from backbone models pre-trained on ImageNet classification task. All pre-trained  model links can be found at <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/mmcv/model_zoo/open_mmlab.json">open_mmlab</a>.  According to <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> and source of weight, we can divide all the ImageNet  pre-trained  model weights into some cases:</p>
<ul class="simple">
<li><p>TorchVision:  Corresponding to torchvision weight, including ResNet50, ResNet101. The <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(mean=[123.675,</span> <span class="pre">116.28,</span> <span class="pre">103.53],</span> <span class="pre">std=[58.395,</span> <span class="pre">57.12,</span> <span class="pre">57.375],</span> <span class="pre">to_rgb=True)</span></code>.</p></li>
<li><p>Pycls:  Corresponding to <a class="reference external" href="https://github.com/facebookresearch/pycls">pycls</a> weight, including RegNetX. The <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(</span>&#160;&#160; <span class="pre">mean=[103.530,</span> <span class="pre">116.280,</span> <span class="pre">123.675],</span> <span class="pre">std=[57.375,</span> <span class="pre">57.12,</span> <span class="pre">58.395],</span> <span class="pre">to_rgb=False)</span></code>.</p></li>
<li><p>MSRA styles: Corresponding to <a class="reference external" href="https://github.com/KaimingHe/deep-residual-networks">MSRA</a> weights, including ResNet50_Caffe and ResNet101_Caffe. The <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(</span>&#160;&#160; <span class="pre">mean=[103.530,</span> <span class="pre">116.280,</span> <span class="pre">123.675],</span> <span class="pre">std=[1.0,</span> <span class="pre">1.0,</span> <span class="pre">1.0],</span> <span class="pre">to_rgb=False)</span></code>.</p></li>
<li><p>Caffe2 styles:  Currently only contains ResNext101_32x8d. The <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(mean=[103.530,</span> <span class="pre">116.280,</span> <span class="pre">123.675],</span> <span class="pre">std=[57.375,</span> <span class="pre">57.120,</span> <span class="pre">58.395],</span> <span class="pre">to_rgb=False)</span></code>.</p></li>
<li><p>Other styles: E.g SSD which corresponds to <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(mean=[123.675,</span> <span class="pre">116.28,</span> <span class="pre">103.53],</span> <span class="pre">std=[1,</span> <span class="pre">1,</span> <span class="pre">1],</span> <span class="pre">to_rgb=True)</span></code> and YOLOv3 which corresponds to <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> is <code class="docutils literal notranslate"><span class="pre">dict(mean=[0,</span> <span class="pre">0,</span> <span class="pre">0],</span> <span class="pre">std=[255.,</span> <span class="pre">255.,</span> <span class="pre">255.],</span> <span class="pre">to_rgb=True)</span></code>.</p></li>
</ul>
<p>The detailed table of the commonly used backbone models in MMDetection is listed below :</p>
<table border="1" class="docutils">
<thead>
<tr>
<th>model</th>
<th>source</th>
<th>link</th>
<th>description</th>
</tr>
</thead>
<tbody>
<tr>
<td>ResNet50</td>
<td>TorchVision</td>
<td><a href="https://download.pytorch.org/models/resnet50-19c8e357.pth">torchvision's ResNet-50</a></td>
<td>From <a href="https://download.pytorch.org/models/resnet50-19c8e357.pth">torchvision's ResNet-50</a>.</td>
</tr>
<tr>
<td>ResNet101</td>
<td>TorchVision</td>
<td><a href="https://download.pytorch.org/models/resnet101-5d3b4d8f.pth">torchvision's ResNet-101</a></td>
<td>From <a href="https://download.pytorch.org/models/resnet101-5d3b4d8f.pth">torchvision's ResNet-101</a>.</td>
</tr>
<tr>
<td>RegNetX</td>
<td>Pycls</td>
<td><a href="https://download.openmmlab.com/pretrain/third_party/regnetx_3.2gf-c2599b0f.pth">RegNetX_3.2gf</a>, <a href="https://download.openmmlab.com/pretrain/third_party/regnetx_800mf-1f4be4c7.pth">RegNetX_800mf</a>. etc.</td>
<td>From <a href="https://github.com/facebookresearch/pycls">pycls</a>.</td>
</tr>
<tr>
<td>ResNet50_Caffe</td>
<td>MSRA</td>
<td><a href="https://download.openmmlab.com/pretrain/third_party/resnet50_caffe-788b5fa3.pth">MSRA's ResNet-50</a></td>
<td>Converted copy of <a href="https://dl.fbaipublicfiles.com/detectron2/ImageNetPretrained/MSRA/R-50.pkl">Detectron2's R-50.pkl</a> model. The original weight comes from <a href="https://github.com/KaimingHe/deep-residual-networks">MSRA's original ResNet-50</a>.</td>
</tr>
<tr>
<td>ResNet101_Caffe</td>
<td>MSRA</td>
<td><a href="https://download.openmmlab.com/pretrain/third_party/resnet101_caffe-3ad79236.pth">MSRA's ResNet-101</a></td>
<td>Converted copy of <a href="https://dl.fbaipublicfiles.com/detectron2/ImageNetPretrained/MSRA/R-101.pkl">Detectron2's R-101.pkl</a> model. The original weight comes from <a href="https://github.com/KaimingHe/deep-residual-networks">MSRA's original ResNet-101</a>.</td>
</tr>
<tr>
<td>ResNext101_32x8d</td>
<td>Caffe2</td>
<td><a href="https://download.openmmlab.com/pretrain/third_party/resnext101_32x8d-1516f1aa.pth">Caffe2 ResNext101_32x8d</a></td>
<td>Converted copy of <a href="https://dl.fbaipublicfiles.com/detectron2/ImageNetPretrained/FAIR/X-101-32x8d.pkl">Detectron2's X-101-32x8d.pkl</a> model. The ResNeXt-101-32x8d model trained with Caffe2 at FB.</td>
</tr>
</tbody>
</table>
</section>
<section id="baselines">
<h2>Baselines<a class="headerlink" href="#baselines" title="Permalink to this heading">¶</a></h2>
<section id="rpn">
<h3>RPN<a class="headerlink" href="#rpn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/rpn">RPN</a> for details.</p>
</section>
<section id="faster-r-cnn">
<h3>Faster R-CNN<a class="headerlink" href="#faster-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/faster_rcnn">Faster R-CNN</a> for details.</p>
</section>
<section id="mask-r-cnn">
<h3>Mask R-CNN<a class="headerlink" href="#mask-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/mask_rcnn">Mask R-CNN</a> for details.</p>
</section>
<section id="fast-r-cnn-with-pre-computed-proposals">
<h3>Fast R-CNN (with pre-computed proposals)<a class="headerlink" href="#fast-r-cnn-with-pre-computed-proposals" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/fast_rcnn">Fast R-CNN</a> for details.</p>
</section>
<section id="retinanet">
<h3>RetinaNet<a class="headerlink" href="#retinanet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/retinanet">RetinaNet</a> for details.</p>
</section>
<section id="cascade-r-cnn-and-cascade-mask-r-cnn">
<h3>Cascade R-CNN and Cascade Mask R-CNN<a class="headerlink" href="#cascade-r-cnn-and-cascade-mask-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/cascade_rcnn">Cascade R-CNN</a> for details.</p>
</section>
<section id="hybrid-task-cascade-htc">
<h3>Hybrid Task Cascade (HTC)<a class="headerlink" href="#hybrid-task-cascade-htc" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/htc">HTC</a> for details.</p>
</section>
<section id="ssd">
<h3>SSD<a class="headerlink" href="#ssd" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/ssd">SSD</a> for details.</p>
</section>
<section id="group-normalization-gn">
<h3>Group Normalization (GN)<a class="headerlink" href="#group-normalization-gn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/gn">Group Normalization</a> for details.</p>
</section>
<section id="weight-standardization">
<h3>Weight Standardization<a class="headerlink" href="#weight-standardization" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/gn+ws">Weight Standardization</a> for details.</p>
</section>
<section id="deformable-convolution-v2">
<h3>Deformable Convolution v2<a class="headerlink" href="#deformable-convolution-v2" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/dcn">Deformable Convolutional Networks</a> for details.</p>
</section>
<section id="carafe-content-aware-reassembly-of-features">
<h3>CARAFE: Content-Aware ReAssembly of FEatures<a class="headerlink" href="#carafe-content-aware-reassembly-of-features" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/carafe">CARAFE</a> for details.</p>
</section>
<section id="instaboost">
<h3>Instaboost<a class="headerlink" href="#instaboost" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/instaboost">Instaboost</a> for details.</p>
</section>
<section id="libra-r-cnn">
<h3>Libra R-CNN<a class="headerlink" href="#libra-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/libra_rcnn">Libra R-CNN</a> for details.</p>
</section>
<section id="guided-anchoring">
<h3>Guided Anchoring<a class="headerlink" href="#guided-anchoring" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/guided_anchoring">Guided Anchoring</a> for details.</p>
</section>
<section id="fcos">
<h3>FCOS<a class="headerlink" href="#fcos" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/fcos">FCOS</a> for details.</p>
</section>
<section id="foveabox">
<h3>FoveaBox<a class="headerlink" href="#foveabox" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/foveabox">FoveaBox</a> for details.</p>
</section>
<section id="reppoints">
<h3>RepPoints<a class="headerlink" href="#reppoints" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/reppoints">RepPoints</a> for details.</p>
</section>
<section id="freeanchor">
<h3>FreeAnchor<a class="headerlink" href="#freeanchor" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/free_anchor">FreeAnchor</a> for details.</p>
</section>
<section id="grid-r-cnn-plus">
<h3>Grid R-CNN (plus)<a class="headerlink" href="#grid-r-cnn-plus" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/grid_rcnn">Grid R-CNN</a> for details.</p>
</section>
<section id="ghm">
<h3>GHM<a class="headerlink" href="#ghm" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/ghm">GHM</a> for details.</p>
</section>
<section id="gcnet">
<h3>GCNet<a class="headerlink" href="#gcnet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/gcnet">GCNet</a> for details.</p>
</section>
<section id="hrnet">
<h3>HRNet<a class="headerlink" href="#hrnet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/hrnet">HRNet</a> for details.</p>
</section>
<section id="mask-scoring-r-cnn">
<h3>Mask Scoring R-CNN<a class="headerlink" href="#mask-scoring-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/ms_rcnn">Mask Scoring R-CNN</a> for details.</p>
</section>
<section id="train-from-scratch">
<h3>Train from Scratch<a class="headerlink" href="#train-from-scratch" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/scratch">Rethinking ImageNet Pre-training</a> for details.</p>
</section>
<section id="nas-fpn">
<h3>NAS-FPN<a class="headerlink" href="#nas-fpn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/nas_fpn">NAS-FPN</a> for details.</p>
</section>
<section id="atss">
<h3>ATSS<a class="headerlink" href="#atss" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/atss">ATSS</a> for details.</p>
</section>
<section id="fsaf">
<h3>FSAF<a class="headerlink" href="#fsaf" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/fsaf">FSAF</a> for details.</p>
</section>
<section id="regnetx">
<h3>RegNetX<a class="headerlink" href="#regnetx" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/regnet">RegNet</a> for details.</p>
</section>
<section id="res2net">
<h3>Res2Net<a class="headerlink" href="#res2net" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/res2net">Res2Net</a> for details.</p>
</section>
<section id="groie">
<h3>GRoIE<a class="headerlink" href="#groie" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/groie">GRoIE</a> for details.</p>
</section>
<section id="dynamic-r-cnn">
<h3>Dynamic R-CNN<a class="headerlink" href="#dynamic-r-cnn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/dynamic_rcnn">Dynamic R-CNN</a> for details.</p>
</section>
<section id="pointrend">
<h3>PointRend<a class="headerlink" href="#pointrend" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/point_rend">PointRend</a> for details.</p>
</section>
<section id="detectors">
<h3>DetectoRS<a class="headerlink" href="#detectors" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/detectors">DetectoRS</a> for details.</p>
</section>
<section id="generalized-focal-loss">
<h3>Generalized Focal Loss<a class="headerlink" href="#generalized-focal-loss" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/gfl">Generalized Focal Loss</a> for details.</p>
</section>
<section id="cornernet">
<h3>CornerNet<a class="headerlink" href="#cornernet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/cornernet">CornerNet</a> for details.</p>
</section>
<section id="yolov3">
<h3>YOLOv3<a class="headerlink" href="#yolov3" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/yolo">YOLOv3</a> for details.</p>
</section>
<section id="paa">
<h3>PAA<a class="headerlink" href="#paa" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/paa">PAA</a> for details.</p>
</section>
<section id="sabl">
<h3>SABL<a class="headerlink" href="#sabl" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/sabl">SABL</a> for details.</p>
</section>
<section id="centripetalnet">
<h3>CentripetalNet<a class="headerlink" href="#centripetalnet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/centripetalnet">CentripetalNet</a> for details.</p>
</section>
<section id="resnest">
<h3>ResNeSt<a class="headerlink" href="#resnest" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/resnest">ResNeSt</a> for details.</p>
</section>
<section id="detr">
<h3>DETR<a class="headerlink" href="#detr" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/detr">DETR</a> for details.</p>
</section>
<section id="deformable-detr">
<h3>Deformable DETR<a class="headerlink" href="#deformable-detr" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/deformable_detr">Deformable DETR</a> for details.</p>
</section>
<section id="autoassign">
<h3>AutoAssign<a class="headerlink" href="#autoassign" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/autoassign">AutoAssign</a> for details.</p>
</section>
<section id="yolof">
<h3>YOLOF<a class="headerlink" href="#yolof" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/yolof">YOLOF</a> for details.</p>
</section>
<section id="seesaw-loss">
<h3>Seesaw Loss<a class="headerlink" href="#seesaw-loss" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/seesaw_loss">Seesaw Loss</a> for details.</p>
</section>
<section id="centernet">
<h3>CenterNet<a class="headerlink" href="#centernet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/centernet">CenterNet</a> for details.</p>
</section>
<section id="yolox">
<h3>YOLOX<a class="headerlink" href="#yolox" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/yolox">YOLOX</a> for details.</p>
</section>
<section id="pvt">
<h3>PVT<a class="headerlink" href="#pvt" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/pvt">PVT</a> for details.</p>
</section>
<section id="solo">
<h3>SOLO<a class="headerlink" href="#solo" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/solo">SOLO</a> for details.</p>
</section>
<section id="queryinst">
<h3>QueryInst<a class="headerlink" href="#queryinst" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/queryinst">QueryInst</a> for details.</p>
</section>
<section id="panopticfpn">
<h3>PanopticFPN<a class="headerlink" href="#panopticfpn" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/panoptic_fpn">PanopticFPN</a> for details.</p>
</section>
<section id="maskformer">
<h3>MaskFormer<a class="headerlink" href="#maskformer" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/maskformer">MaskFormer</a> for details.</p>
</section>
<section id="dyhead">
<h3>DyHead<a class="headerlink" href="#dyhead" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/dyhead">DyHead</a> for details.</p>
</section>
<section id="mask2former">
<h3>Mask2Former<a class="headerlink" href="#mask2former" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/mask2former">Mask2Former</a> for details.</p>
</section>
<section id="efficientnet">
<h3>Efficientnet<a class="headerlink" href="#efficientnet" title="Permalink to this heading">¶</a></h3>
<p>Please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/efficientnet">Efficientnet</a> for details.</p>
</section>
<section id="other-datasets">
<h3>Other datasets<a class="headerlink" href="#other-datasets" title="Permalink to this heading">¶</a></h3>
<p>We also benchmark some methods on <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/pascal_voc">PASCAL VOC</a>, <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/cityscapes">Cityscapes</a>, <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/openimages">OpenImages</a> and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/wider_face">WIDER FACE</a>.</p>
</section>
<section id="pre-trained-models">
<h3>Pre-trained Models<a class="headerlink" href="#pre-trained-models" title="Permalink to this heading">¶</a></h3>
<p>We also train <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/faster_rcnn">Faster R-CNN</a> and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/mask_rcnn">Mask R-CNN</a> using ResNet-50 and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/regnet">RegNetX-3.2G</a> with multi-scale training and longer schedules. These models serve as strong pre-trained models for downstream tasks for convenience.</p>
</section>
</section>
<section id="speed-benchmark">
<h2>Speed benchmark<a class="headerlink" href="#speed-benchmark" title="Permalink to this heading">¶</a></h2>
<section id="training-speed-benchmark">
<h3>Training Speed benchmark<a class="headerlink" href="#training-speed-benchmark" title="Permalink to this heading">¶</a></h3>
<p>We provide <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/analysis_tools/analyze_logs.py">analyze_logs.py</a> to get average time of iteration in training. You can find examples in <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/useful_tools.html#log-analysis">Log Analysis</a>.</p>
<p>We compare the training speed of Mask R-CNN with some other popular frameworks (The data is copied from <a class="reference external" href="https://github.com/facebookresearch/detectron2/blob/master/docs/notes/benchmarks.md/">detectron2</a>).
For rsidetection, we benchmark with <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/mask_rcnn/mask_rcnn_r50_caffe_fpn_poly_1x_coco_v1.py">mask_rcnn_r50_caffe_fpn_poly_1x_coco_v1.py</a>, which should have the same setting with <a class="reference external" href="https://github.com/facebookresearch/detectron2/blob/master/configs/Detectron1-Comparisons/mask_rcnn_R_50_FPN_noaug_1x.yaml">mask_rcnn_R_50_FPN_noaug_1x.yaml</a> of detectron2.
We also provide the <a class="reference external" href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/mask_rcnn_r50_caffe_fpn_poly_1x_coco_no_aug/mask_rcnn_r50_caffe_fpn_poly_1x_coco_no_aug_compare_20200518-10127928.pth">checkpoint</a> and <a class="reference external" href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/mask_rcnn_r50_caffe_fpn_poly_1x_coco_no_aug/mask_rcnn_r50_caffe_fpn_poly_1x_coco_no_aug_20200518_105755.log.json">training log</a> for reference. The throughput is computed as the average throughput in iterations 100-500 to skip GPU warmup time.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th>Implementation</th>
<th>Throughput (img/s)</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="https://github.com/facebookresearch/detectron2">Detectron2</a></td>
<td>62</td>
</tr>
<tr>
<td><a href="https://github.com/open-mmlab/rsidetection">MMDetection</a></td>
<td>61</td>
</tr>
<tr>
<td><a href="https://github.com/facebookresearch/maskrcnn-benchmark/">maskrcnn-benchmark</a></td>
<td>53</td>
</tr>
<tr>
<td><a href="https://github.com/tensorpack/tensorpack/tree/master/examples/FasterRCNN">tensorpack</a></td>
<td>50</td>
</tr>
<tr>
<td><a href="https://github.com/TuSimple/simpledet/">simpledet</a></td>
<td>39</td>
</tr>
<tr>
<td><a href="https://github.com/facebookresearch/Detectron">Detectron</a></td>
<td>19</td>
</tr>
<tr>
<td><a href="https://github.com/matterport/Mask_RCNN/">matterport/Mask_RCNN</a></td>
<td>14</td>
</tr>
</tbody>
</table>
</section>
<section id="inference-speed-benchmark">
<h3>Inference Speed Benchmark<a class="headerlink" href="#inference-speed-benchmark" title="Permalink to this heading">¶</a></h3>
<p>We provide <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/analysis_tools/benchmark.py">benchmark.py</a> to benchmark the inference latency.
The script benchmarkes the model with 2000 images and calculates the average time ignoring first 5 times. You can change the output log interval (defaults: 50) by setting <code class="docutils literal notranslate"><span class="pre">LOG-INTERVAL</span></code>.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/benchmark.py <span class="si">${</span><span class="nv">CONFIG</span><span class="si">}</span> <span class="si">${</span><span class="nv">CHECKPOINT</span><span class="si">}</span> <span class="o">[</span>--log-interval $<span class="o">[</span>LOG-INTERVAL<span class="o">]]</span> <span class="o">[</span>--fuse-conv-bn<span class="o">]</span>
</pre></div>
</div>
<p>The latency of all models in our model zoo is benchmarked without setting <code class="docutils literal notranslate"><span class="pre">fuse-conv-bn</span></code>, you can get a lower latency by setting it.</p>
</section>
</section>
<section id="comparison-with-detectron2">
<h2>Comparison with Detectron2<a class="headerlink" href="#comparison-with-detectron2" title="Permalink to this heading">¶</a></h2>
<p>We compare rsidetection with <a class="reference external" href="https://github.com/facebookresearch/detectron2.git">Detectron2</a> in terms of speed and performance.
We use the commit id <a class="reference external" href="https://github.com/facebookresearch/detectron2/tree/185c27e4b4d2d4c68b5627b3765420c6d7f5a659">185c27e</a>(30/4/2020) of detectron.
For fair comparison, we install and run both frameworks on the same machine.</p>
<section id="hardware">
<h3>Hardware<a class="headerlink" href="#hardware" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>8 NVIDIA Tesla V100 (32G) GPUs</p></li>
<li><p>Intel(R) Xeon(R) Gold 6148 CPU &#64; 2.40GHz</p></li>
</ul>
</section>
<section id="software-environment">
<h3>Software environment<a class="headerlink" href="#software-environment" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Python 3.7</p></li>
<li><p>PyTorch 1.4</p></li>
<li><p>CUDA 10.1</p></li>
<li><p>CUDNN 7.6.03</p></li>
<li><p>NCCL 2.4.08</p></li>
</ul>
</section>
<section id="performance">
<h3>Performance<a class="headerlink" href="#performance" title="Permalink to this heading">¶</a></h3>
<table border="1" class="docutils">
<thead>
<tr>
<th>Type</th>
<th>Lr schd</th>
<th>Detectron2</th>
<th>rsidetection</th>
<th>Download</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="https://github.com/open-mmlab/rsidetection/blob/master/configs/faster_rcnn/faster_rcnn_r50_caffe_fpn_mstrain_1x_coco.py">Faster R-CNN</a></td>
<td>1x</td>
<td><a href="https://github.com/facebookresearch/detectron2/blob/master/configs/COCO-Detection/faster_rcnn_R_50_FPN_1x.yaml">37.9</a></td>
<td>38.0</td>
<td><a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/faster_rcnn_r50_caffe_fpn_mstrain_1x_coco/faster_rcnn_r50_caffe_fpn_mstrain_1x_coco-5324cff8.pth">model</a> | <a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/faster_rcnn_r50_caffe_fpn_mstrain_1x_coco/faster_rcnn_r50_caffe_fpn_mstrain_1x_coco_20200429_234554.log.json">log</a></td>
</tr>
<tr>
<td><a href="https://github.com/open-mmlab/rsidetection/blob/master/configs/mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco.py">Mask R-CNN</a></td>
<td>1x</td>
<td><a href="https://github.com/facebookresearch/detectron2/blob/master/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_1x.yaml">38.6 &amp; 35.2</a></td>
<td>38.8 &amp; 35.4</td>
<td><a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco-dbecf295.pth">model</a> | <a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_coco_20200430_054239.log.json">log</a></td>
</tr>
<tr>
<td><a href="https://github.com/open-mmlab/rsidetection/blob/master/configs/retinanet/retinanet_r50_caffe_fpn_mstrain_1x_coco.py">Retinanet</a></td>
<td>1x</td>
<td><a href="https://github.com/facebookresearch/detectron2/blob/master/configs/COCO-Detection/retinanet_R_50_FPN_1x.yaml">36.5</a></td>
<td>37.0</td>
<td><a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/retinanet_r50_caffe_fpn_mstrain_1x_coco/retinanet_r50_caffe_fpn_mstrain_1x_coco-586977a0.pth">model</a> | <a href="https://download.openmmlab.com/rsidetection/v2.0/benchmark/retinanet_r50_caffe_fpn_mstrain_1x_coco/retinanet_r50_caffe_fpn_mstrain_1x_coco_20200430_014748.log.json">log</a></td>
</tr>
</tbody>
</table>
</section>
<section id="training-speed">
<h3>Training Speed<a class="headerlink" href="#training-speed" title="Permalink to this heading">¶</a></h3>
<p>The training speed is measure with s/iter. The lower, the better.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th>Type</th>
<th>Detectron2</th>
<th>rsidetection</th>
</tr>
</thead>
<tbody>
<tr>
<td>Faster R-CNN</td>
<td>0.210</td>
<td>0.216</td>
</tr>
<tr>
<td>Mask R-CNN</td>
<td>0.261</td>
<td>0.265</td>
</tr>
<tr>
<td>Retinanet</td>
<td>0.200</td>
<td>0.205</td>
</tr>
</tbody>
</table>
</section>
<section id="inference-speed">
<h3>Inference Speed<a class="headerlink" href="#inference-speed" title="Permalink to this heading">¶</a></h3>
<p>The inference speed is measured with fps (img/s) on a single GPU, the higher, the better.
To be consistent with Detectron2, we report the pure inference speed (without the time of data loading).
For Mask R-CNN, we exclude the time of RLE encoding in post-processing.
We also include the officially reported speed in the parentheses, which is slightly higher
than the results tested on our server due to differences of hardwares.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th>Type</th>
<th>Detectron2</th>
<th>rsidetection</th>
</tr>
</thead>
<tbody>
<tr>
<td>Faster R-CNN</td>
<td>25.6 (26.3)</td>
<td>22.2</td>
</tr>
<tr>
<td>Mask R-CNN</td>
<td>22.5 (23.3)</td>
<td>19.6</td>
</tr>
<tr>
<td>Retinanet</td>
<td>17.8 (18.2)</td>
<td>20.6</td>
</tr>
</tbody>
</table>
</section>
<section id="training-memory">
<h3>Training memory<a class="headerlink" href="#training-memory" title="Permalink to this heading">¶</a></h3>
<table border="1" class="docutils">
<thead>
<tr>
<th>Type</th>
<th>Detectron2</th>
<th>rsidetection</th>
</tr>
</thead>
<tbody>
<tr>
<td>Faster R-CNN</td>
<td>3.0</td>
<td>3.8</td>
</tr>
<tr>
<td>Mask R-CNN</td>
<td>3.4</td>
<td>3.9</td>
</tr>
<tr>
<td>Retinanet</td>
<td>3.9</td>
<td>3.4</td>
</tr>
</tbody>
</table></section>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="1_exist_data_model.html" class="btn btn-neutral float-right" title="1: Inference and train with existing models and standard datasets" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="get_started.html" class="btn btn-neutral" title="Prerequisites" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Benchmark and Model Zoo</a><ul>
<li><a class="reference internal" href="#mirror-sites">Mirror sites</a></li>
<li><a class="reference internal" href="#common-settings">Common settings</a></li>
<li><a class="reference internal" href="#imagenet-pretrained-models">ImageNet Pretrained Models</a></li>
<li><a class="reference internal" href="#baselines">Baselines</a><ul>
<li><a class="reference internal" href="#rpn">RPN</a></li>
<li><a class="reference internal" href="#faster-r-cnn">Faster R-CNN</a></li>
<li><a class="reference internal" href="#mask-r-cnn">Mask R-CNN</a></li>
<li><a class="reference internal" href="#fast-r-cnn-with-pre-computed-proposals">Fast R-CNN (with pre-computed proposals)</a></li>
<li><a class="reference internal" href="#retinanet">RetinaNet</a></li>
<li><a class="reference internal" href="#cascade-r-cnn-and-cascade-mask-r-cnn">Cascade R-CNN and Cascade Mask R-CNN</a></li>
<li><a class="reference internal" href="#hybrid-task-cascade-htc">Hybrid Task Cascade (HTC)</a></li>
<li><a class="reference internal" href="#ssd">SSD</a></li>
<li><a class="reference internal" href="#group-normalization-gn">Group Normalization (GN)</a></li>
<li><a class="reference internal" href="#weight-standardization">Weight Standardization</a></li>
<li><a class="reference internal" href="#deformable-convolution-v2">Deformable Convolution v2</a></li>
<li><a class="reference internal" href="#carafe-content-aware-reassembly-of-features">CARAFE: Content-Aware ReAssembly of FEatures</a></li>
<li><a class="reference internal" href="#instaboost">Instaboost</a></li>
<li><a class="reference internal" href="#libra-r-cnn">Libra R-CNN</a></li>
<li><a class="reference internal" href="#guided-anchoring">Guided Anchoring</a></li>
<li><a class="reference internal" href="#fcos">FCOS</a></li>
<li><a class="reference internal" href="#foveabox">FoveaBox</a></li>
<li><a class="reference internal" href="#reppoints">RepPoints</a></li>
<li><a class="reference internal" href="#freeanchor">FreeAnchor</a></li>
<li><a class="reference internal" href="#grid-r-cnn-plus">Grid R-CNN (plus)</a></li>
<li><a class="reference internal" href="#ghm">GHM</a></li>
<li><a class="reference internal" href="#gcnet">GCNet</a></li>
<li><a class="reference internal" href="#hrnet">HRNet</a></li>
<li><a class="reference internal" href="#mask-scoring-r-cnn">Mask Scoring R-CNN</a></li>
<li><a class="reference internal" href="#train-from-scratch">Train from Scratch</a></li>
<li><a class="reference internal" href="#nas-fpn">NAS-FPN</a></li>
<li><a class="reference internal" href="#atss">ATSS</a></li>
<li><a class="reference internal" href="#fsaf">FSAF</a></li>
<li><a class="reference internal" href="#regnetx">RegNetX</a></li>
<li><a class="reference internal" href="#res2net">Res2Net</a></li>
<li><a class="reference internal" href="#groie">GRoIE</a></li>
<li><a class="reference internal" href="#dynamic-r-cnn">Dynamic R-CNN</a></li>
<li><a class="reference internal" href="#pointrend">PointRend</a></li>
<li><a class="reference internal" href="#detectors">DetectoRS</a></li>
<li><a class="reference internal" href="#generalized-focal-loss">Generalized Focal Loss</a></li>
<li><a class="reference internal" href="#cornernet">CornerNet</a></li>
<li><a class="reference internal" href="#yolov3">YOLOv3</a></li>
<li><a class="reference internal" href="#paa">PAA</a></li>
<li><a class="reference internal" href="#sabl">SABL</a></li>
<li><a class="reference internal" href="#centripetalnet">CentripetalNet</a></li>
<li><a class="reference internal" href="#resnest">ResNeSt</a></li>
<li><a class="reference internal" href="#detr">DETR</a></li>
<li><a class="reference internal" href="#deformable-detr">Deformable DETR</a></li>
<li><a class="reference internal" href="#autoassign">AutoAssign</a></li>
<li><a class="reference internal" href="#yolof">YOLOF</a></li>
<li><a class="reference internal" href="#seesaw-loss">Seesaw Loss</a></li>
<li><a class="reference internal" href="#centernet">CenterNet</a></li>
<li><a class="reference internal" href="#yolox">YOLOX</a></li>
<li><a class="reference internal" href="#pvt">PVT</a></li>
<li><a class="reference internal" href="#solo">SOLO</a></li>
<li><a class="reference internal" href="#queryinst">QueryInst</a></li>
<li><a class="reference internal" href="#panopticfpn">PanopticFPN</a></li>
<li><a class="reference internal" href="#maskformer">MaskFormer</a></li>
<li><a class="reference internal" href="#dyhead">DyHead</a></li>
<li><a class="reference internal" href="#mask2former">Mask2Former</a></li>
<li><a class="reference internal" href="#efficientnet">Efficientnet</a></li>
<li><a class="reference internal" href="#other-datasets">Other datasets</a></li>
<li><a class="reference internal" href="#pre-trained-models">Pre-trained Models</a></li>
</ul>
</li>
<li><a class="reference internal" href="#speed-benchmark">Speed benchmark</a><ul>
<li><a class="reference internal" href="#training-speed-benchmark">Training Speed benchmark</a></li>
<li><a class="reference internal" href="#inference-speed-benchmark">Inference Speed Benchmark</a></li>
</ul>
</li>
<li><a class="reference internal" href="#comparison-with-detectron2">Comparison with Detectron2</a><ul>
<li><a class="reference internal" href="#hardware">Hardware</a></li>
<li><a class="reference internal" href="#software-environment">Software environment</a></li>
<li><a class="reference internal" href="#performance">Performance</a></li>
<li><a class="reference internal" href="#training-speed">Training Speed</a></li>
<li><a class="reference internal" href="#inference-speed">Inference Speed</a></li>
<li><a class="reference internal" href="#training-memory">Training memory</a></li>
</ul>
</li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>