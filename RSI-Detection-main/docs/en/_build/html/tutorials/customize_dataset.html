


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 2: Customize Datasets &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 3: Customize Data Pipelines" href="data_pipeline.html" />
  <link rel="prev" title="Tutorial 1: Learn about Configs" href="config.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 2: Customize Datasets</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/customize_dataset.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-2-customize-datasets">
<h1>Tutorial 2: Customize Datasets<a class="headerlink" href="#tutorial-2-customize-datasets" title="Permalink to this heading">¶</a></h1>
<section id="support-new-data-format">
<h2>Support new data format<a class="headerlink" href="#support-new-data-format" title="Permalink to this heading">¶</a></h2>
<p>To support a new data format, you can either convert them to existing formats (COCO format or PASCAL format) or directly convert them to the middle format. You could also choose to convert them offline (before training by a script) or online (implement a new dataset and do the conversion at training). In MMDetection, we recommend to convert the data into COCO formats and do the conversion offline, thus you only need to modify the config’s data annotation paths and classes after the conversion of your data.</p>
<section id="reorganize-new-data-formats-to-existing-format">
<h3>Reorganize new data formats to existing format<a class="headerlink" href="#reorganize-new-data-formats-to-existing-format" title="Permalink to this heading">¶</a></h3>
<p>The simplest way is to convert your dataset to existing dataset formats (COCO or PASCAL VOC).</p>
<p>The annotation json files in COCO format has the following necessary keys:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;images&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;file_name&#39;</span><span class="p">:</span> <span class="s1">&#39;COCO_val2014_000000001268.jpg&#39;</span><span class="p">,</span>
        <span class="s1">&#39;height&#39;</span><span class="p">:</span> <span class="mi">427</span><span class="p">,</span>
        <span class="s1">&#39;width&#39;</span><span class="p">:</span> <span class="mi">640</span><span class="p">,</span>
        <span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">1268</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">],</span>

<span class="s1">&#39;annotations&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;segmentation&#39;</span><span class="p">:</span> <span class="p">[[</span><span class="mf">192.81</span><span class="p">,</span>
            <span class="mf">247.09</span><span class="p">,</span>
            <span class="o">...</span>
            <span class="mf">219.03</span><span class="p">,</span>
            <span class="mf">249.06</span><span class="p">]],</span>  <span class="c1"># if you have mask labels</span>
        <span class="s1">&#39;area&#39;</span><span class="p">:</span> <span class="mf">1035.749</span><span class="p">,</span>
        <span class="s1">&#39;iscrowd&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
        <span class="s1">&#39;image_id&#39;</span><span class="p">:</span> <span class="mi">1268</span><span class="p">,</span>
        <span class="s1">&#39;bbox&#39;</span><span class="p">:</span> <span class="p">[</span><span class="mf">192.81</span><span class="p">,</span> <span class="mf">224.8</span><span class="p">,</span> <span class="mf">74.73</span><span class="p">,</span> <span class="mf">33.43</span><span class="p">],</span>
        <span class="s1">&#39;category_id&#39;</span><span class="p">:</span> <span class="mi">16</span><span class="p">,</span>
        <span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">42986</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">],</span>

<span class="s1">&#39;categories&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;car&#39;</span><span class="p">},</span>
 <span class="p">]</span>
</pre></div>
</div>
<p>There are three necessary keys in the json file:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">images</span></code>: contains a list of images with their information like <code class="docutils literal notranslate"><span class="pre">file_name</span></code>, <code class="docutils literal notranslate"><span class="pre">height</span></code>, <code class="docutils literal notranslate"><span class="pre">width</span></code>, and <code class="docutils literal notranslate"><span class="pre">id</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">annotations</span></code>: contains the list of instance annotations.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">categories</span></code>: contains the list of categories names and their ID.</p></li>
</ul>
<p>After the data pre-processing, there are two steps for users to train the customized new dataset with existing format (e.g. COCO format):</p>
<ol class="arabic simple">
<li><p>Modify the config file for using the customized dataset.</p></li>
<li><p>Check the annotations of the customized dataset.</p></li>
</ol>
<p>Here we give an example to show the above two steps, which uses a customized dataset of 5 classes with COCO format to train an existing Cascade Mask R-CNN R50-FPN detector.</p>
<section id="modify-the-config-file-for-using-the-customized-dataset">
<h4>1. Modify the config file for using the customized dataset<a class="headerlink" href="#modify-the-config-file-for-using-the-customized-dataset" title="Permalink to this heading">¶</a></h4>
<p>There are two aspects involved in the modification of config file:</p>
<ol class="arabic simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">data</span></code> field. Specifically, you need to explicitly add the <code class="docutils literal notranslate"><span class="pre">classes</span></code> fields in <code class="docutils literal notranslate"><span class="pre">data.train</span></code>, <code class="docutils literal notranslate"><span class="pre">data.val</span></code> and <code class="docutils literal notranslate"><span class="pre">data.test</span></code>.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> field in the <code class="docutils literal notranslate"><span class="pre">model</span></code> part. Explicitly over-write all the <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> from default value (e.g. 80 in COCO) to your classes number.</p></li>
</ol>
<p>In <code class="docutils literal notranslate"><span class="pre">configs/my_custom_config.py</span></code>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span>
<span class="c1"># the new config inherits the base configs to highlight the necessary modification</span>
<span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;./cascade_mask_rcnn_r50_fpn_1x_coco.py&#39;</span>

<span class="c1"># 1. dataset settings</span>
<span class="n">dataset_type</span> <span class="o">=</span> <span class="s1">&#39;CocoDataset&#39;</span>
<span class="n">classes</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">,</span> <span class="s1">&#39;d&#39;</span><span class="p">,</span> <span class="s1">&#39;e&#39;</span><span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="n">dataset_type</span><span class="p">,</span>
        <span class="c1"># explicitly add your class names to the field `classes`</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;path/to/your/train/annotation_data&#39;</span><span class="p">,</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;path/to/your/train/image_data&#39;</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="n">dataset_type</span><span class="p">,</span>
        <span class="c1"># explicitly add your class names to the field `classes`</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;path/to/your/val/annotation_data&#39;</span><span class="p">,</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;path/to/your/val/image_data&#39;</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="n">dataset_type</span><span class="p">,</span>
        <span class="c1"># explicitly add your class names to the field `classes`</span>
        <span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">,</span>
        <span class="n">ann_file</span><span class="o">=</span><span class="s1">&#39;path/to/your/test/annotation_data&#39;</span><span class="p">,</span>
        <span class="n">img_prefix</span><span class="o">=</span><span class="s1">&#39;path/to/your/test/image_data&#39;</span><span class="p">))</span>

<span class="c1"># 2. model settings</span>

<span class="c1"># explicitly over-write all the `num_classes` field from default 80 to 5.</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">roi_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">bbox_head</span><span class="o">=</span><span class="p">[</span>
            <span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Shared2FCBBoxHead&#39;</span><span class="p">,</span>
                <span class="c1"># explicitly over-write all the `num_classes` field from default 80 to 5.</span>
                <span class="n">num_classes</span><span class="o">=</span><span class="mi">5</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Shared2FCBBoxHead&#39;</span><span class="p">,</span>
                <span class="c1"># explicitly over-write all the `num_classes` field from default 80 to 5.</span>
                <span class="n">num_classes</span><span class="o">=</span><span class="mi">5</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Shared2FCBBoxHead&#39;</span><span class="p">,</span>
                <span class="c1"># explicitly over-write all the `num_classes` field from default 80 to 5.</span>
                <span class="n">num_classes</span><span class="o">=</span><span class="mi">5</span><span class="p">)],</span>
    <span class="c1"># explicitly over-write all the `num_classes` field from default 80 to 5.</span>
    <span class="n">mask_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">num_classes</span><span class="o">=</span><span class="mi">5</span><span class="p">)))</span>
</pre></div>
</div>
</section>
<section id="check-the-annotations-of-the-customized-dataset">
<h4>2. Check the annotations of the customized dataset<a class="headerlink" href="#check-the-annotations-of-the-customized-dataset" title="Permalink to this heading">¶</a></h4>
<p>Assuming your customized dataset is COCO format, make sure you have the correct annotations in the customized dataset:</p>
<ol class="arabic simple">
<li><p>The length for <code class="docutils literal notranslate"><span class="pre">categories</span></code> field in annotations should exactly equal the tuple length of <code class="docutils literal notranslate"><span class="pre">classes</span></code> fields in your config, meaning the number of classes (e.g. 5 in this example).</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">classes</span></code> fields in your config file should have exactly the same elements and the same order with the <code class="docutils literal notranslate"><span class="pre">name</span></code> in <code class="docutils literal notranslate"><span class="pre">categories</span></code> of annotations. MMDetection automatically maps the uncontinuous <code class="docutils literal notranslate"><span class="pre">id</span></code> in <code class="docutils literal notranslate"><span class="pre">categories</span></code> to the continuous label indices, so the string order of <code class="docutils literal notranslate"><span class="pre">name</span></code> in <code class="docutils literal notranslate"><span class="pre">categories</span></code> field affects the order of label indices. Meanwhile, the string order of <code class="docutils literal notranslate"><span class="pre">classes</span></code> in config affects the label text during visualization of predicted bounding boxes.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">category_id</span></code> in <code class="docutils literal notranslate"><span class="pre">annotations</span></code> field should be valid, i.e., all values in <code class="docutils literal notranslate"><span class="pre">category_id</span></code> should belong to <code class="docutils literal notranslate"><span class="pre">id</span></code> in <code class="docutils literal notranslate"><span class="pre">categories</span></code>.</p></li>
</ol>
<p>Here is a valid example of annotations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span>
<span class="s1">&#39;annotations&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;segmentation&#39;</span><span class="p">:</span> <span class="p">[[</span><span class="mf">192.81</span><span class="p">,</span>
            <span class="mf">247.09</span><span class="p">,</span>
            <span class="o">...</span>
            <span class="mf">219.03</span><span class="p">,</span>
            <span class="mf">249.06</span><span class="p">]],</span>  <span class="c1"># if you have mask labels</span>
        <span class="s1">&#39;area&#39;</span><span class="p">:</span> <span class="mf">1035.749</span><span class="p">,</span>
        <span class="s1">&#39;iscrowd&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
        <span class="s1">&#39;image_id&#39;</span><span class="p">:</span> <span class="mi">1268</span><span class="p">,</span>
        <span class="s1">&#39;bbox&#39;</span><span class="p">:</span> <span class="p">[</span><span class="mf">192.81</span><span class="p">,</span> <span class="mf">224.8</span><span class="p">,</span> <span class="mf">74.73</span><span class="p">,</span> <span class="mf">33.43</span><span class="p">],</span>
        <span class="s1">&#39;category_id&#39;</span><span class="p">:</span> <span class="mi">16</span><span class="p">,</span>
        <span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">42986</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">],</span>

<span class="c1"># MMDetection automatically maps the uncontinuous `id` to the continuous label indices.</span>
<span class="s1">&#39;categories&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;a&#39;</span><span class="p">},</span> <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;b&#39;</span><span class="p">},</span> <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">4</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;c&#39;</span><span class="p">},</span> <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">16</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;d&#39;</span><span class="p">},</span> <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">17</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;e&#39;</span><span class="p">},</span>
 <span class="p">]</span>
</pre></div>
</div>
<p>We use this way to support CityScapes dataset. The script is in <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/dataset_converters/cityscapes.py">cityscapes.py</a> and we also provide the finetuning <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/cityscapes">configs</a>.</p>
<p><strong>Note</strong></p>
<ol class="arabic simple">
<li><p>For instance segmentation datasets, <strong>MMDetection only supports evaluating mask AP of dataset in COCO format for now</strong>.</p></li>
<li><p>It is recommended to convert the data offline before training, thus you can still use <code class="docutils literal notranslate"><span class="pre">CocoDataset</span></code> and only need to modify the path of annotations and the training classes.</p></li>
</ol>
</section>
</section>
<section id="reorganize-new-data-format-to-middle-format">
<h3>Reorganize new data format to middle format<a class="headerlink" href="#reorganize-new-data-format-to-middle-format" title="Permalink to this heading">¶</a></h3>
<p>It is also fine if you do not want to convert the annotation format to COCO or PASCAL format.
Actually, we define a simple annotation format and all existing datasets are
processed to be compatible with it, either online or offline.</p>
<p>The annotation of a dataset is a list of dict, each dict corresponds to an image.
There are 3 field <code class="docutils literal notranslate"><span class="pre">filename</span></code> (relative path), <code class="docutils literal notranslate"><span class="pre">width</span></code>, <code class="docutils literal notranslate"><span class="pre">height</span></code> for testing,
and an additional field <code class="docutils literal notranslate"><span class="pre">ann</span></code> for training. <code class="docutils literal notranslate"><span class="pre">ann</span></code> is also a dict containing at least 2 fields:
<code class="docutils literal notranslate"><span class="pre">bboxes</span></code> and <code class="docutils literal notranslate"><span class="pre">labels</span></code>, both of which are numpy arrays. Some datasets may provide
annotations like crowd/difficult/ignored bboxes, we use <code class="docutils literal notranslate"><span class="pre">bboxes_ignore</span></code> and <code class="docutils literal notranslate"><span class="pre">labels_ignore</span></code>
to cover them.</p>
<p>Here is an example.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span>
<span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;filename&#39;</span><span class="p">:</span> <span class="s1">&#39;a.jpg&#39;</span><span class="p">,</span>
        <span class="s1">&#39;width&#39;</span><span class="p">:</span> <span class="mi">1280</span><span class="p">,</span>
        <span class="s1">&#39;height&#39;</span><span class="p">:</span> <span class="mi">720</span><span class="p">,</span>
        <span class="s1">&#39;ann&#39;</span><span class="p">:</span> <span class="p">{</span>
            <span class="s1">&#39;bboxes&#39;</span><span class="p">:</span> <span class="o">&lt;</span><span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">,</span> <span class="n">float32</span><span class="o">&gt;</span> <span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
            <span class="s1">&#39;labels&#39;</span><span class="p">:</span> <span class="o">&lt;</span><span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">,</span> <span class="n">int64</span><span class="o">&gt;</span> <span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="p">),</span>
            <span class="s1">&#39;bboxes_ignore&#39;</span><span class="p">:</span> <span class="o">&lt;</span><span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">,</span> <span class="n">float32</span><span class="o">&gt;</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
            <span class="s1">&#39;labels_ignore&#39;</span><span class="p">:</span> <span class="o">&lt;</span><span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">,</span> <span class="n">int64</span><span class="o">&gt;</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="p">)</span> <span class="p">(</span><span class="n">optional</span> <span class="n">field</span><span class="p">)</span>
        <span class="p">}</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">]</span>
</pre></div>
</div>
<p>There are two ways to work with custom datasets.</p>
<ul>
<li><p>online conversion</p>
<p>You can write a new Dataset class inherited from <code class="docutils literal notranslate"><span class="pre">CustomDataset</span></code>, and overwrite two methods
<code class="docutils literal notranslate"><span class="pre">load_annotations(self,</span> <span class="pre">ann_file)</span></code> and <code class="docutils literal notranslate"><span class="pre">get_ann_info(self,</span> <span class="pre">idx)</span></code>,
like <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/datasets/coco.py">CocoDataset</a> and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/datasets/voc.py">VOCDataset</a>.</p>
</li>
<li><p>offline conversion</p>
<p>You can convert the annotation format to the expected format above and save it to
a pickle or json file, like <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/dataset_converters/pascal_voc.py">pascal_voc.py</a>.
Then you can simply use <code class="docutils literal notranslate"><span class="pre">CustomDataset</span></code>.</p>
</li>
</ul>
</section>
<section id="an-example-of-customized-dataset">
<h3>An example of customized dataset<a class="headerlink" href="#an-example-of-customized-dataset" title="Permalink to this heading">¶</a></h3>
<p>Assume the annotation is in a new format in text files.
The bounding boxes annotations are stored in text file <code class="docutils literal notranslate"><span class="pre">annotation.txt</span></code> as the following</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="c1">#</span>
<span class="mf">000001.</span><span class="n">jpg</span>
<span class="mi">1280</span> <span class="mi">720</span>
<span class="mi">2</span>
<span class="mi">10</span> <span class="mi">20</span> <span class="mi">40</span> <span class="mi">60</span> <span class="mi">1</span>
<span class="mi">20</span> <span class="mi">40</span> <span class="mi">50</span> <span class="mi">60</span> <span class="mi">2</span>
<span class="c1">#</span>
<span class="mf">000002.</span><span class="n">jpg</span>
<span class="mi">1280</span> <span class="mi">720</span>
<span class="mi">3</span>
<span class="mi">50</span> <span class="mi">20</span> <span class="mi">40</span> <span class="mi">60</span> <span class="mi">2</span>
<span class="mi">20</span> <span class="mi">40</span> <span class="mi">30</span> <span class="mi">45</span> <span class="mi">2</span>
<span class="mi">30</span> <span class="mi">40</span> <span class="mi">50</span> <span class="mi">60</span> <span class="mi">3</span>
</pre></div>
</div>
<p>We can create a new dataset in <code class="docutils literal notranslate"><span class="pre">rsidet/datasets/my_dataset.py</span></code> to load the data.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">mmcv</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="kn">as</span> <span class="nn">np</span>

<span class="kn">from</span> <span class="nn">.builder</span> <span class="kn">import</span> <span class="n">DATASETS</span>
<span class="kn">from</span> <span class="nn">.custom</span> <span class="kn">import</span> <span class="n">CustomDataset</span>


<span class="nd">@DATASETS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">MyDataset</span><span class="p">(</span><span class="n">CustomDataset</span><span class="p">):</span>

    <span class="n">CLASSES</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;person&#39;</span><span class="p">,</span> <span class="s1">&#39;bicycle&#39;</span><span class="p">,</span> <span class="s1">&#39;car&#39;</span><span class="p">,</span> <span class="s1">&#39;motorcycle&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">load_annotations</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ann_file</span><span class="p">):</span>
        <span class="n">ann_list</span> <span class="o">=</span> <span class="n">mmcv</span><span class="o">.</span><span class="n">list_from_file</span><span class="p">(</span><span class="n">ann_file</span><span class="p">)</span>

        <span class="n">data_infos</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">ann_line</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">ann_list</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">ann_line</span> <span class="o">!=</span> <span class="s1">&#39;#&#39;</span><span class="p">:</span>
                <span class="k">continue</span>

            <span class="n">img_shape</span> <span class="o">=</span> <span class="n">ann_list</span><span class="p">[</span><span class="n">i</span> <span class="o">+</span> <span class="mi">2</span><span class="p">]</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39; &#39;</span><span class="p">)</span>
            <span class="n">width</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">img_shape</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
            <span class="n">height</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">img_shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
            <span class="n">bbox_number</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">ann_list</span><span class="p">[</span><span class="n">i</span> <span class="o">+</span> <span class="mi">3</span><span class="p">])</span>

            <span class="n">anns</span> <span class="o">=</span> <span class="n">ann_line</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39; &#39;</span><span class="p">)</span>
            <span class="n">bboxes</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">labels</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">anns</span> <span class="ow">in</span> <span class="n">ann_list</span><span class="p">[</span><span class="n">i</span> <span class="o">+</span> <span class="mi">4</span><span class="p">:</span><span class="n">i</span> <span class="o">+</span> <span class="mi">4</span> <span class="o">+</span> <span class="n">bbox_number</span><span class="p">]:</span>
                <span class="n">bboxes</span><span class="o">.</span><span class="n">append</span><span class="p">([</span><span class="nb">float</span><span class="p">(</span><span class="n">ann</span><span class="p">)</span> <span class="k">for</span> <span class="n">ann</span> <span class="ow">in</span> <span class="n">anns</span><span class="p">[:</span><span class="mi">4</span><span class="p">]])</span>
                <span class="n">labels</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">anns</span><span class="p">[</span><span class="mi">4</span><span class="p">]))</span>

            <span class="n">data_infos</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
                <span class="nb">dict</span><span class="p">(</span>
                    <span class="n">filename</span><span class="o">=</span><span class="n">ann_list</span><span class="p">[</span><span class="n">i</span> <span class="o">+</span> <span class="mi">1</span><span class="p">],</span>
                    <span class="n">width</span><span class="o">=</span><span class="n">width</span><span class="p">,</span>
                    <span class="n">height</span><span class="o">=</span><span class="n">height</span><span class="p">,</span>
                    <span class="n">ann</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                        <span class="n">bboxes</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">bboxes</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">),</span>
                        <span class="n">labels</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">labels</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">))</span>
                <span class="p">))</span>

        <span class="k">return</span> <span class="n">data_infos</span>

    <span class="k">def</span> <span class="nf">get_ann_info</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">idx</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_infos</span><span class="p">[</span><span class="n">idx</span><span class="p">][</span><span class="s1">&#39;ann&#39;</span><span class="p">]</span>

</pre></div>
</div>
<p>Then in the config, to use <code class="docutils literal notranslate"><span class="pre">MyDataset</span></code> you can modify the config as the following</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MyDataset&#39;</span><span class="p">,</span>
    <span class="n">ann_file</span> <span class="o">=</span> <span class="s1">&#39;image_list.txt&#39;</span><span class="p">,</span>
    <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="customize-datasets-by-dataset-wrappers">
<h2>Customize datasets by dataset wrappers<a class="headerlink" href="#customize-datasets-by-dataset-wrappers" title="Permalink to this heading">¶</a></h2>
<p>MMDetection also supports many dataset wrappers to mix the dataset or modify the dataset distribution for training.
Currently it supports to three dataset wrappers as below:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">RepeatDataset</span></code>: simply repeat the whole dataset.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ClassBalancedDataset</span></code>: repeat dataset in a class balanced manner.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConcatDataset</span></code>: concat datasets.</p></li>
</ul>
<section id="repeat-dataset">
<h3>Repeat dataset<a class="headerlink" href="#repeat-dataset" title="Permalink to this heading">¶</a></h3>
<p>We use <code class="docutils literal notranslate"><span class="pre">RepeatDataset</span></code> as wrapper to repeat the dataset. For example, suppose the original dataset is <code class="docutils literal notranslate"><span class="pre">Dataset_A</span></code>, to repeat it, the config looks like the following</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RepeatDataset&#39;</span><span class="p">,</span>
        <span class="n">times</span><span class="o">=</span><span class="n">N</span><span class="p">,</span>
        <span class="n">dataset</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>  <span class="c1"># This is the original config of Dataset_A</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_A&#39;</span><span class="p">,</span>
            <span class="o">...</span>
            <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
        <span class="p">)</span>
    <span class="p">)</span>
</pre></div>
</div>
</section>
<section id="class-balanced-dataset">
<h3>Class balanced dataset<a class="headerlink" href="#class-balanced-dataset" title="Permalink to this heading">¶</a></h3>
<p>We use <code class="docutils literal notranslate"><span class="pre">ClassBalancedDataset</span></code> as wrapper to repeat the dataset based on category
frequency. The dataset to repeat needs to instantiate function <code class="docutils literal notranslate"><span class="pre">self.get_cat_ids(idx)</span></code>
to support <code class="docutils literal notranslate"><span class="pre">ClassBalancedDataset</span></code>.
For example, to repeat <code class="docutils literal notranslate"><span class="pre">Dataset_A</span></code> with <code class="docutils literal notranslate"><span class="pre">oversample_thr=1e-3</span></code>, the config looks like the following</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ClassBalancedDataset&#39;</span><span class="p">,</span>
        <span class="n">oversample_thr</span><span class="o">=</span><span class="mf">1e-3</span><span class="p">,</span>
        <span class="n">dataset</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>  <span class="c1"># This is the original config of Dataset_A</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_A&#39;</span><span class="p">,</span>
            <span class="o">...</span>
            <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
        <span class="p">)</span>
    <span class="p">)</span>
</pre></div>
</div>
<p>You may refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/datasets/dataset_wrappers.py#L211">source code</a> for details.</p>
</section>
<section id="concatenate-dataset">
<h3>Concatenate dataset<a class="headerlink" href="#concatenate-dataset" title="Permalink to this heading">¶</a></h3>
<p>There are three ways to concatenate the dataset.</p>
<ol class="arabic">
<li><p>If the datasets you want to concatenate are in the same type with different annotation files, you can concatenate the dataset configs like the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_A&#39;</span><span class="p">,</span>
    <span class="n">ann_file</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;anno_file_1&#39;</span><span class="p">,</span> <span class="s1">&#39;anno_file_2&#39;</span><span class="p">],</span>
    <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
<span class="p">)</span>
</pre></div>
</div>
<p>If the concatenated dataset is used for test or evaluation, this manner supports to evaluate each dataset separately. To test the concatenated datasets as a whole, you can set <code class="docutils literal notranslate"><span class="pre">separate_eval=False</span></code> as below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_A&#39;</span><span class="p">,</span>
    <span class="n">ann_file</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;anno_file_1&#39;</span><span class="p">,</span> <span class="s1">&#39;anno_file_2&#39;</span><span class="p">],</span>
    <span class="n">separate_eval</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
    <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
<span class="p">)</span>
</pre></div>
</div>
</li>
<li><p>In case the dataset you want to concatenate is different, you can concatenate the dataset configs like the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
<span class="n">dataset_B_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>

<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">imgs_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">train</span> <span class="o">=</span> <span class="p">[</span>
        <span class="n">dataset_A_train</span><span class="p">,</span>
        <span class="n">dataset_B_train</span>
    <span class="p">],</span>
    <span class="n">val</span> <span class="o">=</span> <span class="n">dataset_A_val</span><span class="p">,</span>
    <span class="n">test</span> <span class="o">=</span> <span class="n">dataset_A_test</span>
    <span class="p">)</span>
</pre></div>
</div>
<p>If the concatenated dataset is used for test or evaluation, this manner also supports to evaluate each dataset separately.</p>
</li>
<li><p>We also support to define <code class="docutils literal notranslate"><span class="pre">ConcatDataset</span></code> explicitly as the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_val</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
<span class="n">dataset_B_val</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>

<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">imgs_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">train</span><span class="o">=</span><span class="n">dataset_A_train</span><span class="p">,</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ConcatDataset&#39;</span><span class="p">,</span>
        <span class="n">datasets</span><span class="o">=</span><span class="p">[</span><span class="n">dataset_A_val</span><span class="p">,</span> <span class="n">dataset_B_val</span><span class="p">],</span>
        <span class="n">separate_eval</span><span class="o">=</span><span class="bp">False</span><span class="p">))</span>
</pre></div>
</div>
<p>This manner allows users to evaluate all the datasets as a single one by setting <code class="docutils literal notranslate"><span class="pre">separate_eval=False</span></code>.</p>
</li>
</ol>
<p><strong>Note:</strong></p>
<ol class="arabic simple">
<li><p>The option <code class="docutils literal notranslate"><span class="pre">separate_eval=False</span></code> assumes the datasets use <code class="docutils literal notranslate"><span class="pre">self.data_infos</span></code> during evaluation. Therefore, COCO datasets do not support this behavior since COCO datasets do not fully rely on <code class="docutils literal notranslate"><span class="pre">self.data_infos</span></code> for evaluation. Combining different types of datasets and evaluating them as a whole is not tested thus is not suggested.</p></li>
<li><p>Evaluating <code class="docutils literal notranslate"><span class="pre">ClassBalancedDataset</span></code> and <code class="docutils literal notranslate"><span class="pre">RepeatDataset</span></code> is not supported thus evaluating concatenated datasets of these types is also not supported.</p></li>
</ol>
<p>A more complex example that repeats <code class="docutils literal notranslate"><span class="pre">Dataset_A</span></code> and <code class="docutils literal notranslate"><span class="pre">Dataset_B</span></code> by N and M times, respectively, and then concatenates the repeated datasets is as the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">dataset_A_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RepeatDataset&#39;</span><span class="p">,</span>
    <span class="n">times</span><span class="o">=</span><span class="n">N</span><span class="p">,</span>
    <span class="n">dataset</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_A&#39;</span><span class="p">,</span>
        <span class="o">...</span>
        <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
    <span class="p">)</span>
<span class="p">)</span>
<span class="n">dataset_A_val</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="o">...</span>
    <span class="n">pipeline</span><span class="o">=</span><span class="n">test_pipeline</span>
<span class="p">)</span>
<span class="n">dataset_A_test</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="o">...</span>
    <span class="n">pipeline</span><span class="o">=</span><span class="n">test_pipeline</span>
<span class="p">)</span>
<span class="n">dataset_B_train</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RepeatDataset&#39;</span><span class="p">,</span>
    <span class="n">times</span><span class="o">=</span><span class="n">M</span><span class="p">,</span>
    <span class="n">dataset</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Dataset_B&#39;</span><span class="p">,</span>
        <span class="o">...</span>
        <span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span>
    <span class="p">)</span>
<span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">imgs_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
    <span class="n">train</span> <span class="o">=</span> <span class="p">[</span>
        <span class="n">dataset_A_train</span><span class="p">,</span>
        <span class="n">dataset_B_train</span>
    <span class="p">],</span>
    <span class="n">val</span> <span class="o">=</span> <span class="n">dataset_A_val</span><span class="p">,</span>
    <span class="n">test</span> <span class="o">=</span> <span class="n">dataset_A_test</span>
<span class="p">)</span>

</pre></div>
</div>
</section>
</section>
<section id="modify-dataset-classes">
<h2>Modify Dataset Classes<a class="headerlink" href="#modify-dataset-classes" title="Permalink to this heading">¶</a></h2>
<p>With existing dataset types, we can modify the class names of them to train subset of the annotations.
For example, if you want to train only three classes of the current dataset,
you can modify the classes of dataset.
The dataset will filter out the ground truth boxes of other classes automatically.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">classes</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;person&#39;</span><span class="p">,</span> <span class="s1">&#39;bicycle&#39;</span><span class="p">,</span> <span class="s1">&#39;car&#39;</span><span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">))</span>
</pre></div>
</div>
<p>MMDetection V2.0 also supports to read the classes from a file, which is common in real applications.
For example, assume the <code class="docutils literal notranslate"><span class="pre">classes.txt</span></code> contains the name of classes as the following.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">person</span>
<span class="n">bicycle</span>
<span class="n">car</span>
</pre></div>
</div>
<p>Users can set the classes as a file path, the dataset will load it and convert it to a list automatically.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">classes</span> <span class="o">=</span> <span class="s1">&#39;path/to/classes.txt&#39;</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">classes</span><span class="o">=</span><span class="n">classes</span><span class="p">))</span>
</pre></div>
</div>
<p><strong>Note</strong>:</p>
<ul class="simple">
<li><p>Before MMDetection v2.5.0, the dataset will filter out the empty GT images automatically if the classes are set and there is no way to disable that through config. This is an undesirable behavior and introduces confusion because if the classes are not set, the dataset only filter the empty GT images when <code class="docutils literal notranslate"><span class="pre">filter_empty_gt=True</span></code> and <code class="docutils literal notranslate"><span class="pre">test_mode=False</span></code>. After MMDetection v2.5.0, we decouple the image filtering process and the classes modification, i.e., the dataset will only filter empty GT images when <code class="docutils literal notranslate"><span class="pre">filter_empty_gt=True</span></code> and <code class="docutils literal notranslate"><span class="pre">test_mode=False</span></code>, no matter whether the classes are set. Thus, setting the classes only influences the annotations of classes used for training and users could decide whether to filter empty GT images by themselves.</p></li>
<li><p>Since the middle format only has box labels and does not contain the class names, when using <code class="docutils literal notranslate"><span class="pre">CustomDataset</span></code>, users cannot filter out the empty GT images through configs but only do this offline.</p></li>
<li><p>Please remember to modify the <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> in the head when specifying <code class="docutils literal notranslate"><span class="pre">classes</span></code> in dataset. We implemented <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/datasets/utils.py">NumClassCheckHook</a> to check whether the numbers are consistent since v2.9.0(after PR#4508).</p></li>
<li><p>The features for setting dataset classes and dataset filtering will be refactored to be more user-friendly in the future (depends on the progress).</p></li>
</ul>
</section>
<section id="coco-panoptic-dataset">
<h2>COCO Panoptic Dataset<a class="headerlink" href="#coco-panoptic-dataset" title="Permalink to this heading">¶</a></h2>
<p>Now we support COCO Panoptic Dataset, the format of panoptic annotations is different from COCO format.
Both the foreground and the background will exist in the annotation file.
The annotation json files in COCO Panoptic format has the following necessary keys:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;images&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;file_name&#39;</span><span class="p">:</span> <span class="s1">&#39;000000001268.jpg&#39;</span><span class="p">,</span>
        <span class="s1">&#39;height&#39;</span><span class="p">:</span> <span class="mi">427</span><span class="p">,</span>
        <span class="s1">&#39;width&#39;</span><span class="p">:</span> <span class="mi">640</span><span class="p">,</span>
        <span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">1268</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">]</span>

<span class="s1">&#39;annotations&#39;</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s1">&#39;filename&#39;</span><span class="p">:</span> <span class="s1">&#39;000000001268.jpg&#39;</span><span class="p">,</span>
        <span class="s1">&#39;image_id&#39;</span><span class="p">:</span> <span class="mi">1268</span><span class="p">,</span>
        <span class="s1">&#39;segments_info&#39;</span><span class="p">:</span> <span class="p">[</span>
            <span class="p">{</span>
                <span class="s1">&#39;id&#39;</span><span class="p">:</span><span class="mi">8345037</span><span class="p">,</span>  <span class="c1"># One-to-one correspondence with the id in the annotation map.</span>
                <span class="s1">&#39;category_id&#39;</span><span class="p">:</span> <span class="mi">51</span><span class="p">,</span>
                <span class="s1">&#39;iscrowd&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                <span class="s1">&#39;bbox&#39;</span><span class="p">:</span> <span class="p">(</span><span class="n">x1</span><span class="p">,</span> <span class="n">y1</span><span class="p">,</span> <span class="n">w</span><span class="p">,</span> <span class="n">h</span><span class="p">),</span>  <span class="c1"># The bbox of the background is the outer rectangle of its mask.</span>
                <span class="s1">&#39;area&#39;</span><span class="p">:</span> <span class="mi">24315</span>
            <span class="p">},</span>
            <span class="o">...</span>
        <span class="p">]</span>
    <span class="p">},</span>
    <span class="o">...</span>
<span class="p">]</span>

<span class="s1">&#39;categories&#39;</span><span class="p">:</span> <span class="p">[</span>  <span class="c1"># including both foreground categories and background categories</span>
    <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;person&#39;</span><span class="p">},</span>
    <span class="o">...</span>
 <span class="p">]</span>
</pre></div>
</div>
<p>Moreover, the <code class="docutils literal notranslate"><span class="pre">seg_prefix</span></code> must be set to the path of the panoptic annotation images.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;CocoPanopticDataset&#39;</span><span class="p">,</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">seg_prefix</span> <span class="o">=</span> <span class="s1">&#39;path/to/your/train/panoptic/image_annotation_data&#39;</span>
    <span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">seg_prefix</span> <span class="o">=</span> <span class="s1">&#39;path/to/your/train/panoptic/image_annotation_data&#39;</span>
    <span class="p">)</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="data_pipeline.html" class="btn btn-neutral float-right" title="Tutorial 3: Customize Data Pipelines" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="config.html" class="btn btn-neutral" title="Tutorial 1: Learn about Configs" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 2: Customize Datasets</a><ul>
<li><a class="reference internal" href="#support-new-data-format">Support new data format</a><ul>
<li><a class="reference internal" href="#reorganize-new-data-formats-to-existing-format">Reorganize new data formats to existing format</a><ul>
<li><a class="reference internal" href="#modify-the-config-file-for-using-the-customized-dataset">1. Modify the config file for using the customized dataset</a></li>
<li><a class="reference internal" href="#check-the-annotations-of-the-customized-dataset">2. Check the annotations of the customized dataset</a></li>
</ul>
</li>
<li><a class="reference internal" href="#reorganize-new-data-format-to-middle-format">Reorganize new data format to middle format</a></li>
<li><a class="reference internal" href="#an-example-of-customized-dataset">An example of customized dataset</a></li>
</ul>
</li>
<li><a class="reference internal" href="#customize-datasets-by-dataset-wrappers">Customize datasets by dataset wrappers</a><ul>
<li><a class="reference internal" href="#repeat-dataset">Repeat dataset</a></li>
<li><a class="reference internal" href="#class-balanced-dataset">Class balanced dataset</a></li>
<li><a class="reference internal" href="#concatenate-dataset">Concatenate dataset</a></li>
</ul>
</li>
<li><a class="reference internal" href="#modify-dataset-classes">Modify Dataset Classes</a></li>
<li><a class="reference internal" href="#coco-panoptic-dataset">COCO Panoptic Dataset</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>