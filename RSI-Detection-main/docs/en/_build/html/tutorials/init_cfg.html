


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 10: Weight initialization &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 11: How to xxx" href="how_to.html" />
  <link rel="prev" title="Tutorial 9: ONNX to TensorRT (Experimental)" href="onnx2tensorrt.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 10: Weight initialization</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/init_cfg.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-10-weight-initialization">
<h1>Tutorial 10: Weight initialization<a class="headerlink" href="#tutorial-10-weight-initialization" title="Permalink to this heading">¶</a></h1>
<p>During training, a proper initialization strategy is beneficial to speeding up the training or obtaining a higher performance. <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/mmcv/cnn/utils/weight_init.py">MMCV</a> provide some commonly used methods for initializing modules like <code class="docutils literal notranslate"><span class="pre">nn.Conv2d</span></code>. Model initialization in MMdetection mainly uses <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code>. Users can initialize models with following two steps:</p>
<ol class="arabic simple">
<li><p>Define <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> for a model or its components in <code class="docutils literal notranslate"><span class="pre">model_cfg</span></code>,  but <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> of children components have higher priority and will override <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> of parents modules.</p></li>
<li><p>Build model as usual, but call <code class="docutils literal notranslate"><span class="pre">model.init_weights()</span></code> method explicitly, and model parameters will be initialized as configuration.</p></li>
</ol>
<p>The high-level workflow of initialization in MMdetection is :</p>
<p>model_cfg(init_cfg) -&gt; build_from_cfg -&gt; model -&gt; init_weight() -&gt; initialize(self, self.init_cfg) -&gt; children’s init_weight()</p>
<section id="description">
<h2>Description<a class="headerlink" href="#description" title="Permalink to this heading">¶</a></h2>
<p>It is dict or list[dict], and contains the following keys and values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> (str), containing the initializer name in <code class="docutils literal notranslate"><span class="pre">INTIALIZERS</span></code>, and followed by arguments of the initializer.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">layer</span></code> (str or list[str]), containing the names of basiclayers in Pytorch or MMCV with learnable parameters that will be initialized, e.g. <code class="docutils literal notranslate"><span class="pre">'Conv2d'</span></code>,<code class="docutils literal notranslate"><span class="pre">'DeformConv2d'</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">override</span></code> (dict or list[dict]),  containing the sub-modules that not inherit from BaseModule and whose initialization configuration is different from other layers’ which are in <code class="docutils literal notranslate"><span class="pre">'layer'</span></code> key. Initializer defined in <code class="docutils literal notranslate"><span class="pre">type</span></code> will work for all layers defined in <code class="docutils literal notranslate"><span class="pre">layer</span></code>, so if sub-modules are not derived Classes of <code class="docutils literal notranslate"><span class="pre">BaseModule</span></code> but can be initialized as same ways of layers in <code class="docutils literal notranslate"><span class="pre">layer</span></code>, it does not need to use <code class="docutils literal notranslate"><span class="pre">override</span></code>. <code class="docutils literal notranslate"><span class="pre">override</span></code> contains:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">type</span></code> followed by arguments of initializer;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> to indicate sub-module which will be initialized.</p></li>
</ul>
</li>
</ul>
</section>
<section id="initialize-parameters">
<h2>Initialize parameters<a class="headerlink" href="#initialize-parameters" title="Permalink to this heading">¶</a></h2>
<p>Inherit a new model from <code class="docutils literal notranslate"><span class="pre">mmcv.runner.BaseModule</span></code> or <code class="docutils literal notranslate"><span class="pre">rsidet.models</span></code>  Here we show an example of FooModel.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch.nn</span> <span class="kn">as</span> <span class="nn">nn</span>
<span class="kn">from</span> <span class="nn">mmcv.runner</span> <span class="kn">import</span> <span class="n">BaseModule</span>

<span class="k">class</span> <span class="nc">FooModel</span><span class="p">(</span><span class="n">BaseModule</span><span class="p">)</span>
	<span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">arg1</span><span class="p">,</span>
                 <span class="n">arg2</span><span class="p">,</span>
                 <span class="n">init_cfg</span><span class="o">=</span><span class="bp">None</span><span class="p">):</span>
    	<span class="nb">super</span><span class="p">(</span><span class="n">FooModel</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">init_cfg</span><span class="p">)</span>
		<span class="o">...</span>
</pre></div>
</div>
<ul>
<li><p>Initialize model by using <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> directly in code</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch.nn</span> <span class="kn">as</span> <span class="nn">nn</span>
<span class="kn">from</span> <span class="nn">mmcv.runner</span> <span class="kn">import</span> <span class="n">BaseModule</span>
<span class="c1"># or directly inherit rsidet models</span>

<span class="k">class</span> <span class="nc">FooModel</span><span class="p">(</span><span class="n">BaseModule</span><span class="p">)</span>
	<span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                <span class="n">arg1</span><span class="p">,</span>
                <span class="n">arg2</span><span class="p">,</span>
                <span class="n">init_cfg</span><span class="o">=</span><span class="n">XXX</span><span class="p">):</span>
		<span class="nb">super</span><span class="p">(</span><span class="n">FooModel</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">init_cfg</span><span class="p">)</span>
	    <span class="o">...</span>
</pre></div>
</div>
</li>
<li><p>Initialize model by using <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> directly in <code class="docutils literal notranslate"><span class="pre">mmcv.Sequential</span></code> or <code class="docutils literal notranslate"><span class="pre">mmcv.ModuleList</span></code> code</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">mmcv.runner</span> <span class="kn">import</span> <span class="n">BaseModule</span><span class="p">,</span> <span class="n">ModuleList</span>

<span class="k">class</span> <span class="nc">FooModel</span><span class="p">(</span><span class="n">BaseModule</span><span class="p">)</span>
	<span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
            	<span class="n">arg1</span><span class="p">,</span>
            	<span class="n">arg2</span><span class="p">,</span>
            	<span class="n">init_cfg</span><span class="o">=</span><span class="bp">None</span><span class="p">):</span>
		<span class="nb">super</span><span class="p">(</span><span class="n">FooModel</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">init_cfg</span><span class="p">)</span>
    	<span class="o">...</span>
    	<span class="bp">self</span><span class="o">.</span><span class="n">conv1</span> <span class="o">=</span> <span class="n">ModuleList</span><span class="p">(</span><span class="n">init_cfg</span><span class="o">=</span><span class="n">XXX</span><span class="p">)</span>
</pre></div>
</div>
</li>
<li><p>Initialize model by using <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> in config file</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
	<span class="o">...</span>
	<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    	<span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FooModel&#39;</span><span class="p">,</span>
    	<span class="n">arg1</span><span class="o">=</span><span class="n">XXX</span><span class="p">,</span>
    	<span class="n">arg2</span><span class="o">=</span><span class="n">XXX</span><span class="p">,</span>
    	<span class="n">init_cfg</span><span class="o">=</span><span class="n">XXX</span><span class="p">),</span>
        <span class="o">...</span>
</pre></div>
</div>
</li>
</ul>
</section>
<section id="usage-of-init-cfg">
<h2>Usage of init_cfg<a class="headerlink" href="#usage-of-init-cfg" title="Permalink to this heading">¶</a></h2>
<ol class="arabic">
<li><p>Initialize model by <code class="docutils literal notranslate"><span class="pre">layer</span></code> key</p>
<p>If we only define <code class="docutils literal notranslate"><span class="pre">layer</span></code>, it just initialize the layer in <code class="docutils literal notranslate"><span class="pre">layer</span></code> key.</p>
<p>NOTE: Value of <code class="docutils literal notranslate"><span class="pre">layer</span></code> key is the class name with attributes weights and bias of Pytorch, (so such as  <code class="docutils literal notranslate"><span class="pre">MultiheadAttention</span> <span class="pre">layer</span></code> is not supported).</p>
</li>
</ol>
<ul>
<li><p>Define <code class="docutils literal notranslate"><span class="pre">layer</span></code> key for initializing module with same configuration.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Conv1d&#39;</span><span class="p">,</span> <span class="s1">&#39;Conv2d&#39;</span><span class="p">,</span> <span class="s1">&#39;Linear&#39;</span><span class="p">],</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="c1"># initialize whole module with same configuration</span>
</pre></div>
</div>
</li>
<li><p>Define <code class="docutils literal notranslate"><span class="pre">layer</span></code> key for initializing layer with different configurations.</p></li>
</ul>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">init_cfg</span> <span class="o">=</span> <span class="p">[</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="s1">&#39;Conv1d&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="s1">&#39;Conv2d&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">2</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="s1">&#39;Linear&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">3</span><span class="p">)]</span>
<span class="c1"># nn.Conv1d will be initialized with dict(type=&#39;Constant&#39;, val=1)</span>
<span class="c1"># nn.Conv2d will be initialized with dict(type=&#39;Constant&#39;, val=2)</span>
<span class="c1"># nn.Linear will be initialized with dict(type=&#39;Constant&#39;, val=3)</span>
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p>Initialize model by <code class="docutils literal notranslate"><span class="pre">override</span></code> key</p></li>
</ol>
<ul>
<li><p>When initializing some specific part with its attribute name, we can use <code class="docutils literal notranslate"><span class="pre">override</span></code> key, and the value in <code class="docutils literal notranslate"><span class="pre">override</span></code> will ignore the value in init_cfg.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># layers：</span>
<span class="c1"># self.feat = nn.Conv1d(3, 1, 3)</span>
<span class="c1"># self.reg = nn.Conv2d(3, 3, 3)</span>
<span class="c1"># self.cls = nn.Linear(1,2)</span>

<span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span>
                <span class="n">layer</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Conv1d&#39;</span><span class="p">,</span><span class="s1">&#39;Conv2d&#39;</span><span class="p">],</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
                <span class="n">override</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s1">&#39;reg&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">4</span><span class="p">))</span>
<span class="c1"># self.feat and self.cls will be initialized with 	dict(type=&#39;Constant&#39;, val=1, bias=2)</span>
<span class="c1"># The module called &#39;reg&#39; will be initialized with dict(type=&#39;Constant&#39;, val=3, bias=4)</span>
</pre></div>
</div>
</li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">layer</span></code> is None in init_cfg, only sub-module with the name in override will be initialized, and type and other args in override can be omitted.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># layers：</span>
<span class="c1"># self.feat = nn.Conv1d(3, 1, 3)</span>
<span class="c1"># self.reg = nn.Conv2d(3, 3, 3)</span>
<span class="c1"># self.cls = nn.Linear(1,2)</span>

<span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> 	<span class="n">override</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s1">&#39;reg&#39;</span><span class="p">))</span>

<span class="c1"># self.feat and self.cls will be initialized by Pytorch</span>
<span class="c1"># The module called &#39;reg&#39; will be initialized with dict(type=&#39;Constant&#39;, val=1, bias=2)</span>
</pre></div>
</div>
</li>
<li><p>If we don’t define <code class="docutils literal notranslate"><span class="pre">layer</span></code> key or <code class="docutils literal notranslate"><span class="pre">override</span></code> key, it will not initialize anything.</p></li>
<li><p>Invalid usage</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># It is invalid that override don&#39;t have name key</span>
<span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Conv1d&#39;</span><span class="p">,</span><span class="s1">&#39;Conv2d&#39;</span><span class="p">],</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
            	<span class="n">override</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">4</span><span class="p">))</span>

<span class="c1"># It is also invalid that override has name and other args except type</span>
<span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Constant&#39;</span><span class="p">,</span> <span class="n">layer</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Conv1d&#39;</span><span class="p">,</span><span class="s1">&#39;Conv2d&#39;</span><span class="p">],</span> <span class="n">val</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
                <span class="n">override</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s1">&#39;reg&#39;</span><span class="p">,</span> <span class="n">val</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">bias</span><span class="o">=</span><span class="mi">4</span><span class="p">))</span>
</pre></div>
</div>
</li>
</ul>
<ol class="arabic" start="3">
<li><p>Initialize model with the pretrained model</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">init_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pretrained&#39;</span><span class="p">,</span>
            <span class="n">checkpoint</span><span class="o">=</span><span class="s1">&#39;torchvision://resnet50&#39;</span><span class="p">)</span>
</pre></div>
</div>
</li>
</ol>
<p>More details can refer to the documentation in <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/cnn.html#weight-initialization">MMCV</a> and MMCV <a class="reference external" href="https://github.com/open-mmlab/mmcv/pull/780">PR #780</a></p>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="how_to.html" class="btn btn-neutral float-right" title="Tutorial 11: How to xxx" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="onnx2tensorrt.html" class="btn btn-neutral" title="Tutorial 9: ONNX to TensorRT (Experimental)" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 10: Weight initialization</a><ul>
<li><a class="reference internal" href="#description">Description</a></li>
<li><a class="reference internal" href="#initialize-parameters">Initialize parameters</a></li>
<li><a class="reference internal" href="#usage-of-init-cfg">Usage of init_cfg</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>