


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 12: Test Results Submission &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 13: Useful Hooks" href="useful_hooks.html" />
  <link rel="prev" title="Tutorial 11: How to xxx" href="how_to.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 12: Test Results Submission</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/test_results_submission.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-12-test-results-submission">
<h1>Tutorial 12: Test Results Submission<a class="headerlink" href="#tutorial-12-test-results-submission" title="Permalink to this heading">¶</a></h1>
<section id="panoptic-segmentation-test-results-submission">
<h2>Panoptic segmentation test results submission<a class="headerlink" href="#panoptic-segmentation-test-results-submission" title="Permalink to this heading">¶</a></h2>
<p>The following sections introduce how to produce the prediction results of panoptic segmentation models on the COCO test-dev set and submit the predictions to <a class="reference external" href="https://competitions.codalab.org/competitions/19507">COCO evaluation server</a>.</p>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Download <a class="reference external" href="http://images.cocodataset.org/zips/test2017.zip">COCO test dataset images</a>, <a class="reference external" href="http://images.cocodataset.org/annotations/image_info_test2017.zip">testing image info</a>, and <a class="reference external" href="http://images.cocodataset.org/annotations/panoptic_annotations_trainval2017.zip">panoptic train/val annotations</a>, then unzip them, put ‘test2017’ to <code class="docutils literal notranslate"><span class="pre">data/coco/</span></code>, put json files and annotation files to <code class="docutils literal notranslate"><span class="pre">data/coco/annotations/</span></code>.</p></li>
</ul>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># suppose data/coco/ does not exist</span>
mkdir -pv data/coco/

<span class="c1"># download test2017</span>
wget -P data/coco/ http://images.cocodataset.org/zips/test2017.zip
wget -P data/coco/ http://images.cocodataset.org/annotations/image_info_test2017.zip
wget -P data/coco/ http://images.cocodataset.org/annotations/panoptic_annotations_trainval2017.zip

<span class="c1"># unzip them</span>
unzip data/coco/test2017.zip -d data/coco/
unzip data/coco/image_info_test2017.zip -d data/coco/
unzip data/coco/panoptic_annotations_trainval2017.zip -d data/coco/

<span class="c1"># remove zip files (optional)</span>
rm -rf data/coco/test2017.zip data/coco/image_info_test2017.zip data/coco/panoptic_annotations_trainval2017.zip
</pre></div>
</div>
<ul class="simple">
<li><p>Run the following code to update category information in testing image info. Since the attribute <code class="docutils literal notranslate"><span class="pre">isthing</span></code> is missing in category information of ‘image_info_test-dev2017.json’, we need to update it with the category information in ‘panoptic_val2017.json’.</p></li>
</ul>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>python tools/misc/gen_coco_panoptic_test_info.py data/coco/annotations
</pre></div>
</div>
<p>After completing the above preparations, your directory structure of <code class="docutils literal notranslate"><span class="pre">data</span></code> should be like this:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>data
`-- coco
    |-- annotations
    |   |-- image_info_test-dev2017.json
    |   |-- image_info_test2017.json
    |   |-- panoptic_image_info_test-dev2017.json
    |   |-- panoptic_train2017.json
    |   |-- panoptic_train2017.zip
    |   |-- panoptic_val2017.json
    |   `-- panoptic_val2017.zip
    `-- test2017
</pre></div>
</div>
</section>
<section id="inference-on-coco-test-dev">
<h3>Inference on coco test-dev<a class="headerlink" href="#inference-on-coco-test-dev" title="Permalink to this heading">¶</a></h3>
<p>The commands to perform inference on test2017 are as below:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># test with single gpu</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span> python tools/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --cfg-options data.test.ann_file<span class="o">=</span>data/coco/annotations/panoptic_image_info_test-dev2017.json data.test.img_prefix<span class="o">=</span>data/coco/test2017 <span class="se">\</span>
    --eval-options <span class="nv">jsonfile_prefix</span><span class="o">=</span><span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>/results

<span class="c1"># test with four gpus</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span>,1,3,4 bash tools/dist_test.sh <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="m">4</span> <span class="se">\ </span><span class="c1"># four gpus</span>
    --format-only <span class="se">\</span>
    --cfg-options data.test.ann_file<span class="o">=</span>data/coco/annotations/panoptic_image_info_test-dev2017.json data.test.img_prefix<span class="o">=</span>data/coco/test2017 <span class="se">\</span>
    --eval-options <span class="nv">jsonfile_prefix</span><span class="o">=</span><span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>/results

<span class="c1"># test with slurm</span>
<span class="nv">GPUS</span><span class="o">=</span><span class="m">8</span> tools/slurm_test.sh <span class="se">\</span>
    <span class="si">${</span><span class="nv">Partition</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">JOB_NAME</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --format-only <span class="se">\</span>
    --cfg-options data.test.ann_file<span class="o">=</span>data/coco/annotations/panoptic_image_info_test-dev2017.json data.test.img_prefix<span class="o">=</span>data/coco/test2017 <span class="se">\</span>
    --eval-options <span class="nv">jsonfile_prefix</span><span class="o">=</span><span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>/results
</pre></div>
</div>
<p>Example</p>
<p>Suppose we perform inference on <code class="docutils literal notranslate"><span class="pre">test2017</span></code> using pretrained MaskFormer with ResNet-50 backbone.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># test with single gpu</span>
<span class="nv">CUDA_VISIBLE_DEVICES</span><span class="o">=</span><span class="m">0</span> python tools/test.py <span class="se">\</span>
    configs/maskformer/maskformer_r50_mstrain_16x1_75e_coco.py <span class="se">\</span>
    checkpoints/maskformer_r50_mstrain_16x1_75e_coco_20220221_141956-bc2699cb.pth <span class="se">\</span>
    --format-only <span class="se">\</span>
    --cfg-options data.test.ann_file<span class="o">=</span>data/coco/annotations/panoptic_image_info_test-dev2017.json data.test.img_prefix<span class="o">=</span>data/coco/test2017 <span class="se">\</span>
    --eval-options <span class="nv">jsonfile_prefix</span><span class="o">=</span>work_dirs/maskformer/results
</pre></div>
</div>
</section>
<section id="rename-files-and-zip-results">
<h3>Rename files and zip results<a class="headerlink" href="#rename-files-and-zip-results" title="Permalink to this heading">¶</a></h3>
<p>After inference, the panoptic segmentation results (a json file and a directory where the masks are stored) will be in <code class="docutils literal notranslate"><span class="pre">WORK_DIR</span></code>. We should rename them according to the naming convention described on <a class="reference external" href="https://cocodataset.org/#upload">COCO’s Website</a>. Finally, we need to compress the json and the directory where the masks are stored into a zip file, and rename the zip file according to the naming convention. Note that the zip file should <strong>directly</strong> contains the above two files.</p>
<p>The commands to rename files and zip results:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># In WORK_DIR, we have panoptic segmentation results: &#39;panoptic&#39; and &#39;results.panoptic.json&#39;.</span>
<span class="nb">cd</span> <span class="si">${</span><span class="nv">WORK_DIR</span><span class="si">}</span>

<span class="c1"># replace &#39;[algorithm_name]&#39; with the name of algorithm you used.</span>
mv ./panoptic ./panoptic_test-dev2017_<span class="o">[</span>algorithm_name<span class="o">]</span>_results
mv ./results.panoptic.json ./panoptic_test-dev2017_<span class="o">[</span>algorithm_name<span class="o">]</span>_results.json
zip panoptic_test-dev2017_<span class="o">[</span>algorithm_name<span class="o">]</span>_results.zip -ur panoptic_test-dev2017_<span class="o">[</span>algorithm_name<span class="o">]</span>_results panoptic_test-dev2017_<span class="o">[</span>algorithm_name<span class="o">]</span>_results.json
</pre></div>
</div>
</section>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="useful_hooks.html" class="btn btn-neutral float-right" title="Tutorial 13: Useful Hooks" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="how_to.html" class="btn btn-neutral" title="Tutorial 11: How to xxx" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 12: Test Results Submission</a><ul>
<li><a class="reference internal" href="#panoptic-segmentation-test-results-submission">Panoptic segmentation test results submission</a><ul>
<li><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li><a class="reference internal" href="#inference-on-coco-test-dev">Inference on coco test-dev</a></li>
<li><a class="reference internal" href="#rename-files-and-zip-results">Rename files and zip results</a></li>
</ul>
</li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>