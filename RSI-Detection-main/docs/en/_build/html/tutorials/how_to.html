


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 11: How to xxx &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 12: Test Results Submission" href="test_results_submission.html" />
  <link rel="prev" title="Tutorial 10: Weight initialization" href="init_cfg.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 11: How to xxx</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/how_to.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-11-how-to-xxx">
<h1>Tutorial 11: How to xxx<a class="headerlink" href="#tutorial-11-how-to-xxx" title="Permalink to this heading">¶</a></h1>
<p>This tutorial collects answers to any <code class="docutils literal notranslate"><span class="pre">How</span> <span class="pre">to</span> <span class="pre">xxx</span> <span class="pre">with</span> <span class="pre">MMDetection</span></code>. Feel free to update this doc if you meet new questions about <code class="docutils literal notranslate"><span class="pre">How</span> <span class="pre">to</span></code> and find the answers!</p>
<section id="use-backbone-network-through-mmclassification">
<h2>Use backbone network through MMClassification<a class="headerlink" href="#use-backbone-network-through-mmclassification" title="Permalink to this heading">¶</a></h2>
<p>The model registry in MMDet, MMCls, MMSeg all inherit from the root registry in MMCV. This allows these repositories to directly use the modules already implemented by each other. Therefore, users can use backbone networks from MMClassification in MMDetection without implementing a network that already exists in MMClassification.</p>
<section id="use-backbone-network-implemented-in-mmclassification">
<h3>Use backbone network implemented in MMClassification<a class="headerlink" href="#use-backbone-network-implemented-in-mmclassification" title="Permalink to this heading">¶</a></h3>
<p>Suppose you want to use <code class="docutils literal notranslate"><span class="pre">MobileNetV3-small</span></code> as the backbone network of <code class="docutils literal notranslate"><span class="pre">RetinaNet</span></code>, the example config is as the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;../_base_/models/retinanet_r50_fpn.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/datasets/coco_detection.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/schedules/schedule_1x.py&#39;</span><span class="p">,</span> <span class="s1">&#39;../_base_/default_runtime.py&#39;</span>
<span class="p">]</span>
<span class="c1"># please install mmcls&gt;=0.20.0</span>
<span class="c1"># import mmcls.models to trigger register_module in mmcls</span>
<span class="n">custom_imports</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;mmcls.models&#39;</span><span class="p">],</span> <span class="n">allow_failed_imports</span><span class="o">=</span><span class="bp">False</span><span class="p">)</span>
<span class="n">pretrained</span> <span class="o">=</span> <span class="s1">&#39;https://download.openmmlab.com/mmclassification/v0/mobilenet_v3/convert/mobilenet_v3_small-8427ecf0.pth&#39;</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">_delete_</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span> <span class="c1"># Delete the backbone field in _base_</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;mmcls.MobileNetV3&#39;</span><span class="p">,</span> <span class="c1"># Using MobileNetV3 from mmcls</span>
        <span class="n">arch</span><span class="o">=</span><span class="s1">&#39;small&#39;</span><span class="p">,</span>
        <span class="n">out_indices</span><span class="o">=</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">11</span><span class="p">),</span> <span class="c1"># Modify out_indices</span>
        <span class="n">init_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pretrained&#39;</span><span class="p">,</span>
            <span class="n">checkpoint</span><span class="o">=</span><span class="n">pretrained</span><span class="p">,</span>
            <span class="n">prefix</span><span class="o">=</span><span class="s1">&#39;backbone.&#39;</span><span class="p">)),</span> <span class="c1"># The pre-trained weights of backbone network in MMCls have prefix=&#39;backbone.&#39;. The prefix in the keys will be removed so that these weights can be normally loaded.</span>
    <span class="c1"># Modify in_channels</span>
    <span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">in_channels</span><span class="o">=</span><span class="p">[</span><span class="mi">24</span><span class="p">,</span> <span class="mi">48</span><span class="p">,</span> <span class="mi">96</span><span class="p">],</span> <span class="n">start_level</span><span class="o">=</span><span class="mi">0</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="use-backbone-network-in-timm-through-mmclassification">
<h3>Use backbone network in TIMM through MMClassification<a class="headerlink" href="#use-backbone-network-in-timm-through-mmclassification" title="Permalink to this heading">¶</a></h3>
<p>MMClassification also provides a wrapper for the PyTorch Image Models (timm) backbone network, users can directly use the backbone network in timm through MMClassification. Suppose you want to use EfficientNet-B1 as the backbone network of RetinaNet, the example config is as the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># https://github.com/open-mmlab/rsidetection/blob/master/configs/timm_example/retinanet_timm_efficientnet_b1_fpn_1x_coco.py</span>

<span class="n">_base_</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;../_base_/models/retinanet_r50_fpn.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/datasets/coco_detection.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/schedules/schedule_1x.py&#39;</span><span class="p">,</span> <span class="s1">&#39;../_base_/default_runtime.py&#39;</span>
<span class="p">]</span>

<span class="c1"># please install mmcls&gt;=0.20.0</span>
<span class="c1"># import mmcls.models to trigger register_module in mmcls</span>
<span class="n">custom_imports</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;mmcls.models&#39;</span><span class="p">],</span> <span class="n">allow_failed_imports</span><span class="o">=</span><span class="bp">False</span><span class="p">)</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">_delete_</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span> <span class="c1"># Delete the backbone field in _base_</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;mmcls.TIMMBackbone&#39;</span><span class="p">,</span> <span class="c1"># Using timm from mmcls</span>
        <span class="n">model_name</span><span class="o">=</span><span class="s1">&#39;efficientnet_b1&#39;</span><span class="p">,</span>
        <span class="n">features_only</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
        <span class="n">pretrained</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
        <span class="n">out_indices</span><span class="o">=</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">)),</span> <span class="c1"># Modify out_indices</span>
    <span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">in_channels</span><span class="o">=</span><span class="p">[</span><span class="mi">24</span><span class="p">,</span> <span class="mi">40</span><span class="p">,</span> <span class="mi">112</span><span class="p">,</span> <span class="mi">320</span><span class="p">]))</span> <span class="c1"># Modify in_channels</span>

<span class="n">optimizer</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;SGD&#39;</span><span class="p">,</span> <span class="n">lr</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.9</span><span class="p">,</span> <span class="n">weight_decay</span><span class="o">=</span><span class="mf">0.0001</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type='mmcls.TIMMBackbone'</span></code> means use the <code class="docutils literal notranslate"><span class="pre">TIMMBackbone</span></code> class from MMClassification in MMDetection, and the model used is <code class="docutils literal notranslate"><span class="pre">EfficientNet-B1</span></code>, where <code class="docutils literal notranslate"><span class="pre">mmcls</span></code> means the MMClassification repo and <code class="docutils literal notranslate"><span class="pre">TIMMBackbone</span></code> means the TIMMBackbone wrapper implemented in MMClassification.</p>
<p>For the principle of the Hierarchy Registry, please refer to the <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/docs/en/understand_mmcv/registry.md#hierarchy-registry">MMCV document</a>. For how to use other backbones in MMClassification, you can refer to the <a class="reference external" href="https://github.com/open-mmlab/mmclassification/blob/master/docs/en/tutorials/config.md">MMClassification document</a>.</p>
</section>
</section>
<section id="use-mosaic-augmentation">
<h2>Use Mosaic augmentation<a class="headerlink" href="#use-mosaic-augmentation" title="Permalink to this heading">¶</a></h2>
<p>If you want to use <code class="docutils literal notranslate"><span class="pre">Mosaic</span></code> in training, please make sure that you use <code class="docutils literal notranslate"><span class="pre">MultiImageMixDataset</span></code> at the same time. Taking the ‘Faster R-CNN’ algorithm as an example, you should modify the values of <code class="docutils literal notranslate"><span class="pre">train_pipeline</span></code> and <code class="docutils literal notranslate"><span class="pre">train_dataset</span></code> in the config as below:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span># Open configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py directly and add the following fields
data_root = &#39;data/coco/&#39;
dataset_type = &#39;CocoDataset&#39;
img_scale=(1333, 800)​
img_norm_cfg = dict(
    mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)

train_pipeline = [
    dict(type=&#39;Mosaic&#39;, img_scale=img_scale, pad_val=114.0),
    dict(
        type=&#39;RandomAffine&#39;,
        scaling_ratio_range=(0.1, 2),
        border=(-img_scale[0] // 2, -img_scale[1] // 2)), # The image will be enlarged by 4 times after Mosaic processing,so we use affine transformation to restore the image size.
    dict(type=&#39;RandomFlip&#39;, flip_ratio=0.5),
    dict(type=&#39;Normalize&#39;, **img_norm_cfg),
    dict(type=&#39;Pad&#39;, size_divisor=32),
    dict(type=&#39;DefaultFormatBundle&#39;),
    dict(type=&#39;Collect&#39;, keys=[&#39;img&#39;, &#39;gt_bboxes&#39;, &#39;gt_labels&#39;])
]

train_dataset = dict(
    _delete_ = True, # remove unnecessary Settings
    type=&#39;MultiImageMixDataset&#39;,
    dataset=dict(
        type=dataset_type,
        ann_file=data_root + &#39;annotations/instances_train2017.json&#39;,
        img_prefix=data_root + &#39;train2017/&#39;,
        pipeline=[
            dict(type=&#39;LoadImageFromFile&#39;),
            dict(type=&#39;LoadAnnotations&#39;, with_bbox=True)
        ],
        filter_empty_gt=False,
    ),
    pipeline=train_pipeline
    )
​
data = dict(
    train=train_dataset
    )
</pre></div>
</div>
</section>
<section id="unfreeze-backbone-network-after-freezing-the-backbone-in-the-config">
<h2>Unfreeze backbone network after freezing the backbone in the config<a class="headerlink" href="#unfreeze-backbone-network-after-freezing-the-backbone-in-the-config" title="Permalink to this heading">¶</a></h2>
<p>If you have freezed the backbone network in the config and want to unfreeze it after some epoches, you can write a hook function to do it.  Taking the Faster R-CNN with the resnet backbone as an example, you can freeze one stage of the backbone network and  add a <code class="docutils literal notranslate"><span class="pre">custom_hooks</span></code> in the config as below:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;../_base_/models/faster_rcnn_r50_fpn.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/datasets/coco_detection.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/schedules/schedule_1x.py&#39;</span><span class="p">,</span> <span class="s1">&#39;../_base_/default_runtime.py&#39;</span>
<span class="p">]</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="c1"># freeze one stage of the backbone network.</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">frozen_stages</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
<span class="p">)</span>
<span class="n">custom_hooks</span> <span class="o">=</span> <span class="p">[</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s2">&quot;UnfreezeBackboneEpochBasedHook&quot;</span><span class="p">,</span> <span class="n">unfreeze_epoch</span><span class="o">=</span><span class="mi">1</span><span class="p">)]</span>
</pre></div>
</div>
<p>Meanwhile write the hook class <code class="docutils literal notranslate"><span class="pre">UnfreezeBackboneEpochBasedHook</span></code> in <code class="docutils literal notranslate"><span class="pre">rsidet/core/hook/unfreeze_backbone_epoch_based_hook.py</span></code></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">mmcv.parallel</span> <span class="kn">import</span> <span class="n">is_module_wrapper</span>
<span class="kn">from</span> <span class="nn">mmcv.runner.hooks</span> <span class="kn">import</span> <span class="n">HOOKS</span><span class="p">,</span> <span class="n">Hook</span>


<span class="nd">@HOOKS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">UnfreezeBackboneEpochBasedHook</span><span class="p">(</span><span class="n">Hook</span><span class="p">):</span>
    <span class="sd">&quot;&quot;&quot;Unfreeze backbone network Hook.</span>

<span class="sd">    Args:</span>
<span class="sd">        unfreeze_epoch (int): The epoch unfreezing the backbone network.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">unfreeze_epoch</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">unfreeze_epoch</span> <span class="o">=</span> <span class="n">unfreeze_epoch</span>

    <span class="k">def</span> <span class="nf">before_train_epoch</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">runner</span><span class="p">):</span>
        <span class="c1"># Unfreeze the backbone network.</span>
        <span class="c1"># Only valid for resnet.</span>
        <span class="k">if</span> <span class="n">runner</span><span class="o">.</span><span class="n">epoch</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">unfreeze_epoch</span><span class="p">:</span>
            <span class="n">model</span> <span class="o">=</span> <span class="n">runner</span><span class="o">.</span><span class="n">model</span>
            <span class="k">if</span> <span class="n">is_module_wrapper</span><span class="p">(</span><span class="n">model</span><span class="p">):</span>
                <span class="n">model</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">module</span>
            <span class="n">backbone</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">backbone</span>
            <span class="k">if</span> <span class="n">backbone</span><span class="o">.</span><span class="n">frozen_stages</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">backbone</span><span class="o">.</span><span class="n">deep_stem</span><span class="p">:</span>
                    <span class="n">backbone</span><span class="o">.</span><span class="n">stem</span><span class="o">.</span><span class="n">train</span><span class="p">()</span>
                    <span class="k">for</span> <span class="n">param</span> <span class="ow">in</span> <span class="n">backbone</span><span class="o">.</span><span class="n">stem</span><span class="o">.</span><span class="n">parameters</span><span class="p">():</span>
                        <span class="n">param</span><span class="o">.</span><span class="n">requires_grad</span> <span class="o">=</span> <span class="bp">True</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">backbone</span><span class="o">.</span><span class="n">norm1</span><span class="o">.</span><span class="n">train</span><span class="p">()</span>
                    <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="p">[</span><span class="n">backbone</span><span class="o">.</span><span class="n">conv1</span><span class="p">,</span> <span class="n">backbone</span><span class="o">.</span><span class="n">norm1</span><span class="p">]:</span>
                        <span class="k">for</span> <span class="n">param</span> <span class="ow">in</span> <span class="n">m</span><span class="o">.</span><span class="n">parameters</span><span class="p">():</span>
                            <span class="n">param</span><span class="o">.</span><span class="n">requires_grad</span> <span class="o">=</span> <span class="bp">True</span>

            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">backbone</span><span class="o">.</span><span class="n">frozen_stages</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                <span class="n">m</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">backbone</span><span class="p">,</span> <span class="n">f</span><span class="s1">&#39;layer{i}&#39;</span><span class="p">)</span>
                <span class="n">m</span><span class="o">.</span><span class="n">train</span><span class="p">()</span>
                <span class="k">for</span> <span class="n">param</span> <span class="ow">in</span> <span class="n">m</span><span class="o">.</span><span class="n">parameters</span><span class="p">():</span>
                    <span class="n">param</span><span class="o">.</span><span class="n">requires_grad</span> <span class="o">=</span> <span class="bp">True</span>
</pre></div>
</div>
</section>
<section id="get-the-channels-of-a-new-backbone">
<h2>Get the channels of a new backbone<a class="headerlink" href="#get-the-channels-of-a-new-backbone" title="Permalink to this heading">¶</a></h2>
<p>If you want to get the channels of a new backbone, you can build this backbone alone and input a pseudo image to get each stage output.</p>
<p>Take <code class="docutils literal notranslate"><span class="pre">ResNet</span></code> as an example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">rsidet.models</span> <span class="kn">import</span> <span class="n">ResNet</span>
<span class="kn">import</span> <span class="nn">torch</span>
<span class="bp">self</span> <span class="o">=</span> <span class="n">ResNet</span><span class="p">(</span><span class="n">depth</span><span class="o">=</span><span class="mi">18</span><span class="p">)</span>
<span class="bp">self</span><span class="o">.</span><span class="n">eval</span><span class="p">()</span>
<span class="n">inputs</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">rand</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">32</span><span class="p">,</span> <span class="mi">32</span><span class="p">)</span>
<span class="n">level_outputs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="n">inputs</span><span class="p">)</span>
<span class="k">for</span> <span class="n">level_out</span> <span class="ow">in</span> <span class="n">level_outputs</span><span class="p">:</span>
    <span class="k">print</span><span class="p">(</span><span class="nb">tuple</span><span class="p">(</span><span class="n">level_out</span><span class="o">.</span><span class="n">shape</span><span class="p">))</span>

</pre></div>
</div>
<p>Output of the above script is as below:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">8</span><span class="p">)</span>
<span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">128</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span>
<span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">256</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">512</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>Users can get the channels of the new backbone by Replacing the <code class="docutils literal notranslate"><span class="pre">ResNet(depth=18)</span></code> in this script with their customized backbone.</p>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="test_results_submission.html" class="btn btn-neutral float-right" title="Tutorial 12: Test Results Submission" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="init_cfg.html" class="btn btn-neutral" title="Tutorial 10: Weight initialization" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 11: How to xxx</a><ul>
<li><a class="reference internal" href="#use-backbone-network-through-mmclassification">Use backbone network through MMClassification</a><ul>
<li><a class="reference internal" href="#use-backbone-network-implemented-in-mmclassification">Use backbone network implemented in MMClassification</a></li>
<li><a class="reference internal" href="#use-backbone-network-in-timm-through-mmclassification">Use backbone network in TIMM through MMClassification</a></li>
</ul>
</li>
<li><a class="reference internal" href="#use-mosaic-augmentation">Use Mosaic augmentation</a></li>
<li><a class="reference internal" href="#unfreeze-backbone-network-after-freezing-the-backbone-in-the-config">Unfreeze backbone network after freezing the backbone in the config</a></li>
<li><a class="reference internal" href="#get-the-channels-of-a-new-backbone">Get the channels of a new backbone</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>