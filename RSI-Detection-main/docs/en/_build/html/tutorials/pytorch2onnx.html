


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 8: Pytorch to ONNX (Experimental) &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 9: ONNX to TensorRT (Experimental)" href="onnx2tensorrt.html" />
  <link rel="prev" title="Tutorial 7: Finetuning Models" href="finetune.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 8: Pytorch to ONNX (Experimental)</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/pytorch2onnx.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-8-pytorch-to-onnx-experimental">
<h1>Tutorial 8: Pytorch to ONNX (Experimental)<a class="headerlink" href="#tutorial-8-pytorch-to-onnx-experimental" title="Permalink to this heading">¶</a></h1>
<section id="try-the-new-mmdeploy-to-deploy-your-model">
<h2><a class="reference external" href="https://mmdeploy.readthedocs.io/">Try the new MMDeploy to deploy your model</a><a class="headerlink" href="#try-the-new-mmdeploy-to-deploy-your-model" title="Permalink to this heading">¶</a></h2>
</section>
<!-- TOC -->
<ul class="simple">
<li><p><a class="reference internal" href="#tutorial-8-pytorch-to-onnx-experimental"><span class="std std-doc">Tutorial 8: Pytorch to ONNX (Experimental)</span></a></p>
<ul>
<li><p><a class="reference internal" href="#how-to-convert-models-from-pytorch-to-onnx"><span class="std std-doc">How to convert models from Pytorch to ONNX</span></a></p>
<ul>
<li><p><a class="reference internal" href="#prerequisite"><span class="std std-doc">Prerequisite</span></a></p></li>
<li><p><a class="reference internal" href="#usage"><span class="std std-doc">Usage</span></a></p></li>
<li><p><a class="reference internal" href="#description-of-all-arguments"><span class="std std-doc">Description of all arguments</span></a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#how-to-evaluate-the-exported-models"><span class="std std-doc">How to evaluate the exported models</span></a></p>
<ul>
<li><p><a class="reference internal" href="#id1"><span class="std std-doc">Prerequisite</span></a></p></li>
<li><p><a class="reference internal" href="#id2"><span class="std std-doc">Usage</span></a></p></li>
<li><p><a class="reference internal" href="#id3"><span class="std std-doc">Description of all arguments</span></a></p></li>
<li><p><a class="reference internal" href="#results-and-models"><span class="std std-doc">Results and Models</span></a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#list-of-supported-models-exportable-to-onnx"><span class="std std-doc">List of supported models exportable to ONNX</span></a></p></li>
<li><p><a class="reference internal" href="#the-parameters-of-non-maximum-suppression-in-onnx-export"><span class="std std-doc">The Parameters of Non-Maximum Suppression in ONNX Export</span></a></p></li>
<li><p><a class="reference internal" href="#reminders"><span class="std std-doc">Reminders</span></a></p></li>
<li><p><a class="reference internal" href="#faqs"><span class="std std-doc">FAQs</span></a></p></li>
</ul>
</li>
</ul>
<!-- TOC -->
<section id="how-to-convert-models-from-pytorch-to-onnx">
<h2>How to convert models from Pytorch to ONNX<a class="headerlink" href="#how-to-convert-models-from-pytorch-to-onnx" title="Permalink to this heading">¶</a></h2>
<section id="prerequisite">
<h3>Prerequisite<a class="headerlink" href="#prerequisite" title="Permalink to this heading">¶</a></h3>
<ol class="arabic simple">
<li><p>Install the prerequisites following <a class="reference internal" href="../get_started.html"><span class="doc std std-doc">get_started.md/Prepare environment</span></a>.</p></li>
<li><p>Build custom operators for ONNX Runtime and install MMCV manually following <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/docs/en/deployment/onnxruntime_op.md/#how-to-build-custom-operators-for-onnx-runtime">How to build custom operators for ONNX Runtime</a></p></li>
<li><p>Install MMdetection manually following steps 2-3 in <a class="reference internal" href="../get_started.html"><span class="doc std std-doc">get_started.md/Install MMdetection</span></a>.</p></li>
</ol>
</section>
<section id="usage">
<h3>Usage<a class="headerlink" href="#usage" title="Permalink to this heading">¶</a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/deployment/pytorch2onnx.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">CHECKPOINT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --output-file <span class="si">${</span><span class="nv">OUTPUT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --input-img <span class="si">${</span><span class="nv">INPUT_IMAGE_PATH</span><span class="si">}</span> <span class="se">\</span>
    --shape <span class="si">${</span><span class="nv">IMAGE_SHAPE</span><span class="si">}</span> <span class="se">\</span>
    --test-img <span class="si">${</span><span class="nv">TEST_IMAGE_PATH</span><span class="si">}</span> <span class="se">\</span>
    --opset-version <span class="si">${</span><span class="nv">OPSET_VERSION</span><span class="si">}</span> <span class="se">\</span>
    --cfg-options <span class="si">${</span><span class="nv">CFG_OPTIONS</span><span class="si">}</span>
    --dynamic-export <span class="se">\</span>
    --show <span class="se">\</span>
    --verify <span class="se">\</span>
    --simplify <span class="se">\</span>
</pre></div>
</div>
</section>
<section id="description-of-all-arguments">
<h3>Description of all arguments<a class="headerlink" href="#description-of-all-arguments" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">config</span></code> : The path of a model config file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">checkpoint</span></code> : The path of a model checkpoint file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--output-file</span></code>: The path of output ONNX model. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">tmp.onnx</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--input-img</span></code>: The path of an input image for tracing and conversion. By default, it will be set to <code class="docutils literal notranslate"><span class="pre">tests/data/color.jpg</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--shape</span></code>: The height and width of input tensor to the model. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">800</span> <span class="pre">1216</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--test-img</span></code> : The path of an image to verify the exported ONNX model. By default, it will be set to <code class="docutils literal notranslate"><span class="pre">None</span></code>, meaning it will use <code class="docutils literal notranslate"><span class="pre">--input-img</span></code> for verification.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--opset-version</span></code> : The opset version of ONNX. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">11</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--dynamic-export</span></code>: Determines whether to export ONNX model with dynamic input and output shapes. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show</span></code>: Determines whether to print the architecture of the exported model and whether to show detection outputs when <code class="docutils literal notranslate"><span class="pre">--verify</span></code> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--verify</span></code>: Determines whether to verify the correctness of an exported model. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--simplify</span></code>: Determines whether to simplify the exported ONNX model. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code>: Override some settings in the used config file, the key-value pair in <code class="docutils literal notranslate"><span class="pre">xxx=yyy</span></code> format will be merged into config file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--skip-postprocess</span></code>: Determines whether export model without post process. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>. Notice: This is an experimental option. Only work for some single stage models. Users need to implement the post-process by themselves. We do not guarantee the correctness of the exported model.</p></li>
</ul>
<p>Example:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/deployment/pytorch2onnx.py <span class="se">\</span>
    configs/yolo/yolov3_d53_mstrain-608_273e_coco.py <span class="se">\</span>
    checkpoints/yolo/yolov3_d53_mstrain-608_273e_coco.pth <span class="se">\</span>
    --output-file checkpoints/yolo/yolov3_d53_mstrain-608_273e_coco.onnx <span class="se">\</span>
    --input-img demo/demo.jpg <span class="se">\</span>
    --test-img tests/data/color.jpg <span class="se">\</span>
    --shape <span class="m">608</span> <span class="m">608</span> <span class="se">\</span>
    --show <span class="se">\</span>
    --verify <span class="se">\</span>
    --dynamic-export <span class="se">\</span>
    --cfg-options <span class="se">\</span>
      model.test_cfg.deploy_nms_pre<span class="o">=</span>-1 <span class="se">\</span>
</pre></div>
</div>
</section>
</section>
<section id="how-to-evaluate-the-exported-models">
<h2>How to evaluate the exported models<a class="headerlink" href="#how-to-evaluate-the-exported-models" title="Permalink to this heading">¶</a></h2>
<p>We prepare a tool <code class="docutils literal notranslate"><span class="pre">tools/deplopyment/test.py</span></code> to evaluate ONNX models with ONNXRuntime and TensorRT.</p>
<section id="id1">
<h3>Prerequisite<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<ul>
<li><p>Install onnx and onnxruntime (CPU version)</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>pip install onnx <span class="nv">onnxruntime</span><span class="o">==</span><span class="m">1</span>.5.1
</pre></div>
</div>
</li>
<li><p>If you want to run the model on GPU, please remove the CPU version before using the GPU version.</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>pip uninstall onnxruntime
pip install onnxruntime-gpu
</pre></div>
</div>
<p>Note: onnxruntime-gpu is version-dependent on CUDA and CUDNN, please ensure that your
environment meets the requirements.</p>
</li>
<li><p>Build custom operators for ONNX Runtime following <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/docs/en/deployment/onnxruntime_op.md/#how-to-build-custom-operators-for-onnx-runtime">How to build custom operators for ONNX Runtime</a></p></li>
<li><p>Install TensorRT by referring to <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/deployment/tensorrt_plugin.html#how-to-build-tensorrt-plugins-in-mmcv">How to build TensorRT plugins in MMCV</a> (optional)</p></li>
</ul>
</section>
<section id="id2">
<h3>Usage<a class="headerlink" href="#id2" title="Permalink to this heading">¶</a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/deployment/test.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG_FILE</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">MODEL_FILE</span><span class="si">}</span> <span class="se">\</span>
    --out <span class="si">${</span><span class="nv">OUTPUT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --backend <span class="si">${</span><span class="nv">BACKEND</span><span class="si">}</span> <span class="se">\</span>
    --format-only <span class="si">${</span><span class="nv">FORMAT_ONLY</span><span class="si">}</span> <span class="se">\</span>
    --eval <span class="si">${</span><span class="nv">EVALUATION_METRICS</span><span class="si">}</span> <span class="se">\</span>
    --show-dir <span class="si">${</span><span class="nv">SHOW_DIRECTORY</span><span class="si">}</span> <span class="se">\</span>
    ----show-score-thr <span class="si">${</span><span class="nv">SHOW_SCORE_THRESHOLD</span><span class="si">}</span> <span class="se">\</span>
    ----cfg-options <span class="si">${</span><span class="nv">CFG_OPTIONS</span><span class="si">}</span> <span class="se">\</span>
    ----eval-options <span class="si">${</span><span class="nv">EVALUATION_OPTIONS</span><span class="si">}</span> <span class="se">\</span>
</pre></div>
</div>
</section>
<section id="id3">
<h3>Description of all arguments<a class="headerlink" href="#id3" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">config</span></code>: The path of a model config file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">model</span></code>: The path of an input model file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--out</span></code>: The path of output result file in pickle format.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--backend</span></code>: Backend for input model to run and should be <code class="docutils literal notranslate"><span class="pre">onnxruntime</span></code> or <code class="docutils literal notranslate"><span class="pre">tensorrt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--format-only</span></code> : Format the output results without perform evaluation. It is useful when you want to format the result to a specific format and submit it to the test server. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--eval</span></code>: Evaluation metrics, which depends on the dataset, e.g., “bbox”, “segm”, “proposal” for COCO, and “mAP”, “recall” for PASCAL VOC.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show-dir</span></code>: Directory where painted images will be saved</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show-score-thr</span></code>: Score threshold. Default is set to <code class="docutils literal notranslate"><span class="pre">0.3</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code>: Override some settings in the used config file, the key-value pair in <code class="docutils literal notranslate"><span class="pre">xxx=yyy</span></code> format will be merged into config file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--eval-options</span></code>: Custom options for evaluation, the key-value pair in <code class="docutils literal notranslate"><span class="pre">xxx=yyy</span></code> format will be kwargs for <code class="docutils literal notranslate"><span class="pre">dataset.evaluate()</span></code> function</p></li>
</ul>
<p>Notes:</p>
<ul>
<li><p>If the deployed backend platform is TensorRT, please add environment variables before running the file:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">export</span> <span class="nv">ONNX_BACKEND</span><span class="o">=</span>MMCVTensorRT
</pre></div>
</div>
</li>
<li><p>If you want to use the <code class="docutils literal notranslate"><span class="pre">--dynamic-export</span></code> parameter in the TensorRT backend to export ONNX, please remove the <code class="docutils literal notranslate"><span class="pre">--simplify</span></code> parameter, and vice versa.</p></li>
</ul>
</section>
<section id="results-and-models">
<h3>Results and Models<a class="headerlink" href="#results-and-models" title="Permalink to this heading">¶</a></h3>
<table border="1" class="docutils">
	<tr>
	    <th align="center">Model</th>
	    <th align="center">Config</th>
	    <th align="center">Metric</th>
	    <th align="center">PyTorch</th>
	    <th align="center">ONNX Runtime</th>
	    <th align="center">TensorRT</th>
	</tr >
  <tr >
	    <td align="center">FCOS</td>
	    <td align="center"><code>configs/fcos/fcos_r50_caffe_fpn_gn-head_4x4_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">36.6</td>
	    <td align="center">36.5</td>
	    <td align="center">36.3</td>
	</tr>
  <tr >
	    <td align="center">FSAF</td>
	    <td align="center"><code>configs/fsaf/fsaf_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">36.0</td>
	    <td align="center">36.0</td>
	    <td align="center">35.9</td>
	</tr>
  <tr >
	    <td align="center">RetinaNet</td>
	    <td align="center"><code>configs/retinanet/retinanet_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">36.5</td>
	    <td align="center">36.4</td>
	    <td align="center">36.3</td>
	</tr>
	<tr >
	    <td align="center" align="center" >SSD</td>
	    <td align="center" align="center"><code>configs/ssd/ssd300_coco.py</code></td>
	    <td align="center" align="center">Box AP</td>
	    <td align="center" align="center">25.6</td>
	    <td align="center" align="center">25.6</td>
	    <td align="center" align="center">25.6</td>
	</tr>
  <tr >
	    <td align="center">YOLOv3</td>
	    <td align="center"><code>configs/yolo/yolov3_d53_mstrain-608_273e_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">33.5</td>
	    <td align="center">33.5</td>
	    <td align="center">33.5</td>
	</tr>
  <tr >
	    <td align="center">Faster R-CNN</td>
	    <td align="center"><code>configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">37.4</td>
	    <td align="center">37.4</td>
	    <td align="center">37.0</td>
	</tr>
  <tr >
	    <td align="center">Cascade R-CNN</td>
	    <td align="center"><code>configs/cascade_rcnn/cascade_rcnn_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">40.3</td>
	    <td align="center">40.3</td>
	    <td align="center">40.1</td>
	</tr>
<tr >
	    <td align="center" rowspan="2">Mask R-CNN</td>
	    <td align="center" rowspan="2"><code>configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">38.2</td>
	    <td align="center">38.1</td>
	    <td align="center">37.7</td>
	</tr>
	<tr>
	    <td align="center">Mask AP</td>
	    <td align="center">34.7</td>
	    <td align="center">33.7</td>
	    <td align="center">33.3</td>
	</tr>
  <tr >
	    <td align="center" rowspan="2">Cascade Mask R-CNN</td>
	    <td align="center" rowspan="2"><code>configs/cascade_rcnn/cascade_mask_rcnn_r50_fpn_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">41.2</td>
	    <td align="center">41.2</td>
	    <td align="center">40.9</td>
	</tr>
	<tr>
	    <td align="center">Mask AP</td>
	    <td align="center">35.9</td>
	    <td align="center">34.8</td>
	    <td align="center">34.5</td>
	</tr>
<tr >
	    <td align="center">CornerNet</td>
	    <td align="center"><code>configs/cornernet/cornernet_hourglass104_mstest_10x5_210e_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">40.6</td>
	    <td align="center">40.4</td>
		<td align="center">-</td>
	</tr>
  <tr >
	    <td align="center">DETR</td>
	    <td align="center"><code>configs/detr/detr_r50_8x2_150e_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">40.1</td>
	    <td align="center">40.1</td>
		<td align="center">-</td>
  </tr>
  <tr >
	    <td align="center" rowspan="2">PointRend</td>
	    <td align="center" rowspan="2"><code>configs/point_rend/point_rend_r50_caffe_fpn_mstrain_1x_coco.py</code></td>
	    <td align="center">Box AP</td>
	    <td align="center">38.4</td>
	    <td align="center">38.4</td>
	    <td align="center">-</td>
  </tr>
  <tr>
	    <td align="center">Mask AP</td>
	    <td align="center">36.3</td>
	    <td align="center">35.2</td>
	    <td align="center">-</td>
  </tr>
</table>
<p>Notes:</p>
<ul class="simple">
<li><p>All ONNX models are evaluated with dynamic shape on coco dataset and images are preprocessed according to the original config file. Note that CornerNet is evaluated without test-time flip, since currently only single-scale evaluation is supported with ONNX Runtime.</p></li>
<li><p>Mask AP of Mask R-CNN drops by 1% for ONNXRuntime. The main reason is that the predicted masks are directly interpolated to original image in PyTorch, while they are at first interpolated to the preprocessed input image of the model and then to original image in other backend.</p></li>
</ul>
</section>
</section>
<section id="list-of-supported-models-exportable-to-onnx">
<h2>List of supported models exportable to ONNX<a class="headerlink" href="#list-of-supported-models-exportable-to-onnx" title="Permalink to this heading">¶</a></h2>
<p>The table below lists the models that are guaranteed to be exportable to ONNX and runnable in ONNX Runtime.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th style="text-align: center;">Model</th>
<th style="text-align: center;">Config</th>
<th style="text-align: center;">Dynamic Shape</th>
<th style="text-align: center;">Batch Inference</th>
<th style="text-align: center;">Note</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: center;">FCOS</td>
<td style="text-align: center;"><code>configs/fcos/fcos_r50_caffe_fpn_gn-head_4x4_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">FSAF</td>
<td style="text-align: center;"><code>configs/fsaf/fsaf_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">RetinaNet</td>
<td style="text-align: center;"><code>configs/retinanet/retinanet_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">SSD</td>
<td style="text-align: center;"><code>configs/ssd/ssd300_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">YOLOv3</td>
<td style="text-align: center;"><code>configs/yolo/yolov3_d53_mstrain-608_273e_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;"><code>configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Cascade R-CNN</td>
<td style="text-align: center;"><code>configs/cascade_rcnn/cascade_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Mask R-CNN</td>
<td style="text-align: center;"><code>configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Cascade Mask R-CNN</td>
<td style="text-align: center;"><code>configs/cascade_rcnn/cascade_mask_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">CornerNet</td>
<td style="text-align: center;"><code>configs/cornernet/cornernet_hourglass104_mstest_10x5_210e_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">N</td>
<td style="text-align: center;">no flip, no batch inference, tested with torch==1.7.0 and onnxruntime==1.5.1.</td>
</tr>
<tr>
<td style="text-align: center;">DETR</td>
<td style="text-align: center;"><code>configs/detr/detr_r50_8x2_150e_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">batch inference is <em>not recommended</em></td>
</tr>
<tr>
<td style="text-align: center;">PointRend</td>
<td style="text-align: center;"><code>configs/point_rend/point_rend_r50_caffe_fpn_mstrain_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
</tbody>
</table>
<p>Notes:</p>
<ul class="simple">
<li><p>Minimum required version of MMCV is <code class="docutils literal notranslate"><span class="pre">1.3.5</span></code></p></li>
<li><p><em>All models above are tested with Pytorch==1.6.0 and onnxruntime==1.5.1</em>, except for CornerNet. For more details about the
torch version when exporting CornerNet to ONNX, which involves <code class="docutils literal notranslate"><span class="pre">mmcv::cummax</span></code>, please refer to the <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/docs/en/deployment/onnxruntime_op.md#known-issues">Known Issues</a> in mmcv.</p></li>
<li><p>Though supported, it is <em>not recommended</em> to use batch inference in onnxruntime for <code class="docutils literal notranslate"><span class="pre">DETR</span></code>, because there is huge performance gap between ONNX and torch model (e.g. 33.5 vs 39.9 mAP on COCO for onnxruntime and torch respectively, with a batch size 2). The main reason for the gap is that these is non-negligible effect on the predicted regressions during batch inference for ONNX, since the predicted coordinates is normalized by <code class="docutils literal notranslate"><span class="pre">img_shape</span></code> (without padding) and should be converted to absolute format, but <code class="docutils literal notranslate"><span class="pre">img_shape</span></code> is not dynamically traceable thus the padded <code class="docutils literal notranslate"><span class="pre">img_shape_for_onnx</span></code> is used.</p></li>
<li><p>Currently only single-scale evaluation is supported with ONNX Runtime, also <code class="docutils literal notranslate"><span class="pre">mmcv::SoftNonMaxSuppression</span></code> is only supported for single image by now.</p></li>
</ul>
</section>
<section id="the-parameters-of-non-maximum-suppression-in-onnx-export">
<h2>The Parameters of Non-Maximum Suppression in ONNX Export<a class="headerlink" href="#the-parameters-of-non-maximum-suppression-in-onnx-export" title="Permalink to this heading">¶</a></h2>
<p>In the process of exporting the ONNX model, we set some parameters for the NMS op to control the number of output bounding boxes. The following will introduce the parameter setting of the NMS op in the supported models. You can set these parameters through <code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code>.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">nms_pre</span></code>: The number of boxes before NMS. The default setting is <code class="docutils literal notranslate"><span class="pre">1000</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">deploy_nms_pre</span></code>: The number of boxes before NMS when exporting to ONNX model. The default setting is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_per_img</span></code>: The number of boxes to be kept after NMS. The default setting is <code class="docutils literal notranslate"><span class="pre">100</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_output_boxes_per_class</span></code>: Maximum number of output boxes per class of NMS. The default setting is <code class="docutils literal notranslate"><span class="pre">200</span></code>.</p></li>
</ul>
</section>
<section id="reminders">
<h2>Reminders<a class="headerlink" href="#reminders" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>When the input model has custom op such as <code class="docutils literal notranslate"><span class="pre">RoIAlign</span></code> and if you want to verify the exported ONNX model, you may have to build <code class="docutils literal notranslate"><span class="pre">mmcv</span></code> with <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/deployment/onnxruntime_op.html">ONNXRuntime</a> from source.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mmcv.onnx.simplify</span></code> feature is based on <a class="reference external" href="https://github.com/daquexian/onnx-simplifier">onnx-simplifier</a>. If you want to try it, please refer to <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/deployment/onnx.html">onnx in <code class="docutils literal notranslate"><span class="pre">mmcv</span></code></a> and <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/deployment/onnxruntime_op.html">onnxruntime op in <code class="docutils literal notranslate"><span class="pre">mmcv</span></code></a> for more information.</p></li>
<li><p>If you meet any problem with the listed models above, please create an issue and it would be taken care of soon. For models not included in the list, please try to dig a little deeper and debug a little bit more and hopefully solve them by yourself.</p></li>
<li><p>Because this feature is experimental and may change fast, please always try with the latest <code class="docutils literal notranslate"><span class="pre">mmcv</span></code> and <code class="docutils literal notranslate"><span class="pre">rsidetecion</span></code>.</p></li>
</ul>
</section>
<section id="faqs">
<h2>FAQs<a class="headerlink" href="#faqs" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>None</p></li>
</ul>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="onnx2tensorrt.html" class="btn btn-neutral float-right" title="Tutorial 9: ONNX to TensorRT (Experimental)" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="finetune.html" class="btn btn-neutral" title="Tutorial 7: Finetuning Models" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 8: Pytorch to ONNX (Experimental)</a><ul>
<li><a class="reference internal" href="#try-the-new-mmdeploy-to-deploy-your-model">Try the new MMDeploy to deploy your model</a></li>
<li><a class="reference internal" href="#how-to-convert-models-from-pytorch-to-onnx">How to convert models from Pytorch to ONNX</a><ul>
<li><a class="reference internal" href="#prerequisite">Prerequisite</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
<li><a class="reference internal" href="#description-of-all-arguments">Description of all arguments</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-evaluate-the-exported-models">How to evaluate the exported models</a><ul>
<li><a class="reference internal" href="#id1">Prerequisite</a></li>
<li><a class="reference internal" href="#id2">Usage</a></li>
<li><a class="reference internal" href="#id3">Description of all arguments</a></li>
<li><a class="reference internal" href="#results-and-models">Results and Models</a></li>
</ul>
</li>
<li><a class="reference internal" href="#list-of-supported-models-exportable-to-onnx">List of supported models exportable to ONNX</a></li>
<li><a class="reference internal" href="#the-parameters-of-non-maximum-suppression-in-onnx-export">The Parameters of Non-Maximum Suppression in ONNX Export</a></li>
<li><a class="reference internal" href="#reminders">Reminders</a></li>
<li><a class="reference internal" href="#faqs">FAQs</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>