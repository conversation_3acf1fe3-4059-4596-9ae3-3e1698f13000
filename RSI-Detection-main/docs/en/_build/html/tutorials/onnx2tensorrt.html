


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 9: ONNX to TensorRT (Experimental) &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 10: Weight initialization" href="init_cfg.html" />
  <link rel="prev" title="Tutorial 8: Pytorch to ONNX (Experimental)" href="pytorch2onnx.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 9: ONNX to TensorRT (Experimental)</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/onnx2tensorrt.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-9-onnx-to-tensorrt-experimental">
<h1>Tutorial 9: ONNX to TensorRT (Experimental)<a class="headerlink" href="#tutorial-9-onnx-to-tensorrt-experimental" title="Permalink to this heading">¶</a></h1>
<section id="try-the-new-mmdeploy-to-deploy-your-model">
<h2><a class="reference external" href="https://mmdeploy.readthedocs.io/">Try the new MMDeploy to deploy your model</a><a class="headerlink" href="#try-the-new-mmdeploy-to-deploy-your-model" title="Permalink to this heading">¶</a></h2>
</section>
<!-- TOC -->
<ul class="simple">
<li><p><a class="reference internal" href="#tutorial-9-onnx-to-tensorrt-experimental"><span class="std std-doc">Tutorial 9: ONNX to TensorRT (Experimental)</span></a></p>
<ul>
<li><p><a class="reference internal" href="#how-to-convert-models-from-onnx-to-tensorrt"><span class="std std-doc">How to convert models from ONNX to TensorRT</span></a></p>
<ul>
<li><p><a class="reference internal" href="#prerequisite"><span class="std std-doc">Prerequisite</span></a></p></li>
<li><p><a class="reference internal" href="#usage"><span class="std std-doc">Usage</span></a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#how-to-evaluate-the-exported-models"><span class="std std-doc">How to evaluate the exported models</span></a></p></li>
<li><p><a class="reference internal" href="#list-of-supported-models-convertible-to-tensorrt"><span class="std std-doc">List of supported models convertible to TensorRT</span></a></p></li>
<li><p><a class="reference internal" href="#reminders"><span class="std std-doc">Reminders</span></a></p></li>
<li><p><a class="reference internal" href="#faqs"><span class="std std-doc">FAQs</span></a></p></li>
</ul>
</li>
</ul>
<!-- TOC -->
<section id="how-to-convert-models-from-onnx-to-tensorrt">
<h2>How to convert models from ONNX to TensorRT<a class="headerlink" href="#how-to-convert-models-from-onnx-to-tensorrt" title="Permalink to this heading">¶</a></h2>
<section id="prerequisite">
<h3>Prerequisite<a class="headerlink" href="#prerequisite" title="Permalink to this heading">¶</a></h3>
<ol class="arabic simple">
<li><p>Please refer to <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/get_started.html">get_started.md</a> for installation of MMCV and MMDetection from source.</p></li>
<li><p>Please refer to <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/deployment/onnxruntime_op.html">ONNXRuntime in mmcv</a> and <a class="reference external" href="https://github.com/open-mmlab/mmcv/blob/master/docs/en/deployment/tensorrt_plugin.md/">TensorRT plugin in mmcv</a> to install <code class="docutils literal notranslate"><span class="pre">mmcv-full</span></code> with ONNXRuntime custom ops and TensorRT plugins.</p></li>
<li><p>Use our tool <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/tutorials/pytorch2onnx.html">pytorch2onnx</a> to convert the model from PyTorch to ONNX.</p></li>
</ol>
</section>
<section id="usage">
<h3>Usage<a class="headerlink" href="#usage" title="Permalink to this heading">¶</a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/deployment/onnx2tensorrt.py <span class="se">\</span>
    <span class="si">${</span><span class="nv">CONFIG</span><span class="si">}</span> <span class="se">\</span>
    <span class="si">${</span><span class="nv">MODEL</span><span class="si">}</span> <span class="se">\</span>
    --trt-file <span class="si">${</span><span class="nv">TRT_FILE</span><span class="si">}</span> <span class="se">\</span>
    --input-img <span class="si">${</span><span class="nv">INPUT_IMAGE_PATH</span><span class="si">}</span> <span class="se">\</span>
    --shape <span class="si">${</span><span class="nv">INPUT_IMAGE_SHAPE</span><span class="si">}</span> <span class="se">\</span>
    --min-shape <span class="si">${</span><span class="nv">MIN_IMAGE_SHAPE</span><span class="si">}</span> <span class="se">\</span>
    --max-shape <span class="si">${</span><span class="nv">MAX_IMAGE_SHAPE</span><span class="si">}</span> <span class="se">\</span>
    --workspace-size <span class="o">{</span>WORKSPACE_SIZE<span class="o">}</span> <span class="se">\</span>
    --show <span class="se">\</span>
    --verify <span class="se">\</span>
</pre></div>
</div>
<p>Description of all arguments:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">config</span></code> : The path of a model config file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">model</span></code> : The path of an ONNX model file.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--trt-file</span></code>: The Path of output TensorRT engine file. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">tmp.trt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--input-img</span></code> : The path of an input image for tracing and conversion. By default, it will be set to <code class="docutils literal notranslate"><span class="pre">demo/demo.jpg</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--shape</span></code>: The height and width of model input. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">400</span> <span class="pre">600</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--min-shape</span></code>: The minimum height and width of model input. If not specified, it will be set to the same as <code class="docutils literal notranslate"><span class="pre">--shape</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--max-shape</span></code>: The maximum height and width of model input. If not specified, it will be set to the same as <code class="docutils literal notranslate"><span class="pre">--shape</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--workspace-size</span></code> : The required GPU workspace size in GiB to build TensorRT engine. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">1</span></code> GiB.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--show</span></code>: Determines whether to show the outputs of the model. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--verify</span></code>: Determines whether to verify the correctness of models between ONNXRuntime and TensorRT. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">--verbose</span></code>: Determines whether to print logging messages. It’s useful for debugging. If not specified, it will be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
</ul>
<p>Example:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/deployment/onnx2tensorrt.py <span class="se">\</span>
    configs/retinanet/retinanet_r50_fpn_1x_coco.py <span class="se">\</span>
    checkpoints/retinanet_r50_fpn_1x_coco.onnx <span class="se">\</span>
    --trt-file checkpoints/retinanet_r50_fpn_1x_coco.trt <span class="se">\</span>
    --input-img demo/demo.jpg <span class="se">\</span>
    --shape <span class="m">400</span> <span class="m">600</span> <span class="se">\</span>
    --show <span class="se">\</span>
    --verify <span class="se">\</span>
</pre></div>
</div>
</section>
</section>
<section id="how-to-evaluate-the-exported-models">
<h2>How to evaluate the exported models<a class="headerlink" href="#how-to-evaluate-the-exported-models" title="Permalink to this heading">¶</a></h2>
<p>We prepare a tool <code class="docutils literal notranslate"><span class="pre">tools/deplopyment/test.py</span></code> to evaluate TensorRT models.</p>
<p>Please refer to following links for more information.</p>
<ul class="simple">
<li><p><a class="reference internal" href="pytorch2onnx.html#how-to-evaluate-the-exported-models"><span class="std std-doc">how-to-evaluate-the-exported-models</span></a></p></li>
<li><p><a class="reference internal" href="pytorch2onnx.html#results-and-models"><span class="std std-doc">results-and-models</span></a></p></li>
</ul>
</section>
<section id="list-of-supported-models-convertible-to-tensorrt">
<h2>List of supported models convertible to TensorRT<a class="headerlink" href="#list-of-supported-models-convertible-to-tensorrt" title="Permalink to this heading">¶</a></h2>
<p>The table below lists the models that are guaranteed to be convertible to TensorRT.</p>
<table border="1" class="docutils">
<thead>
<tr>
<th style="text-align: center;">Model</th>
<th style="text-align: center;">Config</th>
<th style="text-align: center;">Dynamic Shape</th>
<th style="text-align: center;">Batch Inference</th>
<th style="text-align: center;">Note</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: center;">SSD</td>
<td style="text-align: center;"><code>configs/ssd/ssd300_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">FSAF</td>
<td style="text-align: center;"><code>configs/fsaf/fsaf_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">FCOS</td>
<td style="text-align: center;"><code>configs/fcos/fcos_r50_caffe_fpn_4x4_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">YOLOv3</td>
<td style="text-align: center;"><code>configs/yolo/yolov3_d53_mstrain-608_273e_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">RetinaNet</td>
<td style="text-align: center;"><code>configs/retinanet/retinanet_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Faster R-CNN</td>
<td style="text-align: center;"><code>configs/faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Cascade R-CNN</td>
<td style="text-align: center;"><code>configs/cascade_rcnn/cascade_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Mask R-CNN</td>
<td style="text-align: center;"><code>configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">Cascade Mask R-CNN</td>
<td style="text-align: center;"><code>configs/cascade_rcnn/cascade_mask_rcnn_r50_fpn_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
<tr>
<td style="text-align: center;">PointRend</td>
<td style="text-align: center;"><code>configs/point_rend/point_rend_r50_caffe_fpn_mstrain_1x_coco.py</code></td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;">Y</td>
<td style="text-align: center;"></td>
</tr>
</tbody>
</table>
<p>Notes:</p>
<ul class="simple">
<li><p><em>All models above are tested with Pytorch==1.6.0, onnx==1.7.0 and TensorRT-7.2.1.6.Ubuntu-16.04.x86_64-gnu.cuda-10.2.cudnn8.0</em></p></li>
</ul>
</section>
<section id="reminders">
<h2>Reminders<a class="headerlink" href="#reminders" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>If you meet any problem with the listed models above, please create an issue and it would be taken care of soon. For models not included in the list, we may not provide much help here due to the limited resources. Please try to dig a little deeper and debug by yourself.</p></li>
<li><p>Because this feature is experimental and may change fast, please always try with the latest <code class="docutils literal notranslate"><span class="pre">mmcv</span></code> and <code class="docutils literal notranslate"><span class="pre">rsidetecion</span></code>.</p></li>
</ul>
</section>
<section id="faqs">
<h2>FAQs<a class="headerlink" href="#faqs" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>None</p></li>
</ul>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="init_cfg.html" class="btn btn-neutral float-right" title="Tutorial 10: Weight initialization" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="pytorch2onnx.html" class="btn btn-neutral" title="Tutorial 8: Pytorch to ONNX (Experimental)" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 9: ONNX to TensorRT (Experimental)</a><ul>
<li><a class="reference internal" href="#try-the-new-mmdeploy-to-deploy-your-model">Try the new MMDeploy to deploy your model</a></li>
<li><a class="reference internal" href="#how-to-convert-models-from-onnx-to-tensorrt">How to convert models from ONNX to TensorRT</a><ul>
<li><a class="reference internal" href="#prerequisite">Prerequisite</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-evaluate-the-exported-models">How to evaluate the exported models</a></li>
<li><a class="reference internal" href="#list-of-supported-models-convertible-to-tensorrt">List of supported models convertible to TensorRT</a></li>
<li><a class="reference internal" href="#reminders">Reminders</a></li>
<li><a class="reference internal" href="#faqs">FAQs</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>