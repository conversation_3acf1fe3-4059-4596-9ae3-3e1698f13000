


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 6: Customize Losses &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 7: Finetuning Models" href="finetune.html" />
  <link rel="prev" title="Tutorial 5: Customize Runtime Settings" href="customize_runtime.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 6: Customize Losses</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/customize_losses.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-6-customize-losses">
<h1>Tutorial 6: Customize Losses<a class="headerlink" href="#tutorial-6-customize-losses" title="Permalink to this heading">¶</a></h1>
<p>MMDetection provides users with different loss functions. But the default configuration may be not applicable for different datasets or models, so users may want to modify a specific loss to adapt the new situation.</p>
<p>This tutorial first elaborate the computation pipeline of losses, then give some instructions about how to modify each step. The modification can be categorized as tweaking and weighting.</p>
<section id="computation-pipeline-of-a-loss">
<h2>Computation pipeline of a loss<a class="headerlink" href="#computation-pipeline-of-a-loss" title="Permalink to this heading">¶</a></h2>
<p>Given the input prediction and target, as well as the weights, a loss function maps the input tensor to the final loss scalar. The mapping can be divided into five steps:</p>
<ol class="arabic simple">
<li><p>Set the sampling method to sample positive and negative samples.</p></li>
<li><p>Get <strong>element-wise</strong> or <strong>sample-wise</strong> loss by the loss kernel function.</p></li>
<li><p>Weighting the loss with a weight tensor <strong>element-wisely</strong>.</p></li>
<li><p>Reduce the loss tensor to a <strong>scalar</strong>.</p></li>
<li><p>Weighting the loss with a <strong>scalar</strong>.</p></li>
</ol>
</section>
<section id="set-sampling-method-step-1">
<h2>Set sampling method (step 1)<a class="headerlink" href="#set-sampling-method-step-1" title="Permalink to this heading">¶</a></h2>
<p>For some loss functions, sampling strategies are needed to avoid imbalance between positive and negative samples.</p>
<p>For example, when using <code class="docutils literal notranslate"><span class="pre">CrossEntropyLoss</span></code> in RPN head, we need to set <code class="docutils literal notranslate"><span class="pre">RandomSampler</span></code> in <code class="docutils literal notranslate"><span class="pre">train_cfg</span></code></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">train_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="n">rpn</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">sampler</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RandomSampler&#39;</span><span class="p">,</span>
            <span class="n">num</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
            <span class="n">pos_fraction</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span>
            <span class="n">neg_pos_ub</span><span class="o">=-</span><span class="mi">1</span><span class="p">,</span>
            <span class="n">add_gt_as_proposals</span><span class="o">=</span><span class="bp">False</span><span class="p">))</span>
</pre></div>
</div>
<p>For some other losses which have positive and negative sample balance mechanism such as Focal Loss, GHMC, and QualityFocalLoss, the sampler is no more necessary.</p>
</section>
<section id="tweaking-loss">
<h2>Tweaking loss<a class="headerlink" href="#tweaking-loss" title="Permalink to this heading">¶</a></h2>
<p>Tweaking a loss is more related with step 2, 4, 5, and most modifications can be specified in the config.
Here we take <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/losses/focal_loss.py">Focal Loss (FL)</a> as an example.
The following code sniper are the construction method and config of FL respectively, they are actually one to one correspondence.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@LOSSES.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">FocalLoss</span><span class="p">(</span><span class="n">nn</span><span class="o">.</span><span class="n">Module</span><span class="p">):</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
                 <span class="n">gamma</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
                 <span class="n">alpha</span><span class="o">=</span><span class="mf">0.25</span><span class="p">,</span>
                 <span class="n">reduction</span><span class="o">=</span><span class="s1">&#39;mean&#39;</span><span class="p">,</span>
                 <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">):</span>
</pre></div>
</div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FocalLoss&#39;</span><span class="p">,</span>
    <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
    <span class="n">gamma</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
    <span class="n">alpha</span><span class="o">=</span><span class="mf">0.25</span><span class="p">,</span>
    <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">)</span>
</pre></div>
</div>
<section id="tweaking-hyper-parameters-step-2">
<h3>Tweaking hyper-parameters (step 2)<a class="headerlink" href="#tweaking-hyper-parameters-step-2" title="Permalink to this heading">¶</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">gamma</span></code> and <code class="docutils literal notranslate"><span class="pre">beta</span></code> are two hyper-parameters in the Focal Loss. Say if we want to change the value of <code class="docutils literal notranslate"><span class="pre">gamma</span></code> to be 1.5 and <code class="docutils literal notranslate"><span class="pre">alpha</span></code> to be 0.5, then we can specify them in the config as follows:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FocalLoss&#39;</span><span class="p">,</span>
    <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
    <span class="n">gamma</span><span class="o">=</span><span class="mf">1.5</span><span class="p">,</span>
    <span class="n">alpha</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span>
    <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="tweaking-the-way-of-reduction-step-3">
<h3>Tweaking the way of reduction (step 3)<a class="headerlink" href="#tweaking-the-way-of-reduction-step-3" title="Permalink to this heading">¶</a></h3>
<p>The default way of reduction is <code class="docutils literal notranslate"><span class="pre">mean</span></code> for FL. Say if we want to change the reduction from <code class="docutils literal notranslate"><span class="pre">mean</span></code> to <code class="docutils literal notranslate"><span class="pre">sum</span></code>, we can specify it in the config as follows:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FocalLoss&#39;</span><span class="p">,</span>
    <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
    <span class="n">gamma</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
    <span class="n">alpha</span><span class="o">=</span><span class="mf">0.25</span><span class="p">,</span>
    <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">,</span>
    <span class="n">reduction</span><span class="o">=</span><span class="s1">&#39;sum&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="tweaking-loss-weight-step-5">
<h3>Tweaking loss weight (step 5)<a class="headerlink" href="#tweaking-loss-weight-step-5" title="Permalink to this heading">¶</a></h3>
<p>The loss weight here is a scalar which controls the weight of different losses in multi-task learning, e.g. classification loss and regression loss. Say if we want to change to loss weight of classification loss to be 0.5, we can specify it in the config as follows:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FocalLoss&#39;</span><span class="p">,</span>
    <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
    <span class="n">gamma</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
    <span class="n">alpha</span><span class="o">=</span><span class="mf">0.25</span><span class="p">,</span>
    <span class="n">loss_weight</span><span class="o">=</span><span class="mf">0.5</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="weighting-loss-step-3">
<h2>Weighting loss (step 3)<a class="headerlink" href="#weighting-loss-step-3" title="Permalink to this heading">¶</a></h2>
<p>Weighting loss means we re-weight the loss element-wisely. To be more specific, we multiply the loss tensor with a weight tensor which has the same shape. As a result, different entries of the loss can be scaled differently, and so called element-wisely.
The loss weight varies across different models and highly context related, but overall there are two kinds of loss weights, <code class="docutils literal notranslate"><span class="pre">label_weights</span></code> for classification loss and <code class="docutils literal notranslate"><span class="pre">bbox_weights</span></code> for bbox regression loss. You can find them in the <code class="docutils literal notranslate"><span class="pre">get_target</span></code> method of the corresponding head. Here we take <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/dense_heads/atss_head.py#L530">ATSSHead</a> as an example, which inherit <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/dense_heads/anchor_head.py">AnchorHead</a> but overwrite its <code class="docutils literal notranslate"><span class="pre">get_targets</span></code> method which yields different <code class="docutils literal notranslate"><span class="pre">label_weights</span></code> and <code class="docutils literal notranslate"><span class="pre">bbox_weights</span></code>.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">ATSSHead</span><span class="p">(</span><span class="n">AnchorHead</span><span class="p">):</span>

    <span class="o">...</span>

    <span class="k">def</span> <span class="nf">get_targets</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                    <span class="n">anchor_list</span><span class="p">,</span>
                    <span class="n">valid_flag_list</span><span class="p">,</span>
                    <span class="n">gt_bboxes_list</span><span class="p">,</span>
                    <span class="n">img_metas</span><span class="p">,</span>
                    <span class="n">gt_bboxes_ignore_list</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                    <span class="n">gt_labels_list</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                    <span class="n">label_channels</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                    <span class="n">unmap_outputs</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
</pre></div>
</div>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="finetune.html" class="btn btn-neutral float-right" title="Tutorial 7: Finetuning Models" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="customize_runtime.html" class="btn btn-neutral" title="Tutorial 5: Customize Runtime Settings" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 6: Customize Losses</a><ul>
<li><a class="reference internal" href="#computation-pipeline-of-a-loss">Computation pipeline of a loss</a></li>
<li><a class="reference internal" href="#set-sampling-method-step-1">Set sampling method (step 1)</a></li>
<li><a class="reference internal" href="#tweaking-loss">Tweaking loss</a><ul>
<li><a class="reference internal" href="#tweaking-hyper-parameters-step-2">Tweaking hyper-parameters (step 2)</a></li>
<li><a class="reference internal" href="#tweaking-the-way-of-reduction-step-3">Tweaking the way of reduction (step 3)</a></li>
<li><a class="reference internal" href="#tweaking-loss-weight-step-5">Tweaking loss weight (step 5)</a></li>
</ul>
</li>
<li><a class="reference internal" href="#weighting-loss-step-3">Weighting loss (step 3)</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>