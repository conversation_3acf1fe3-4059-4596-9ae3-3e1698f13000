


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 4: Customize Models &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 5: Customize Runtime Settings" href="customize_runtime.html" />
  <link rel="prev" title="Tutorial 3: Customize Data Pipelines" href="data_pipeline.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 4: Customize Models</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/customize_models.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-4-customize-models">
<h1>Tutorial 4: Customize Models<a class="headerlink" href="#tutorial-4-customize-models" title="Permalink to this heading">¶</a></h1>
<p>We basically categorize model components into 5 types.</p>
<ul class="simple">
<li><p>backbone: usually an FCN network to extract feature maps, e.g., ResNet, MobileNet.</p></li>
<li><p>neck: the component between backbones and heads, e.g., FPN, PAFPN.</p></li>
<li><p>head: the component for specific tasks, e.g., bbox prediction and mask prediction.</p></li>
<li><p>roi extractor: the part for extracting RoI features from feature maps, e.g., RoI Align.</p></li>
<li><p>loss: the component in head for calculating losses, e.g., FocalLoss, L1Loss, and GHMLoss.</p></li>
</ul>
<section id="develop-new-components">
<h2>Develop new components<a class="headerlink" href="#develop-new-components" title="Permalink to this heading">¶</a></h2>
<section id="add-a-new-backbone">
<h3>Add a new backbone<a class="headerlink" href="#add-a-new-backbone" title="Permalink to this heading">¶</a></h3>
<p>Here we show how to develop new components with an example of MobileNet.</p>
<section id="define-a-new-backbone-e-g-mobilenet">
<h4>1. Define a new backbone (e.g. MobileNet)<a class="headerlink" href="#define-a-new-backbone-e-g-mobilenet" title="Permalink to this heading">¶</a></h4>
<p>Create a new file <code class="docutils literal notranslate"><span class="pre">rsidet/models/backbones/mobilenet.py</span></code>.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch.nn</span> <span class="kn">as</span> <span class="nn">nn</span>

<span class="kn">from</span> <span class="nn">..builder</span> <span class="kn">import</span> <span class="n">BACKBONES</span>


<span class="nd">@BACKBONES.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">MobileNet</span><span class="p">(</span><span class="n">nn</span><span class="o">.</span><span class="n">Module</span><span class="p">):</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">):</span>
        <span class="k">pass</span>

    <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>  <span class="c1"># should return a tuple</span>
        <span class="k">pass</span>
</pre></div>
</div>
</section>
<section id="import-the-module">
<h4>2. Import the module<a class="headerlink" href="#import-the-module" title="Permalink to this heading">¶</a></h4>
<p>You can either add the following line to <code class="docutils literal notranslate"><span class="pre">rsidet/models/backbones/__init__.py</span></code></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">.mobilenet</span> <span class="kn">import</span> <span class="n">MobileNet</span>
</pre></div>
</div>
<p>or alternatively add</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">custom_imports</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;rsidet.models.backbones.mobilenet&#39;</span><span class="p">],</span>
    <span class="n">allow_failed_imports</span><span class="o">=</span><span class="bp">False</span><span class="p">)</span>
</pre></div>
</div>
<p>to the config file to avoid modifying the original code.</p>
</section>
<section id="use-the-backbone-in-your-config-file">
<h4>3. Use the backbone in your config file<a class="headerlink" href="#use-the-backbone-in-your-config-file" title="Permalink to this heading">¶</a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="o">...</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MobileNet&#39;</span><span class="p">,</span>
        <span class="n">arg1</span><span class="o">=</span><span class="n">xxx</span><span class="p">,</span>
        <span class="n">arg2</span><span class="o">=</span><span class="n">xxx</span><span class="p">),</span>
    <span class="o">...</span>
</pre></div>
</div>
</section>
</section>
<section id="add-new-necks">
<h3>Add new necks<a class="headerlink" href="#add-new-necks" title="Permalink to this heading">¶</a></h3>
<section id="define-a-neck-e-g-pafpn">
<h4>1. Define a neck (e.g. PAFPN)<a class="headerlink" href="#define-a-neck-e-g-pafpn" title="Permalink to this heading">¶</a></h4>
<p>Create a new file <code class="docutils literal notranslate"><span class="pre">rsidet/models/necks/pafpn.py</span></code>.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">..builder</span> <span class="kn">import</span> <span class="n">NECKS</span>

<span class="nd">@NECKS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">PAFPN</span><span class="p">(</span><span class="n">nn</span><span class="o">.</span><span class="n">Module</span><span class="p">):</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                <span class="n">in_channels</span><span class="p">,</span>
                <span class="n">out_channels</span><span class="p">,</span>
                <span class="n">num_outs</span><span class="p">,</span>
                <span class="n">start_level</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">end_level</span><span class="o">=-</span><span class="mi">1</span><span class="p">,</span>
                <span class="n">add_extra_convs</span><span class="o">=</span><span class="bp">False</span><span class="p">):</span>
        <span class="k">pass</span>

    <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">inputs</span><span class="p">):</span>
        <span class="c1"># implementation is ignored</span>
        <span class="k">pass</span>
</pre></div>
</div>
</section>
<section id="id1">
<h4>2. Import the module<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h4>
<p>You can either add the following line to <code class="docutils literal notranslate"><span class="pre">rsidet/models/necks/__init__.py</span></code>,</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">.pafpn</span> <span class="kn">import</span> <span class="n">PAFPN</span>
</pre></div>
</div>
<p>or alternatively add</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">custom_imports</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;rsidet.models.necks.pafpn.py&#39;</span><span class="p">],</span>
    <span class="n">allow_failed_imports</span><span class="o">=</span><span class="bp">False</span><span class="p">)</span>
</pre></div>
</div>
<p>to the config file and avoid modifying the original code.</p>
</section>
<section id="modify-the-config-file">
<h4>3. Modify the config file<a class="headerlink" href="#modify-the-config-file" title="Permalink to this heading">¶</a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;PAFPN&#39;</span><span class="p">,</span>
    <span class="n">in_channels</span><span class="o">=</span><span class="p">[</span><span class="mi">256</span><span class="p">,</span> <span class="mi">512</span><span class="p">,</span> <span class="mi">1024</span><span class="p">,</span> <span class="mi">2048</span><span class="p">],</span>
    <span class="n">out_channels</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
    <span class="n">num_outs</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="add-new-heads">
<h3>Add new heads<a class="headerlink" href="#add-new-heads" title="Permalink to this heading">¶</a></h3>
<p>Here we show how to develop a new head with the example of <a class="reference external" href="https://arxiv.org/abs/1904.06493">Double Head R-CNN</a> as the following.</p>
<p>First, add a new bbox head in <code class="docutils literal notranslate"><span class="pre">rsidet/models/roi_heads/bbox_heads/double_bbox_head.py</span></code>.
Double Head R-CNN implements a new bbox head for object detection.
To implement a bbox head, basically we need to implement three functions of the new module as the following.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">rsidet.models.builder</span> <span class="kn">import</span> <span class="n">HEADS</span>
<span class="kn">from</span> <span class="nn">.bbox_head</span> <span class="kn">import</span> <span class="n">BBoxHead</span>

<span class="nd">@HEADS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">DoubleConvFCBBoxHead</span><span class="p">(</span><span class="n">BBoxHead</span><span class="p">):</span>
    <span class="sa">r</span><span class="sd">&quot;&quot;&quot;Bbox head used in Double-Head R-CNN</span>

<span class="sd">                                      /-&gt; cls</span>
<span class="sd">                  /-&gt; shared convs -&gt;</span>
<span class="sd">                                      \-&gt; reg</span>
<span class="sd">    roi features</span>
<span class="sd">                                      /-&gt; cls</span>
<span class="sd">                  \-&gt; shared fc    -&gt;</span>
<span class="sd">                                      \-&gt; reg</span>
<span class="sd">    &quot;&quot;&quot;</span>  <span class="c1"># noqa: W605</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">num_convs</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                 <span class="n">num_fcs</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                 <span class="n">conv_out_channels</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span>
                 <span class="n">fc_out_channels</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span>
                 <span class="n">conv_cfg</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
                 <span class="n">norm_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;BN&#39;</span><span class="p">),</span>
                 <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="n">kwargs</span><span class="o">.</span><span class="n">setdefault</span><span class="p">(</span><span class="s1">&#39;with_avg_pool&#39;</span><span class="p">,</span> <span class="bp">True</span><span class="p">)</span>
        <span class="nb">super</span><span class="p">(</span><span class="n">DoubleConvFCBBoxHead</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>


    <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x_cls</span><span class="p">,</span> <span class="n">x_reg</span><span class="p">):</span>

</pre></div>
</div>
<p>Second, implement a new RoI Head if it is necessary. We plan to inherit the new <code class="docutils literal notranslate"><span class="pre">DoubleHeadRoIHead</span></code> from <code class="docutils literal notranslate"><span class="pre">StandardRoIHead</span></code>. We can find that a <code class="docutils literal notranslate"><span class="pre">StandardRoIHead</span></code> already implements the following functions.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch</span>

<span class="kn">from</span> <span class="nn">rsidet.core</span> <span class="kn">import</span> <span class="n">bbox2result</span><span class="p">,</span> <span class="n">bbox2roi</span><span class="p">,</span> <span class="n">build_assigner</span><span class="p">,</span> <span class="n">build_sampler</span>
<span class="kn">from</span> <span class="nn">..builder</span> <span class="kn">import</span> <span class="n">HEADS</span><span class="p">,</span> <span class="n">build_head</span><span class="p">,</span> <span class="n">build_roi_extractor</span>
<span class="kn">from</span> <span class="nn">.base_roi_head</span> <span class="kn">import</span> <span class="n">BaseRoIHead</span>
<span class="kn">from</span> <span class="nn">.test_mixins</span> <span class="kn">import</span> <span class="n">BBoxTestMixin</span><span class="p">,</span> <span class="n">MaskTestMixin</span>


<span class="nd">@HEADS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">StandardRoIHead</span><span class="p">(</span><span class="n">BaseRoIHead</span><span class="p">,</span> <span class="n">BBoxTestMixin</span><span class="p">,</span> <span class="n">MaskTestMixin</span><span class="p">):</span>
    <span class="sd">&quot;&quot;&quot;Simplest base roi head including one bbox head and one mask head.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">init_assigner_sampler</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">init_bbox_head</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bbox_roi_extractor</span><span class="p">,</span> <span class="n">bbox_head</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">init_mask_head</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">mask_roi_extractor</span><span class="p">,</span> <span class="n">mask_head</span><span class="p">):</span>


    <span class="k">def</span> <span class="nf">forward_dummy</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">proposals</span><span class="p">):</span>


    <span class="k">def</span> <span class="nf">forward_train</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                      <span class="n">x</span><span class="p">,</span>
                      <span class="n">img_metas</span><span class="p">,</span>
                      <span class="n">proposal_list</span><span class="p">,</span>
                      <span class="n">gt_bboxes</span><span class="p">,</span>
                      <span class="n">gt_labels</span><span class="p">,</span>
                      <span class="n">gt_bboxes_ignore</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
                      <span class="n">gt_masks</span><span class="o">=</span><span class="bp">None</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">_bbox_forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">rois</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">_bbox_forward_train</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">sampling_results</span><span class="p">,</span> <span class="n">gt_bboxes</span><span class="p">,</span> <span class="n">gt_labels</span><span class="p">,</span>
                            <span class="n">img_metas</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">_mask_forward_train</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">sampling_results</span><span class="p">,</span> <span class="n">bbox_feats</span><span class="p">,</span> <span class="n">gt_masks</span><span class="p">,</span>
                            <span class="n">img_metas</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">_mask_forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">rois</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span> <span class="n">pos_inds</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span> <span class="n">bbox_feats</span><span class="o">=</span><span class="bp">None</span><span class="p">):</span>


    <span class="k">def</span> <span class="nf">simple_test</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                    <span class="n">x</span><span class="p">,</span>
                    <span class="n">proposal_list</span><span class="p">,</span>
                    <span class="n">img_metas</span><span class="p">,</span>
                    <span class="n">proposals</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
                    <span class="n">rescale</span><span class="o">=</span><span class="bp">False</span><span class="p">):</span>
        <span class="sd">&quot;&quot;&quot;Test without augmentation.&quot;&quot;&quot;</span>

</pre></div>
</div>
<p>Double Head’s modification is mainly in the bbox_forward logic, and it inherits other logics from the <code class="docutils literal notranslate"><span class="pre">StandardRoIHead</span></code>.
In the <code class="docutils literal notranslate"><span class="pre">rsidet/models/roi_heads/double_roi_head.py</span></code>, we implement the new RoI Head as the following:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">..builder</span> <span class="kn">import</span> <span class="n">HEADS</span>
<span class="kn">from</span> <span class="nn">.standard_roi_head</span> <span class="kn">import</span> <span class="n">StandardRoIHead</span>


<span class="nd">@HEADS.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">DoubleHeadRoIHead</span><span class="p">(</span><span class="n">StandardRoIHead</span><span class="p">):</span>
    <span class="sd">&quot;&quot;&quot;RoI head for Double Head RCNN</span>

<span class="sd">    https://arxiv.org/abs/1904.06493</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">reg_roi_scale_factor</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">(</span><span class="n">DoubleHeadRoIHead</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">reg_roi_scale_factor</span> <span class="o">=</span> <span class="n">reg_roi_scale_factor</span>

    <span class="k">def</span> <span class="nf">_bbox_forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">rois</span><span class="p">):</span>
        <span class="n">bbox_cls_feats</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bbox_roi_extractor</span><span class="p">(</span>
            <span class="n">x</span><span class="p">[:</span><span class="bp">self</span><span class="o">.</span><span class="n">bbox_roi_extractor</span><span class="o">.</span><span class="n">num_inputs</span><span class="p">],</span> <span class="n">rois</span><span class="p">)</span>
        <span class="n">bbox_reg_feats</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bbox_roi_extractor</span><span class="p">(</span>
            <span class="n">x</span><span class="p">[:</span><span class="bp">self</span><span class="o">.</span><span class="n">bbox_roi_extractor</span><span class="o">.</span><span class="n">num_inputs</span><span class="p">],</span>
            <span class="n">rois</span><span class="p">,</span>
            <span class="n">roi_scale_factor</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">reg_roi_scale_factor</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">with_shared_head</span><span class="p">:</span>
            <span class="n">bbox_cls_feats</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">shared_head</span><span class="p">(</span><span class="n">bbox_cls_feats</span><span class="p">)</span>
            <span class="n">bbox_reg_feats</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">shared_head</span><span class="p">(</span><span class="n">bbox_reg_feats</span><span class="p">)</span>
        <span class="n">cls_score</span><span class="p">,</span> <span class="n">bbox_pred</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bbox_head</span><span class="p">(</span><span class="n">bbox_cls_feats</span><span class="p">,</span> <span class="n">bbox_reg_feats</span><span class="p">)</span>

        <span class="n">bbox_results</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
            <span class="n">cls_score</span><span class="o">=</span><span class="n">cls_score</span><span class="p">,</span>
            <span class="n">bbox_pred</span><span class="o">=</span><span class="n">bbox_pred</span><span class="p">,</span>
            <span class="n">bbox_feats</span><span class="o">=</span><span class="n">bbox_cls_feats</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">bbox_results</span>
</pre></div>
</div>
<p>Last, the users need to add the module in
<code class="docutils literal notranslate"><span class="pre">rsidet/models/bbox_heads/__init__.py</span></code> and <code class="docutils literal notranslate"><span class="pre">rsidet/models/roi_heads/__init__.py</span></code> thus the corresponding registry could find and load them.</p>
<p>Alternatively, the users can add</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">custom_imports</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;rsidet.models.roi_heads.double_roi_head&#39;</span><span class="p">,</span> <span class="s1">&#39;rsidet.models.bbox_heads.double_bbox_head&#39;</span><span class="p">])</span>
</pre></div>
</div>
<p>to the config file and achieve the same goal.</p>
<p>The config file of Double Head R-CNN is as the following</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;../faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py&#39;</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">roi_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DoubleHeadRoIHead&#39;</span><span class="p">,</span>
        <span class="n">reg_roi_scale_factor</span><span class="o">=</span><span class="mf">1.3</span><span class="p">,</span>
        <span class="n">bbox_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="n">_delete_</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DoubleConvFCBBoxHead&#39;</span><span class="p">,</span>
            <span class="n">num_convs</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
            <span class="n">num_fcs</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
            <span class="n">in_channels</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
            <span class="n">conv_out_channels</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span>
            <span class="n">fc_out_channels</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span>
            <span class="n">roi_feat_size</span><span class="o">=</span><span class="mi">7</span><span class="p">,</span>
            <span class="n">num_classes</span><span class="o">=</span><span class="mi">80</span><span class="p">,</span>
            <span class="n">bbox_coder</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DeltaXYWHBBoxCoder&#39;</span><span class="p">,</span>
                <span class="n">target_means</span><span class="o">=</span><span class="p">[</span><span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">],</span>
                <span class="n">target_stds</span><span class="o">=</span><span class="p">[</span><span class="mf">0.1</span><span class="p">,</span> <span class="mf">0.1</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">]),</span>
            <span class="n">reg_class_agnostic</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
            <span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;CrossEntropyLoss&#39;</span><span class="p">,</span> <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">2.0</span><span class="p">),</span>
            <span class="n">loss_bbox</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;SmoothL1Loss&#39;</span><span class="p">,</span> <span class="n">beta</span><span class="o">=</span><span class="mf">1.0</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">2.0</span><span class="p">))))</span>

</pre></div>
</div>
<p>Since MMDetection 2.0, the config system supports to inherit configs such that the users can focus on the modification.
The Double Head R-CNN mainly uses a new DoubleHeadRoIHead and a new
<code class="docutils literal notranslate"><span class="pre">DoubleConvFCBBoxHead</span></code>, the arguments are set according to the <code class="docutils literal notranslate"><span class="pre">__init__</span></code> function of each module.</p>
</section>
<section id="add-new-loss">
<h3>Add new loss<a class="headerlink" href="#add-new-loss" title="Permalink to this heading">¶</a></h3>
<p>Assume you want to add a new loss as <code class="docutils literal notranslate"><span class="pre">MyLoss</span></code>, for bounding box regression.
To add a new loss function, the users need implement it in <code class="docutils literal notranslate"><span class="pre">rsidet/models/losses/my_loss.py</span></code>.
The decorator <code class="docutils literal notranslate"><span class="pre">weighted_loss</span></code> enable the loss to be weighted for each element.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">torch</span>
<span class="kn">import</span> <span class="nn">torch.nn</span> <span class="kn">as</span> <span class="nn">nn</span>

<span class="kn">from</span> <span class="nn">..builder</span> <span class="kn">import</span> <span class="n">LOSSES</span>
<span class="kn">from</span> <span class="nn">.utils</span> <span class="kn">import</span> <span class="n">weighted_loss</span>

<span class="nd">@weighted_loss</span>
<span class="k">def</span> <span class="nf">my_loss</span><span class="p">(</span><span class="n">pred</span><span class="p">,</span> <span class="n">target</span><span class="p">):</span>
    <span class="k">assert</span> <span class="n">pred</span><span class="o">.</span><span class="n">size</span><span class="p">()</span> <span class="o">==</span> <span class="n">target</span><span class="o">.</span><span class="n">size</span><span class="p">()</span> <span class="ow">and</span> <span class="n">target</span><span class="o">.</span><span class="n">numel</span><span class="p">()</span> <span class="o">&gt;</span> <span class="mi">0</span>
    <span class="n">loss</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">pred</span> <span class="o">-</span> <span class="n">target</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">loss</span>

<span class="nd">@LOSSES.register_module</span><span class="p">()</span>
<span class="k">class</span> <span class="nc">MyLoss</span><span class="p">(</span><span class="n">nn</span><span class="o">.</span><span class="n">Module</span><span class="p">):</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">reduction</span><span class="o">=</span><span class="s1">&#39;mean&#39;</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">(</span><span class="n">MyLoss</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">reduction</span> <span class="o">=</span> <span class="n">reduction</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">loss_weight</span> <span class="o">=</span> <span class="n">loss_weight</span>

    <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                <span class="n">pred</span><span class="p">,</span>
                <span class="n">target</span><span class="p">,</span>
                <span class="n">weight</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
                <span class="n">avg_factor</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
                <span class="n">reduction_override</span><span class="o">=</span><span class="bp">None</span><span class="p">):</span>
        <span class="k">assert</span> <span class="n">reduction_override</span> <span class="ow">in</span> <span class="p">(</span><span class="bp">None</span><span class="p">,</span> <span class="s1">&#39;none&#39;</span><span class="p">,</span> <span class="s1">&#39;mean&#39;</span><span class="p">,</span> <span class="s1">&#39;sum&#39;</span><span class="p">)</span>
        <span class="n">reduction</span> <span class="o">=</span> <span class="p">(</span>
            <span class="n">reduction_override</span> <span class="k">if</span> <span class="n">reduction_override</span> <span class="k">else</span> <span class="bp">self</span><span class="o">.</span><span class="n">reduction</span><span class="p">)</span>
        <span class="n">loss_bbox</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">loss_weight</span> <span class="o">*</span> <span class="n">my_loss</span><span class="p">(</span>
            <span class="n">pred</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">weight</span><span class="p">,</span> <span class="n">reduction</span><span class="o">=</span><span class="n">reduction</span><span class="p">,</span> <span class="n">avg_factor</span><span class="o">=</span><span class="n">avg_factor</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">loss_bbox</span>
</pre></div>
</div>
<p>Then the users need to add it in the <code class="docutils literal notranslate"><span class="pre">rsidet/models/losses/__init__.py</span></code>.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">.my_loss</span> <span class="kn">import</span> <span class="n">MyLoss</span><span class="p">,</span> <span class="n">my_loss</span>

</pre></div>
</div>
<p>Alternatively, you can add</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">custom_imports</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
    <span class="n">imports</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;rsidet.models.losses.my_loss&#39;</span><span class="p">])</span>
</pre></div>
</div>
<p>to the config file and achieve the same goal.</p>
<p>To use it, modify the <code class="docutils literal notranslate"><span class="pre">loss_xxx</span></code> field.
Since MyLoss is for regression, you need to modify the <code class="docutils literal notranslate"><span class="pre">loss_bbox</span></code> field in the head.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">loss_bbox</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MyLoss&#39;</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">))</span>
</pre></div>
</div>
</section>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="customize_runtime.html" class="btn btn-neutral float-right" title="Tutorial 5: Customize Runtime Settings" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="data_pipeline.html" class="btn btn-neutral" title="Tutorial 3: Customize Data Pipelines" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 4: Customize Models</a><ul>
<li><a class="reference internal" href="#develop-new-components">Develop new components</a><ul>
<li><a class="reference internal" href="#add-a-new-backbone">Add a new backbone</a><ul>
<li><a class="reference internal" href="#define-a-new-backbone-e-g-mobilenet">1. Define a new backbone (e.g. MobileNet)</a></li>
<li><a class="reference internal" href="#import-the-module">2. Import the module</a></li>
<li><a class="reference internal" href="#use-the-backbone-in-your-config-file">3. Use the backbone in your config file</a></li>
</ul>
</li>
<li><a class="reference internal" href="#add-new-necks">Add new necks</a><ul>
<li><a class="reference internal" href="#define-a-neck-e-g-pafpn">1. Define a neck (e.g. PAFPN)</a></li>
<li><a class="reference internal" href="#id1">2. Import the module</a></li>
<li><a class="reference internal" href="#modify-the-config-file">3. Modify the config file</a></li>
</ul>
</li>
<li><a class="reference internal" href="#add-new-heads">Add new heads</a></li>
<li><a class="reference internal" href="#add-new-loss">Add new loss</a></li>
</ul>
</li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>