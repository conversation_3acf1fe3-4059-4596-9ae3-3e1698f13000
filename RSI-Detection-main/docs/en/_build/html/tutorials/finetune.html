


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 7: Finetuning Models &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 8: Pytorch to ONNX (Experimental)" href="pytorch2onnx.html" />
  <link rel="prev" title="Tutorial 6: Customize Losses" href="customize_losses.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 7: Finetuning Models</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/finetune.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-7-finetuning-models">
<h1>Tutorial 7: Finetuning Models<a class="headerlink" href="#tutorial-7-finetuning-models" title="Permalink to this heading">¶</a></h1>
<p>Detectors pre-trained on the COCO dataset can serve as a good pre-trained model for other datasets, e.g., CityScapes and KITTI Dataset.
This tutorial provides instruction for users to use the models provided in the <a class="reference internal" href="../model_zoo.html"><span class="doc std std-doc">Model Zoo</span></a> for other datasets to obtain better performance.</p>
<p>There are two steps to finetune a model on a new dataset.</p>
<ul class="simple">
<li><p>Add support for the new dataset following <a class="reference internal" href="customize_dataset.html"><span class="doc std std-doc">Tutorial 2: Customize Datasets</span></a>.</p></li>
<li><p>Modify the configs as will be discussed in this tutorial.</p></li>
</ul>
<p>Take the finetuning process on Cityscapes Dataset as an example, the users need to modify five parts in the config.</p>
<section id="inherit-base-configs">
<h2>Inherit base configs<a class="headerlink" href="#inherit-base-configs" title="Permalink to this heading">¶</a></h2>
<p>To release the burden and reduce bugs in writing the whole configs, MMDetection V2.0 support inheriting configs from multiple existing configs. To finetune a Mask RCNN model, the new config needs to inherit
<code class="docutils literal notranslate"><span class="pre">_base_/models/mask_rcnn_r50_fpn.py</span></code> to build the basic structure of the model. To use the Cityscapes Dataset, the new config can also simply inherit <code class="docutils literal notranslate"><span class="pre">_base_/datasets/cityscapes_instance.py</span></code>. For runtime settings such as training schedules, the new config needs to inherit <code class="docutils literal notranslate"><span class="pre">_base_/default_runtime.py</span></code>. This configs are in the <code class="docutils literal notranslate"><span class="pre">configs</span></code> directory and the users can also choose to write the whole contents rather than use inheritance.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;../_base_/models/mask_rcnn_r50_fpn.py&#39;</span><span class="p">,</span>
    <span class="s1">&#39;../_base_/datasets/cityscapes_instance.py&#39;</span><span class="p">,</span> <span class="s1">&#39;../_base_/default_runtime.py&#39;</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="modify-head">
<h2>Modify head<a class="headerlink" href="#modify-head" title="Permalink to this heading">¶</a></h2>
<p>Then the new config needs to modify the head according to the class numbers of the new datasets. By only changing <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> in the roi_head, the weights of the pre-trained models are mostly reused except the final prediction head.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">pretrained</span><span class="o">=</span><span class="bp">None</span><span class="p">,</span>
    <span class="n">roi_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">bbox_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Shared2FCBBoxHead&#39;</span><span class="p">,</span>
            <span class="n">in_channels</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
            <span class="n">fc_out_channels</span><span class="o">=</span><span class="mi">1024</span><span class="p">,</span>
            <span class="n">roi_feat_size</span><span class="o">=</span><span class="mi">7</span><span class="p">,</span>
            <span class="n">num_classes</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span>
            <span class="n">bbox_coder</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DeltaXYWHBBoxCoder&#39;</span><span class="p">,</span>
                <span class="n">target_means</span><span class="o">=</span><span class="p">[</span><span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">],</span>
                <span class="n">target_stds</span><span class="o">=</span><span class="p">[</span><span class="mf">0.1</span><span class="p">,</span> <span class="mf">0.1</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">]),</span>
            <span class="n">reg_class_agnostic</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
            <span class="n">loss_cls</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;CrossEntropyLoss&#39;</span><span class="p">,</span> <span class="n">use_sigmoid</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">),</span>
            <span class="n">loss_bbox</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;SmoothL1Loss&#39;</span><span class="p">,</span> <span class="n">beta</span><span class="o">=</span><span class="mf">1.0</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">)),</span>
        <span class="n">mask_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;FCNMaskHead&#39;</span><span class="p">,</span>
            <span class="n">num_convs</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
            <span class="n">in_channels</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
            <span class="n">conv_out_channels</span><span class="o">=</span><span class="mi">256</span><span class="p">,</span>
            <span class="n">num_classes</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span>
            <span class="n">loss_mask</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;CrossEntropyLoss&#39;</span><span class="p">,</span> <span class="n">use_mask</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span> <span class="n">loss_weight</span><span class="o">=</span><span class="mf">1.0</span><span class="p">))))</span>
</pre></div>
</div>
</section>
<section id="modify-dataset">
<h2>Modify dataset<a class="headerlink" href="#modify-dataset" title="Permalink to this heading">¶</a></h2>
<p>The users may also need to prepare the dataset and write the configs about dataset. MMDetection V2.0 already support VOC, WIDER FACE, COCO and Cityscapes Dataset.</p>
</section>
<section id="modify-training-schedule">
<h2>Modify training schedule<a class="headerlink" href="#modify-training-schedule" title="Permalink to this heading">¶</a></h2>
<p>The finetuning hyperparameters vary from the default schedule. It usually requires smaller learning rate and less training epochs</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># optimizer</span>
<span class="c1"># lr is set for a batch size of 8</span>
<span class="n">optimizer</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;SGD&#39;</span><span class="p">,</span> <span class="n">lr</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span> <span class="n">momentum</span><span class="o">=</span><span class="mf">0.9</span><span class="p">,</span> <span class="n">weight_decay</span><span class="o">=</span><span class="mf">0.0001</span><span class="p">)</span>
<span class="n">optimizer_config</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">grad_clip</span><span class="o">=</span><span class="bp">None</span><span class="p">)</span>
<span class="c1"># learning policy</span>
<span class="n">lr_config</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">policy</span><span class="o">=</span><span class="s1">&#39;step&#39;</span><span class="p">,</span>
    <span class="n">warmup</span><span class="o">=</span><span class="s1">&#39;linear&#39;</span><span class="p">,</span>
    <span class="n">warmup_iters</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
    <span class="n">warmup_ratio</span><span class="o">=</span><span class="mf">0.001</span><span class="p">,</span>
    <span class="n">step</span><span class="o">=</span><span class="p">[</span><span class="mi">7</span><span class="p">])</span>
<span class="c1"># the max_epochs and step in lr_config need specifically tuned for the customized dataset</span>
<span class="n">runner</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">max_epochs</span><span class="o">=</span><span class="mi">8</span><span class="p">)</span>
<span class="n">log_config</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">interval</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="use-pre-trained-model">
<h2>Use pre-trained model<a class="headerlink" href="#use-pre-trained-model" title="Permalink to this heading">¶</a></h2>
<p>To use the pre-trained model, the new config add the link of pre-trained models in the <code class="docutils literal notranslate"><span class="pre">load_from</span></code>. The users might need to download the model weights before training to avoid the download time during training.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">load_from</span> <span class="o">=</span> <span class="s1">&#39;https://download.openmmlab.com/rsidetection/v2.0/mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco_bbox_mAP-0.408__segm_mAP-0.37_20200504_163245-42aa3d00.pth&#39;</span>  <span class="c1"># noqa</span>

</pre></div>
</div>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="pytorch2onnx.html" class="btn btn-neutral float-right" title="Tutorial 8: Pytorch to ONNX (Experimental)" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="customize_losses.html" class="btn btn-neutral" title="Tutorial 6: Customize Losses" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 7: Finetuning Models</a><ul>
<li><a class="reference internal" href="#inherit-base-configs">Inherit base configs</a></li>
<li><a class="reference internal" href="#modify-head">Modify head</a></li>
<li><a class="reference internal" href="#modify-dataset">Modify dataset</a></li>
<li><a class="reference internal" href="#modify-training-schedule">Modify training schedule</a></li>
<li><a class="reference internal" href="#use-pre-trained-model">Use pre-trained model</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>