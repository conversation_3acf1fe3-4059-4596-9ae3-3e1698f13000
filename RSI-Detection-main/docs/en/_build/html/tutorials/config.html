


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Tutorial 1: Learn about Configs &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="../_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="../_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="../_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="../_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="../genindex.html" />
  <link rel="search" title="Search" href="../search.html" />
  <link rel="next" title="Tutorial 2: Customize Datasets" href="customize_dataset.html" />
  <link rel="prev" title="&lt;no title&gt;" href="index.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="../_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="../_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="../useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="../projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="../index.html">
            Docs
        </a> &gt;
      </li>

        
          <li><a href="index.html">&lt;no title&gt;</a> &gt;</li>
        
      <li>Tutorial 1: Learn about Configs</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="../_sources/tutorials/config.md.txt" rel="nofollow"><img src="../_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="tutorial-1-learn-about-configs">
<h1>Tutorial 1: Learn about Configs<a class="headerlink" href="#tutorial-1-learn-about-configs" title="Permalink to this heading">¶</a></h1>
<p>We incorporate modular and inheritance design into our config system, which is convenient to conduct various experiments.
If you wish to inspect the config file, you may run <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">tools/misc/print_config.py</span> <span class="pre">/PATH/TO/CONFIG</span></code> to see the complete config.</p>
<section id="modify-config-through-script-arguments">
<h2>Modify config through script arguments<a class="headerlink" href="#modify-config-through-script-arguments" title="Permalink to this heading">¶</a></h2>
<p>When submitting jobs using “tools/train.py” or “tools/test.py”, you may specify <code class="docutils literal notranslate"><span class="pre">--cfg-options</span></code> to in-place modify the config.</p>
<ul>
<li><p>Update config keys of dict chains.</p>
<p>The config options can be specified following the order of the dict keys in the original config.
For example, <code class="docutils literal notranslate"><span class="pre">--cfg-options</span> <span class="pre">model.backbone.norm_eval=False</span></code> changes the all BN modules in model backbones to <code class="docutils literal notranslate"><span class="pre">train</span></code> mode.</p>
</li>
<li><p>Update keys inside a list of configs.</p>
<p>Some config dicts are composed as a list in your config. For example, the training pipeline <code class="docutils literal notranslate"><span class="pre">data.train.pipeline</span></code> is normally a list
e.g. <code class="docutils literal notranslate"><span class="pre">[dict(type='LoadImageFromFile'),</span> <span class="pre">...]</span></code>. If you want to change <code class="docutils literal notranslate"><span class="pre">'LoadImageFromFile'</span></code> to <code class="docutils literal notranslate"><span class="pre">'LoadImageFromWebcam'</span></code> in the pipeline,
you may specify <code class="docutils literal notranslate"><span class="pre">--cfg-options</span> <span class="pre">data.train.pipeline.0.type=LoadImageFromWebcam</span></code>.</p>
</li>
<li><p>Update values of list/tuples.</p>
<p>If the value to be updated is a list or a tuple. For example, the config file normally sets <code class="docutils literal notranslate"><span class="pre">workflow=[('train',</span> <span class="pre">1)]</span></code>. If you want to
change this key, you may specify <code class="docutils literal notranslate"><span class="pre">--cfg-options</span> <span class="pre">workflow=&quot;[(train,1),(val,1)]&quot;</span></code>. Note that the quotation mark ” is necessary to
support list/tuple data types, and that <strong>NO</strong> white space is allowed inside the quotation marks in the specified value.</p>
</li>
</ul>
</section>
<section id="config-file-structure">
<h2>Config File Structure<a class="headerlink" href="#config-file-structure" title="Permalink to this heading">¶</a></h2>
<p>There are 4 basic component types under <code class="docutils literal notranslate"><span class="pre">config/_base_</span></code>, dataset, model, schedule, default_runtime.
Many methods could be easily constructed with one of each like Faster R-CNN, Mask R-CNN, Cascade R-CNN, RPN, SSD.
The configs that are composed by components from <code class="docutils literal notranslate"><span class="pre">_base_</span></code> are called <em>primitive</em>.</p>
<p>For all configs under the same folder, it is recommended to have only <strong>one</strong> <em>primitive</em> config. All other configs should inherit from the <em>primitive</em> config. In this way, the maximum of inheritance level is 3.</p>
<p>For easy understanding, we recommend contributors to inherit from existing methods.
For example, if some modification is made base on Faster R-CNN, user may first inherit the basic Faster R-CNN structure by specifying <code class="docutils literal notranslate"><span class="pre">_base_</span> <span class="pre">=</span> <span class="pre">../faster_rcnn/faster_rcnn_r50_fpn_1x_coco.py</span></code>, then modify the necessary fields in the config files.</p>
<p>If you are building an entirely new method that does not share the structure with any of the existing methods, you may create a folder <code class="docutils literal notranslate"><span class="pre">xxx_rcnn</span></code> under <code class="docutils literal notranslate"><span class="pre">configs</span></code>,</p>
<p>Please refer to <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/understand_mmcv/config.html">mmcv</a> for detailed documentation.</p>
</section>
<section id="config-name-style">
<h2>Config Name Style<a class="headerlink" href="#config-name-style" title="Permalink to this heading">¶</a></h2>
<p>We follow the below style to name config files. Contributors are advised to follow the same style.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="p">{</span><span class="n">model</span><span class="p">}</span><span class="n">_</span><span class="p">[</span><span class="n">model</span> <span class="n">setting</span><span class="p">]</span><span class="n">_</span><span class="p">{</span><span class="n">backbone</span><span class="p">}</span><span class="n">_</span><span class="p">{</span><span class="n">neck</span><span class="p">}</span><span class="n">_</span><span class="p">[</span><span class="n">norm</span> <span class="n">setting</span><span class="p">]</span><span class="n">_</span><span class="p">[</span><span class="n">misc</span><span class="p">]</span><span class="n">_</span><span class="p">[</span><span class="n">gpu</span> <span class="n">x</span> <span class="n">batch_per_gpu</span><span class="p">]</span><span class="n">_</span><span class="p">{</span><span class="n">schedule</span><span class="p">}</span><span class="n">_</span><span class="p">{</span><span class="n">dataset</span><span class="p">}</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">{xxx}</span></code> is required field and <code class="docutils literal notranslate"><span class="pre">[yyy]</span></code> is optional.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">{model}</span></code>: model type like <code class="docutils literal notranslate"><span class="pre">faster_rcnn</span></code>, <code class="docutils literal notranslate"><span class="pre">mask_rcnn</span></code>, etc.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[model</span> <span class="pre">setting]</span></code>: specific setting for some model, like <code class="docutils literal notranslate"><span class="pre">without_semantic</span></code> for <code class="docutils literal notranslate"><span class="pre">htc</span></code>, <code class="docutils literal notranslate"><span class="pre">moment</span></code> for <code class="docutils literal notranslate"><span class="pre">reppoints</span></code>, etc.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">{backbone}</span></code>: backbone type like <code class="docutils literal notranslate"><span class="pre">r50</span></code> (ResNet-50), <code class="docutils literal notranslate"><span class="pre">x101</span></code> (ResNeXt-101).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">{neck}</span></code>: neck type like <code class="docutils literal notranslate"><span class="pre">fpn</span></code>, <code class="docutils literal notranslate"><span class="pre">pafpn</span></code>, <code class="docutils literal notranslate"><span class="pre">nasfpn</span></code>, <code class="docutils literal notranslate"><span class="pre">c4</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[norm_setting]</span></code>: <code class="docutils literal notranslate"><span class="pre">bn</span></code> (Batch Normalization) is used unless specified, other norm layer type could be <code class="docutils literal notranslate"><span class="pre">gn</span></code> (Group Normalization), <code class="docutils literal notranslate"><span class="pre">syncbn</span></code> (Synchronized Batch Normalization).
<code class="docutils literal notranslate"><span class="pre">gn-head</span></code>/<code class="docutils literal notranslate"><span class="pre">gn-neck</span></code> indicates GN is applied in head/neck only, while <code class="docutils literal notranslate"><span class="pre">gn-all</span></code> means GN is applied in the entire model, e.g. backbone, neck, head.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[misc]</span></code>: miscellaneous setting/plugins of model, e.g. <code class="docutils literal notranslate"><span class="pre">dconv</span></code>, <code class="docutils literal notranslate"><span class="pre">gcb</span></code>, <code class="docutils literal notranslate"><span class="pre">attention</span></code>, <code class="docutils literal notranslate"><span class="pre">albu</span></code>, <code class="docutils literal notranslate"><span class="pre">mstrain</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[gpu</span> <span class="pre">x</span> <span class="pre">batch_per_gpu]</span></code>: GPUs and samples per GPU, <code class="docutils literal notranslate"><span class="pre">8x2</span></code> is used by default.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">{schedule}</span></code>: training schedule, options are <code class="docutils literal notranslate"><span class="pre">1x</span></code>, <code class="docutils literal notranslate"><span class="pre">2x</span></code>, <code class="docutils literal notranslate"><span class="pre">20e</span></code>, etc.
<code class="docutils literal notranslate"><span class="pre">1x</span></code> and <code class="docutils literal notranslate"><span class="pre">2x</span></code> means 12 epochs and 24 epochs respectively.
<code class="docutils literal notranslate"><span class="pre">20e</span></code> is adopted in cascade models, which denotes 20 epochs.
For <code class="docutils literal notranslate"><span class="pre">1x</span></code>/<code class="docutils literal notranslate"><span class="pre">2x</span></code>, initial learning rate decays by a factor of 10 at the 8/16th and 11/22th epochs.
For <code class="docutils literal notranslate"><span class="pre">20e</span></code>, initial learning rate decays by a factor of 10 at the 16th and 19th epochs.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">{dataset}</span></code>: dataset like <code class="docutils literal notranslate"><span class="pre">coco</span></code>, <code class="docutils literal notranslate"><span class="pre">cityscapes</span></code>, <code class="docutils literal notranslate"><span class="pre">voc_0712</span></code>, <code class="docutils literal notranslate"><span class="pre">wider_face</span></code>.</p></li>
</ul>
</section>
<section id="deprecated-train-cfg-test-cfg">
<h2>Deprecated train_cfg/test_cfg<a class="headerlink" href="#deprecated-train-cfg-test-cfg" title="Permalink to this heading">¶</a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">train_cfg</span></code> and <code class="docutils literal notranslate"><span class="pre">test_cfg</span></code> are deprecated in config file, please specify them in the model config. The original config structure is as below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># deprecated</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
   <span class="nb">type</span><span class="o">=...</span><span class="p">,</span>
   <span class="o">...</span>
<span class="p">)</span>
<span class="n">train_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">)</span>
<span class="n">test_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
<p>The migration example is as below.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># recommended</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
   <span class="nb">type</span><span class="o">=...</span><span class="p">,</span>
   <span class="o">...</span>
   <span class="n">train_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">),</span>
   <span class="n">test_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">),</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="an-example-of-mask-r-cnn">
<h2>An Example of Mask R-CNN<a class="headerlink" href="#an-example-of-mask-r-cnn" title="Permalink to this heading">¶</a></h2>
<p>To help the users have a basic idea of a complete config and the modules in a modern detection system,
we make brief comments on the config of Mask R-CNN using ResNet50 and FPN as the following.
For more detailed usage and the corresponding alternative for each modules, please refer to the API documentation.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span>model = dict(
    type=&#39;MaskRCNN&#39;,  # The name of detector
    backbone=dict(  # The config of backbone
        type=&#39;ResNet&#39;,  # The type of the backbone, refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/backbones/resnet.py#L308 for more details.
        depth=50,  # The depth of backbone, usually it is 50 or 101 for ResNet and ResNext backbones.
        num_stages=4,  # Number of stages of the backbone.
        out_indices=(0, 1, 2, 3),  # The index of output feature maps produced in each stages
        frozen_stages=1,  # The weights in the first 1 stage are frozen
        norm_cfg=dict(  # The config of normalization layers.
            type=&#39;BN&#39;,  # Type of norm layer, usually it is BN or GN
            requires_grad=True),  # Whether to train the gamma and beta in BN
        norm_eval=True,  # Whether to freeze the statistics in BN
        style=&#39;pytorch&#39;， # The style of backbone, &#39;pytorch&#39; means that stride 2 layers are in 3x3 conv, &#39;caffe&#39; means stride 2 layers are in 1x1 convs.
    	init_cfg=dict(type=&#39;Pretrained&#39;, checkpoint=&#39;torchvision://resnet50&#39;)),  # The ImageNet pretrained backbone to be loaded
    neck=dict(
        type=&#39;FPN&#39;,  # The neck of detector is FPN. We also support &#39;NASFPN&#39;, &#39;PAFPN&#39;, etc. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/necks/fpn.py#L10 for more details.
        in_channels=[256, 512, 1024, 2048],  # The input channels, this is consistent with the output channels of backbone
        out_channels=256,  # The output channels of each level of the pyramid feature map
        num_outs=5),  # The number of output scales
    rpn_head=dict(
        type=&#39;RPNHead&#39;,  # The type of RPN head is &#39;RPNHead&#39;, we also support &#39;GARPNHead&#39;, etc. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/dense_heads/rpn_head.py#L12 for more details.
        in_channels=256,  # The input channels of each input feature map, this is consistent with the output channels of neck
        feat_channels=256,  # Feature channels of convolutional layers in the head.
        anchor_generator=dict(  # The config of anchor generator
            type=&#39;AnchorGenerator&#39;,  # Most of methods use AnchorGenerator, SSD Detectors uses `SSDAnchorGenerator`. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/anchor/anchor_generator.py#L10 for more details
            scales=[8],  # Basic scale of the anchor, the area of the anchor in one position of a feature map will be scale * base_sizes
            ratios=[0.5, 1.0, 2.0],  # The ratio between height and width.
            strides=[4, 8, 16, 32, 64]),  # The strides of the anchor generator. This is consistent with the FPN feature strides. The strides will be taken as base_sizes if base_sizes is not set.
        bbox_coder=dict(  # Config of box coder to encode and decode the boxes during training and testing
            type=&#39;DeltaXYWHBBoxCoder&#39;,  # Type of box coder. &#39;DeltaXYWHBBoxCoder&#39; is applied for most of methods. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/bbox/coder/delta_xywh_bbox_coder.py#L9 for more details.
            target_means=[0.0, 0.0, 0.0, 0.0],  # The target means used to encode and decode boxes
            target_stds=[1.0, 1.0, 1.0, 1.0]),  # The standard variance used to encode and decode boxes
        loss_cls=dict(  # Config of loss function for the classification branch
            type=&#39;CrossEntropyLoss&#39;,  # Type of loss for classification branch, we also support FocalLoss etc.
            use_sigmoid=True,  # RPN usually perform two-class classification, so it usually uses sigmoid function.
            loss_weight=1.0),  # Loss weight of the classification branch.
        loss_bbox=dict(  # Config of loss function for the regression branch.
            type=&#39;L1Loss&#39;,  # Type of loss, we also support many IoU Losses and smooth L1-loss, etc. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/losses/smooth_l1_loss.py#L56 for implementation.
            loss_weight=1.0)),  # Loss weight of the regression branch.
    roi_head=dict(  # RoIHead encapsulates the second stage of two-stage/cascade detectors.
        type=&#39;StandardRoIHead&#39;,  # Type of the RoI head. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/roi_heads/standard_roi_head.py#L10 for implementation.
        bbox_roi_extractor=dict(  # RoI feature extractor for bbox regression.
            type=&#39;SingleRoIExtractor&#39;,  # Type of the RoI feature extractor, most of methods uses SingleRoIExtractor. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/roi_heads/roi_extractors/single_level.py#L10 for details.
            roi_layer=dict(  # Config of RoI Layer
                type=&#39;RoIAlign&#39;,  # Type of RoI Layer, DeformRoIPoolingPack and ModulatedDeformRoIPoolingPack are also supported. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/ops/roi_align/roi_align.py#L79 for details.
                output_size=7,  # The output size of feature maps.
                sampling_ratio=0),  # Sampling ratio when extracting the RoI features. 0 means adaptive ratio.
            out_channels=256,  # output channels of the extracted feature.
            featmap_strides=[4, 8, 16, 32]),  # Strides of multi-scale feature maps. It should be consistent to the architecture of the backbone.
        bbox_head=dict(  # Config of box head in the RoIHead.
            type=&#39;Shared2FCBBoxHead&#39;,  # Type of the bbox head, Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/roi_heads/bbox_heads/convfc_bbox_head.py#L177 for implementation details.
            in_channels=256,  # Input channels for bbox head. This is consistent with the out_channels in roi_extractor
            fc_out_channels=1024,  # Output feature channels of FC layers.
            roi_feat_size=7,  # Size of RoI features
            num_classes=80,  # Number of classes for classification
            bbox_coder=dict(  # Box coder used in the second stage.
                type=&#39;DeltaXYWHBBoxCoder&#39;,  # Type of box coder. &#39;DeltaXYWHBBoxCoder&#39; is applied for most of methods.
                target_means=[0.0, 0.0, 0.0, 0.0],  # Means used to encode and decode box
                target_stds=[0.1, 0.1, 0.2, 0.2]),  # Standard variance for encoding and decoding. It is smaller since the boxes are more accurate. [0.1, 0.1, 0.2, 0.2] is a conventional setting.
            reg_class_agnostic=False,  # Whether the regression is class agnostic.
            loss_cls=dict(  # Config of loss function for the classification branch
                type=&#39;CrossEntropyLoss&#39;,  # Type of loss for classification branch, we also support FocalLoss etc.
                use_sigmoid=False,  # Whether to use sigmoid.
                loss_weight=1.0),  # Loss weight of the classification branch.
            loss_bbox=dict(  # Config of loss function for the regression branch.
                type=&#39;L1Loss&#39;,  # Type of loss, we also support many IoU Losses and smooth L1-loss, etc.
                loss_weight=1.0)),  # Loss weight of the regression branch.
        mask_roi_extractor=dict(  # RoI feature extractor for mask generation.
            type=&#39;SingleRoIExtractor&#39;,  # Type of the RoI feature extractor, most of methods uses SingleRoIExtractor.
            roi_layer=dict(  # Config of RoI Layer that extracts features for instance segmentation
                type=&#39;RoIAlign&#39;,  # Type of RoI Layer, DeformRoIPoolingPack and ModulatedDeformRoIPoolingPack are also supported
                output_size=14,  # The output size of feature maps.
                sampling_ratio=0),  # Sampling ratio when extracting the RoI features.
            out_channels=256,  # Output channels of the extracted feature.
            featmap_strides=[4, 8, 16, 32]),  # Strides of multi-scale feature maps.
        mask_head=dict(  # Mask prediction head
            type=&#39;FCNMaskHead&#39;,  # Type of mask head, refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/models/roi_heads/mask_heads/fcn_mask_head.py#L21 for implementation details.
            num_convs=4,  # Number of convolutional layers in mask head.
            in_channels=256,  # Input channels, should be consistent with the output channels of mask roi extractor.
            conv_out_channels=256,  # Output channels of the convolutional layer.
            num_classes=80,  # Number of class to be segmented.
            loss_mask=dict(  # Config of loss function for the mask branch.
                type=&#39;CrossEntropyLoss&#39;,  # Type of loss used for segmentation
                use_mask=True,  # Whether to only train the mask in the correct class.
                loss_weight=1.0))))  # Loss weight of mask branch.
    train_cfg = dict(  # Config of training hyperparameters for rpn and rcnn
        rpn=dict(  # Training config of rpn
            assigner=dict(  # Config of assigner
                type=&#39;MaxIoUAssigner&#39;,  # Type of assigner, MaxIoUAssigner is used for many common detectors. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/bbox/assigners/max_iou_assigner.py#L10 for more details.
                pos_iou_thr=0.7,  # IoU &gt;= threshold 0.7 will be taken as positive samples
                neg_iou_thr=0.3,  # IoU &lt; threshold 0.3 will be taken as negative samples
                min_pos_iou=0.3,  # The minimal IoU threshold to take boxes as positive samples
                match_low_quality=True,  # Whether to match the boxes under low quality (see API doc for more details).
                ignore_iof_thr=-1),  # IoF threshold for ignoring bboxes
            sampler=dict(  # Config of positive/negative sampler
                type=&#39;RandomSampler&#39;,  # Type of sampler, PseudoSampler and other samplers are also supported. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/bbox/samplers/random_sampler.py#L8 for implementation details.
                num=256,  # Number of samples
                pos_fraction=0.5,  # The ratio of positive samples in the total samples.
                neg_pos_ub=-1,  # The upper bound of negative samples based on the number of positive samples.
                add_gt_as_proposals=False),  # Whether add GT as proposals after sampling.
            allowed_border=-1,  # The border allowed after padding for valid anchors.
            pos_weight=-1,  # The weight of positive samples during training.
            debug=False),  # Whether to set the debug mode
        rpn_proposal=dict(  # The config to generate proposals during training
            nms_across_levels=False,  # Whether to do NMS for boxes across levels. Only work in `GARPNHead`, naive rpn does not support do nms cross levels.
            nms_pre=2000,  # The number of boxes before NMS
            nms_post=1000,  # The number of boxes to be kept by NMS, Only work in `GARPNHead`.
            max_per_img=1000,  # The number of boxes to be kept after NMS.
            nms=dict( # Config of NMS
                type=&#39;nms&#39;,  # Type of NMS
                iou_threshold=0.7 # NMS threshold
                ),
            min_bbox_size=0),  # The allowed minimal box size
        rcnn=dict(  # The config for the roi heads.
            assigner=dict(  # Config of assigner for second stage, this is different for that in rpn
                type=&#39;MaxIoUAssigner&#39;,  # Type of assigner, MaxIoUAssigner is used for all roi_heads for now. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/bbox/assigners/max_iou_assigner.py#L10 for more details.
                pos_iou_thr=0.5,  # IoU &gt;= threshold 0.5 will be taken as positive samples
                neg_iou_thr=0.5,  # IoU &lt; threshold 0.5 will be taken as negative samples
                min_pos_iou=0.5,  # The minimal IoU threshold to take boxes as positive samples
                match_low_quality=False,  # Whether to match the boxes under low quality (see API doc for more details).
                ignore_iof_thr=-1),  # IoF threshold for ignoring bboxes
            sampler=dict(
                type=&#39;RandomSampler&#39;,  # Type of sampler, PseudoSampler and other samplers are also supported. Refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/bbox/samplers/random_sampler.py#L8 for implementation details.
                num=512,  # Number of samples
                pos_fraction=0.25,  # The ratio of positive samples in the total samples.
                neg_pos_ub=-1,  # The upper bound of negative samples based on the number of positive samples.
                add_gt_as_proposals=True
            ),  # Whether add GT as proposals after sampling.
            mask_size=28,  # Size of mask
            pos_weight=-1,  # The weight of positive samples during training.
            debug=False))  # Whether to set the debug mode
    test_cfg = dict(  # Config for testing hyperparameters for rpn and rcnn
        rpn=dict(  # The config to generate proposals during testing
            nms_across_levels=False,  # Whether to do NMS for boxes across levels. Only work in `GARPNHead`, naive rpn does not support do nms cross levels.
            nms_pre=1000,  # The number of boxes before NMS
            nms_post=1000,  # The number of boxes to be kept by NMS, Only work in `GARPNHead`.
            max_per_img=1000,  # The number of boxes to be kept after NMS.
            nms=dict( # Config of NMS
                type=&#39;nms&#39;,  #Type of NMS
                iou_threshold=0.7 # NMS threshold
                ),
            min_bbox_size=0),  # The allowed minimal box size
        rcnn=dict(  # The config for the roi heads.
            score_thr=0.05,  # Threshold to filter out boxes
            nms=dict(  # Config of NMS in the second stage
                type=&#39;nms&#39;,  # Type of NMS
                iou_thr=0.5),  # NMS threshold
            max_per_img=100,  # Max number of detections of each image
            mask_thr_binary=0.5))  # Threshold of mask prediction
dataset_type = &#39;CocoDataset&#39;  # Dataset type, this will be used to define the dataset
data_root = &#39;data/coco/&#39;  # Root path of data
img_norm_cfg = dict(  # Image normalization config to normalize the input images
    mean=[123.675, 116.28, 103.53],  # Mean values used to pre-training the pre-trained backbone models
    std=[58.395, 57.12, 57.375],  # Standard variance used to pre-training the pre-trained backbone models
    to_rgb=True
)  # The channel orders of image used to pre-training the pre-trained backbone models
train_pipeline = [  # Training pipeline
    dict(type=&#39;LoadImageFromFile&#39;),  # First pipeline to load images from file path
    dict(
        type=&#39;LoadAnnotations&#39;,  # Second pipeline to load annotations for current image
        with_bbox=True,  # Whether to use bounding box, True for detection
        with_mask=True,  # Whether to use instance mask, True for instance segmentation
        poly2mask=False),  # Whether to convert the polygon mask to instance mask, set False for acceleration and to save memory
    dict(
        type=&#39;Resize&#39;,  # Augmentation pipeline that resize the images and their annotations
        img_scale=(1333, 800),  # The largest scale of image
        keep_ratio=True
    ),  # whether to keep the ratio between height and width.
    dict(
        type=&#39;RandomFlip&#39;,  # Augmentation pipeline that flip the images and their annotations
        flip_ratio=0.5),  # The ratio or probability to flip
    dict(
        type=&#39;Normalize&#39;,  # Augmentation pipeline that normalize the input images
        mean=[123.675, 116.28, 103.53],  # These keys are the same of img_norm_cfg since the
        std=[58.395, 57.12, 57.375],  # keys of img_norm_cfg are used here as arguments
        to_rgb=True),
    dict(
        type=&#39;Pad&#39;,  # Padding config
        size_divisor=32),  # The number the padded images should be divisible
    dict(type=&#39;DefaultFormatBundle&#39;),  # Default format bundle to gather data in the pipeline
    dict(
        type=&#39;Collect&#39;,  # Pipeline that decides which keys in the data should be passed to the detector
        keys=[&#39;img&#39;, &#39;gt_bboxes&#39;, &#39;gt_labels&#39;, &#39;gt_masks&#39;])
]
test_pipeline = [
    dict(type=&#39;LoadImageFromFile&#39;),  # First pipeline to load images from file path
    dict(
        type=&#39;MultiScaleFlipAug&#39;,  # An encapsulation that encapsulates the testing augmentations
        img_scale=(1333, 800),  # Decides the largest scale for testing, used for the Resize pipeline
        flip=False,  # Whether to flip images during testing
        transforms=[
            dict(type=&#39;Resize&#39;,  # Use resize augmentation
                 keep_ratio=True),  # Whether to keep the ratio between height and width, the img_scale set here will be suppressed by the img_scale set above.
            dict(type=&#39;RandomFlip&#39;),  # Thought RandomFlip is added in pipeline, it is not used because flip=False
            dict(
                type=&#39;Normalize&#39;,  # Normalization config, the values are from img_norm_cfg
                mean=[123.675, 116.28, 103.53],
                std=[58.395, 57.12, 57.375],
                to_rgb=True),
            dict(
                type=&#39;Pad&#39;,  # Padding config to pad images divisible by 32.
                size_divisor=32),
            dict(
                type=&#39;ImageToTensor&#39;,  # convert image to tensor
                keys=[&#39;img&#39;]),
            dict(
                type=&#39;Collect&#39;,  # Collect pipeline that collect necessary keys for testing.
                keys=[&#39;img&#39;])
        ])
]
data = dict(
    samples_per_gpu=2,  # Batch size of a single GPU
    workers_per_gpu=2,  # Worker to pre-fetch data for each single GPU
    train=dict(  # Train dataset config
        type=&#39;CocoDataset&#39;,  # Type of dataset, refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/datasets/coco.py#L19 for details.
        ann_file=&#39;data/coco/annotations/instances_train2017.json&#39;,  # Path of annotation file
        img_prefix=&#39;data/coco/train2017/&#39;,  # Prefix of image path
        pipeline=[  # pipeline, this is passed by the train_pipeline created before.
            dict(type=&#39;LoadImageFromFile&#39;),
            dict(
                type=&#39;LoadAnnotations&#39;,
                with_bbox=True,
                with_mask=True,
                poly2mask=False),
            dict(type=&#39;Resize&#39;, img_scale=(1333, 800), keep_ratio=True),
            dict(type=&#39;RandomFlip&#39;, flip_ratio=0.5),
            dict(
                type=&#39;Normalize&#39;,
                mean=[123.675, 116.28, 103.53],
                std=[58.395, 57.12, 57.375],
                to_rgb=True),
            dict(type=&#39;Pad&#39;, size_divisor=32),
            dict(type=&#39;DefaultFormatBundle&#39;),
            dict(
                type=&#39;Collect&#39;,
                keys=[&#39;img&#39;, &#39;gt_bboxes&#39;, &#39;gt_labels&#39;, &#39;gt_masks&#39;])
        ]),
    val=dict(  # Validation dataset config
        type=&#39;CocoDataset&#39;,
        ann_file=&#39;data/coco/annotations/instances_val2017.json&#39;,
        img_prefix=&#39;data/coco/val2017/&#39;,
        pipeline=[  # Pipeline is passed by test_pipeline created before
            dict(type=&#39;LoadImageFromFile&#39;),
            dict(
                type=&#39;MultiScaleFlipAug&#39;,
                img_scale=(1333, 800),
                flip=False,
                transforms=[
                    dict(type=&#39;Resize&#39;, keep_ratio=True),
                    dict(type=&#39;RandomFlip&#39;),
                    dict(
                        type=&#39;Normalize&#39;,
                        mean=[123.675, 116.28, 103.53],
                        std=[58.395, 57.12, 57.375],
                        to_rgb=True),
                    dict(type=&#39;Pad&#39;, size_divisor=32),
                    dict(type=&#39;ImageToTensor&#39;, keys=[&#39;img&#39;]),
                    dict(type=&#39;Collect&#39;, keys=[&#39;img&#39;])
                ])
        ]),
    test=dict(  # Test dataset config, modify the ann_file for test-dev/test submission
        type=&#39;CocoDataset&#39;,
        ann_file=&#39;data/coco/annotations/instances_val2017.json&#39;,
        img_prefix=&#39;data/coco/val2017/&#39;,
        pipeline=[  # Pipeline is passed by test_pipeline created before
            dict(type=&#39;LoadImageFromFile&#39;),
            dict(
                type=&#39;MultiScaleFlipAug&#39;,
                img_scale=(1333, 800),
                flip=False,
                transforms=[
                    dict(type=&#39;Resize&#39;, keep_ratio=True),
                    dict(type=&#39;RandomFlip&#39;),
                    dict(
                        type=&#39;Normalize&#39;,
                        mean=[123.675, 116.28, 103.53],
                        std=[58.395, 57.12, 57.375],
                        to_rgb=True),
                    dict(type=&#39;Pad&#39;, size_divisor=32),
                    dict(type=&#39;ImageToTensor&#39;, keys=[&#39;img&#39;]),
                    dict(type=&#39;Collect&#39;, keys=[&#39;img&#39;])
                ])
        ],
        samples_per_gpu=2  # Batch size of a single GPU used in testing
        ))
evaluation = dict(  # The config to build the evaluation hook, refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/evaluation/eval_hooks.py#L7 for more details.
    interval=1,  # Evaluation interval
    metric=[&#39;bbox&#39;, &#39;segm&#39;])  # Metrics used during evaluation
optimizer = dict(  # Config used to build optimizer, support all the optimizers in PyTorch whose arguments are also the same as those in PyTorch
    type=&#39;SGD&#39;,  # Type of optimizers, refer to https://github.com/open-mmlab/rsidetection/blob/master/rsidet/core/optimizer/default_constructor.py#L13 for more details
    lr=0.02,  # Learning rate of optimizers, see detail usages of the parameters in the documentation of PyTorch
    momentum=0.9,  # Momentum
    weight_decay=0.0001)  # Weight decay of SGD
optimizer_config = dict(  # Config used to build the optimizer hook, refer to https://github.com/open-mmlab/mmcv/blob/master/mmcv/runner/hooks/optimizer.py#L8 for implementation details.
    grad_clip=None)  # Most of the methods do not use gradient clip
lr_config = dict(  # Learning rate scheduler config used to register LrUpdater hook
    policy=&#39;step&#39;,  # The policy of scheduler, also support CosineAnnealing, Cyclic, etc. Refer to details of supported LrUpdater from https://github.com/open-mmlab/mmcv/blob/master/mmcv/runner/hooks/lr_updater.py#L9.
    warmup=&#39;linear&#39;,  # The warmup policy, also support `exp` and `constant`.
    warmup_iters=500,  # The number of iterations for warmup
    warmup_ratio=
    0.001,  # The ratio of the starting learning rate used for warmup
    step=[8, 11])  # Steps to decay the learning rate
runner = dict(
    type=&#39;EpochBasedRunner&#39;, # Type of runner to use (i.e. IterBasedRunner or EpochBasedRunner)
    max_epochs=12) # Runner that runs the workflow in total max_epochs. For IterBasedRunner use `max_iters`
checkpoint_config = dict(  # Config to set the checkpoint hook, Refer to https://github.com/open-mmlab/mmcv/blob/master/mmcv/runner/hooks/checkpoint.py for implementation.
    interval=1)  # The save interval is 1
log_config = dict(  # config to register logger hook
    interval=50,  # Interval to print the log
    hooks=[
        # dict(type=&#39;TensorboardLoggerHook&#39;)  # The Tensorboard logger is also supported
        dict(type=&#39;TextLoggerHook&#39;)
    ])  # The logger used to record the training process.
dist_params = dict(backend=&#39;nccl&#39;)  # Parameters to setup distributed training, the port can also be set.
log_level = &#39;INFO&#39;  # The level of logging.
load_from = None  # load models as a pre-trained model from a given path. This will not resume training.
resume_from = None  # Resume checkpoints from a given path, the training will be resumed from the epoch when the checkpoint&#39;s is saved.
workflow = [(&#39;train&#39;, 1)]  # Workflow for runner. [(&#39;train&#39;, 1)] means there is only one workflow and the workflow named &#39;train&#39; is executed once. The workflow trains the model by 12 epochs according to the total_epochs.
work_dir = &#39;work_dir&#39;  # Directory to save the model checkpoints and logs for the current experiments.
</pre></div>
</div>
</section>
<section id="faq">
<h2>FAQ<a class="headerlink" href="#faq" title="Permalink to this heading">¶</a></h2>
<section id="ignore-some-fields-in-the-base-configs">
<h3>Ignore some fields in the base configs<a class="headerlink" href="#ignore-some-fields-in-the-base-configs" title="Permalink to this heading">¶</a></h3>
<p>Sometimes, you may set <code class="docutils literal notranslate"><span class="pre">_delete_=True</span></code> to ignore some of fields in base configs.
You may refer to <a class="reference external" href="https://mmcv.readthedocs.io/en/latest/understand_mmcv/config.html#inherit-from-base-config-with-ignored-fields">mmcv</a> for simple illustration.</p>
<p>In MMDetection, for example, to change the backbone of Mask R-CNN with the following config.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MaskRCNN&#39;</span><span class="p">,</span>
    <span class="n">pretrained</span><span class="o">=</span><span class="s1">&#39;torchvision://resnet50&#39;</span><span class="p">,</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ResNet&#39;</span><span class="p">,</span>
        <span class="n">depth</span><span class="o">=</span><span class="mi">50</span><span class="p">,</span>
        <span class="n">num_stages</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
        <span class="n">out_indices</span><span class="o">=</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>
        <span class="n">frozen_stages</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">norm_cfg</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;BN&#39;</span><span class="p">,</span> <span class="n">requires_grad</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
        <span class="n">norm_eval</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
        <span class="n">style</span><span class="o">=</span><span class="s1">&#39;pytorch&#39;</span><span class="p">),</span>
    <span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">),</span>
    <span class="n">rpn_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">),</span>
    <span class="n">roi_head</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">))</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">ResNet</span></code> and <code class="docutils literal notranslate"><span class="pre">HRNet</span></code> use different keywords to construct.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;../mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py&#39;</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">pretrained</span><span class="o">=</span><span class="s1">&#39;open-mmlab://msra/hrnetv2_w32&#39;</span><span class="p">,</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
        <span class="n">_delete_</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;HRNet&#39;</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
            <span class="n">stage1</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="n">num_modules</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                <span class="n">num_branches</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                <span class="n">block</span><span class="o">=</span><span class="s1">&#39;BOTTLENECK&#39;</span><span class="p">,</span>
                <span class="n">num_blocks</span><span class="o">=</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="p">),</span>
                <span class="n">num_channels</span><span class="o">=</span><span class="p">(</span><span class="mi">64</span><span class="p">,</span> <span class="p">)),</span>
            <span class="n">stage2</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="n">num_modules</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                <span class="n">num_branches</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
                <span class="n">block</span><span class="o">=</span><span class="s1">&#39;BASIC&#39;</span><span class="p">,</span>
                <span class="n">num_blocks</span><span class="o">=</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
                <span class="n">num_channels</span><span class="o">=</span><span class="p">(</span><span class="mi">32</span><span class="p">,</span> <span class="mi">64</span><span class="p">)),</span>
            <span class="n">stage3</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="n">num_modules</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
                <span class="n">num_branches</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span>
                <span class="n">block</span><span class="o">=</span><span class="s1">&#39;BASIC&#39;</span><span class="p">,</span>
                <span class="n">num_blocks</span><span class="o">=</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
                <span class="n">num_channels</span><span class="o">=</span><span class="p">(</span><span class="mi">32</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">128</span><span class="p">)),</span>
            <span class="n">stage4</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span>
                <span class="n">num_modules</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span>
                <span class="n">num_branches</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
                <span class="n">block</span><span class="o">=</span><span class="s1">&#39;BASIC&#39;</span><span class="p">,</span>
                <span class="n">num_blocks</span><span class="o">=</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
                <span class="n">num_channels</span><span class="o">=</span><span class="p">(</span><span class="mi">32</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">128</span><span class="p">,</span> <span class="mi">256</span><span class="p">)))),</span>
    <span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="o">...</span><span class="p">))</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">_delete_=True</span></code> would replace all old keys in <code class="docutils literal notranslate"><span class="pre">backbone</span></code> field with new keys.</p>
</section>
<section id="use-intermediate-variables-in-configs">
<h3>Use intermediate variables in configs<a class="headerlink" href="#use-intermediate-variables-in-configs" title="Permalink to this heading">¶</a></h3>
<p>Some intermediate variables are used in the configs files, like <code class="docutils literal notranslate"><span class="pre">train_pipeline</span></code>/<code class="docutils literal notranslate"><span class="pre">test_pipeline</span></code> in datasets.
It’s worth noting that when modifying intermediate variables in the children configs, user need to pass the intermediate variables into corresponding fields again.
For example, we would like to use multi scale strategy to train a Mask R-CNN. <code class="docutils literal notranslate"><span class="pre">train_pipeline</span></code>/<code class="docutils literal notranslate"><span class="pre">test_pipeline</span></code> are intermediate variable we would like modify.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;./mask_rcnn_r50_fpn_1x_coco.py&#39;</span>
<span class="n">img_norm_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">mean</span><span class="o">=</span><span class="p">[</span><span class="mf">123.675</span><span class="p">,</span> <span class="mf">116.28</span><span class="p">,</span> <span class="mf">103.53</span><span class="p">],</span> <span class="n">std</span><span class="o">=</span><span class="p">[</span><span class="mf">58.395</span><span class="p">,</span> <span class="mf">57.12</span><span class="p">,</span> <span class="mf">57.375</span><span class="p">],</span> <span class="n">to_rgb</span><span class="o">=</span><span class="bp">True</span><span class="p">)</span>
<span class="n">train_pipeline</span> <span class="o">=</span> <span class="p">[</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;LoadImageFromFile&#39;</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;LoadAnnotations&#39;</span><span class="p">,</span> <span class="n">with_bbox</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span> <span class="n">with_mask</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Resize&#39;</span><span class="p">,</span>
        <span class="n">img_scale</span><span class="o">=</span><span class="p">[(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">640</span><span class="p">),</span> <span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">672</span><span class="p">),</span> <span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">704</span><span class="p">),</span> <span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">736</span><span class="p">),</span>
                   <span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">768</span><span class="p">),</span> <span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">800</span><span class="p">)],</span>
        <span class="n">multiscale_mode</span><span class="o">=</span><span class="s2">&quot;value&quot;</span><span class="p">,</span>
        <span class="n">keep_ratio</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RandomFlip&#39;</span><span class="p">,</span> <span class="n">flip_ratio</span><span class="o">=</span><span class="mf">0.5</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Normalize&#39;</span><span class="p">,</span> <span class="o">**</span><span class="n">img_norm_cfg</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pad&#39;</span><span class="p">,</span> <span class="n">size_divisor</span><span class="o">=</span><span class="mi">32</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;DefaultFormatBundle&#39;</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Collect&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">,</span> <span class="s1">&#39;gt_bboxes&#39;</span><span class="p">,</span> <span class="s1">&#39;gt_labels&#39;</span><span class="p">,</span> <span class="s1">&#39;gt_masks&#39;</span><span class="p">]),</span>
<span class="p">]</span>
<span class="n">test_pipeline</span> <span class="o">=</span> <span class="p">[</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;LoadImageFromFile&#39;</span><span class="p">),</span>
    <span class="nb">dict</span><span class="p">(</span>
        <span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MultiScaleFlipAug&#39;</span><span class="p">,</span>
        <span class="n">img_scale</span><span class="o">=</span><span class="p">(</span><span class="mi">1333</span><span class="p">,</span> <span class="mi">800</span><span class="p">),</span>
        <span class="n">flip</span><span class="o">=</span><span class="bp">False</span><span class="p">,</span>
        <span class="n">transforms</span><span class="o">=</span><span class="p">[</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Resize&#39;</span><span class="p">,</span> <span class="n">keep_ratio</span><span class="o">=</span><span class="bp">True</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;RandomFlip&#39;</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Normalize&#39;</span><span class="p">,</span> <span class="o">**</span><span class="n">img_norm_cfg</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Pad&#39;</span><span class="p">,</span> <span class="n">size_divisor</span><span class="o">=</span><span class="mi">32</span><span class="p">),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;ImageToTensor&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">]),</span>
            <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;Collect&#39;</span><span class="p">,</span> <span class="n">keys</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;img&#39;</span><span class="p">]),</span>
        <span class="p">])</span>
<span class="p">]</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">pipeline</span><span class="o">=</span><span class="n">train_pipeline</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">pipeline</span><span class="o">=</span><span class="n">test_pipeline</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">pipeline</span><span class="o">=</span><span class="n">test_pipeline</span><span class="p">))</span>
</pre></div>
</div>
<p>We first define the new <code class="docutils literal notranslate"><span class="pre">train_pipeline</span></code>/<code class="docutils literal notranslate"><span class="pre">test_pipeline</span></code> and pass them into <code class="docutils literal notranslate"><span class="pre">data</span></code>.</p>
<p>Similarly, if we would like to switch from <code class="docutils literal notranslate"><span class="pre">SyncBN</span></code> to <code class="docutils literal notranslate"><span class="pre">BN</span></code> or <code class="docutils literal notranslate"><span class="pre">MMSyncBN</span></code>, we need to substitute every <code class="docutils literal notranslate"><span class="pre">norm_cfg</span></code> in the config.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">_base_</span> <span class="o">=</span> <span class="s1">&#39;./mask_rcnn_r50_fpn_1x_coco.py&#39;</span>
<span class="n">norm_cfg</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;BN&#39;</span><span class="p">,</span> <span class="n">requires_grad</span><span class="o">=</span><span class="bp">True</span><span class="p">)</span>
<span class="n">model</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">backbone</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">norm_cfg</span><span class="o">=</span><span class="n">norm_cfg</span><span class="p">),</span>
    <span class="n">neck</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">norm_cfg</span><span class="o">=</span><span class="n">norm_cfg</span><span class="p">),</span>
    <span class="o">...</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="customize_dataset.html" class="btn btn-neutral float-right" title="Tutorial 2: Customize Datasets" accesskey="n"
      rel="next">Next <img src="../_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="index.html" class="btn btn-neutral" title="&lt;no title&gt;" accesskey="p"
      rel="prev"><img src="../_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Tutorial 1: Learn about Configs</a><ul>
<li><a class="reference internal" href="#modify-config-through-script-arguments">Modify config through script arguments</a></li>
<li><a class="reference internal" href="#config-file-structure">Config File Structure</a></li>
<li><a class="reference internal" href="#config-name-style">Config Name Style</a></li>
<li><a class="reference internal" href="#deprecated-train-cfg-test-cfg">Deprecated train_cfg/test_cfg</a></li>
<li><a class="reference internal" href="#an-example-of-mask-r-cnn">An Example of Mask R-CNN</a></li>
<li><a class="reference internal" href="#faq">FAQ</a><ul>
<li><a class="reference internal" href="#ignore-some-fields-in-the-base-configs">Ignore some fields in the base configs</a></li>
<li><a class="reference internal" href="#use-intermediate-variables-in-configs">Use intermediate variables in configs</a></li>
</ul>
</li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="../"
    src="../_static/documentation_options.js"></script>
  <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
  <script src="../_static/jquery.js"></script>
  <script src="../_static/underscore.js"></script>
  <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="../_static/doctools.js"></script>
  <script src="../_static/clipboard.min.js"></script>
  <script src="../_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="../_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="../_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="../_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="../_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>