


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Compatibility of MMDetection 2.x &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="Projects based on MMDetection" href="projects.html" />
  <link rel="prev" title="Conventions" href="conventions.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>Compatibility of MMDetection 2.x</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/compatibility.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="compatibility-of-mmdetection-2-x">
<h1>Compatibility of MMDetection 2.x<a class="headerlink" href="#compatibility-of-mmdetection-2-x" title="Permalink to this heading">¶</a></h1>
<section id="mmdetection-2-25-0">
<h2>MMDetection 2.25.0<a class="headerlink" href="#mmdetection-2-25-0" title="Permalink to this heading">¶</a></h2>
<p>In order to support Mask2Former for instance segmentation, the original config files of Mask2Former for panpotic segmentation need to be renamed <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/7571">PR #7571</a>.</p>
<table align="center">
    <thead>
        <tr align='center'>
            <td>before v2.25.0</td>
            <td>after v2.25.0</td>
        </tr>
    </thead>
    <tbody><tr valign='top'>
    <th>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;mask2former_xxx_coco.py&#39;</span> <span class="n">represents</span> <span class="n">config</span> <span class="n">files</span> <span class="k">for</span> <span class="o">**</span><span class="n">panoptic</span> <span class="n">segmentation</span><span class="o">**.</span>
</pre></div>
</div>
</th>
    <th>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;mask2former_xxx_coco.py&#39;</span> <span class="n">represents</span> <span class="n">config</span> <span class="n">files</span> <span class="k">for</span> <span class="o">**</span><span class="n">instance</span> <span class="n">segmentation</span><span class="o">**.</span>
<span class="s1">&#39;mask2former_xxx_coco-panoptic.py&#39;</span> <span class="n">represents</span> <span class="n">config</span> <span class="n">files</span> <span class="k">for</span> <span class="o">**</span><span class="n">panoptic</span> <span class="n">segmentation</span><span class="o">**.</span>
</pre></div>
</div>
</th></tr>
  </tbody></table>
</section>
<section id="mmdetection-2-21-0">
<h2>MMDetection 2.21.0<a class="headerlink" href="#mmdetection-2-21-0" title="Permalink to this heading">¶</a></h2>
<p>In order to support CPU training, the logic of scatter in batch collating has been changed. We recommend to use
MMCV v1.4.4 or higher. For more details, please refer to <a class="reference external" href="https://github.com/open-mmlab/mmcv/pull/1621">MMCV PR #1621</a>.</p>
</section>
<section id="mmdetection-2-18-1">
<h2>MMDetection 2.18.1<a class="headerlink" href="#mmdetection-2-18-1" title="Permalink to this heading">¶</a></h2>
<section id="mmcv-compatibility">
<h3>MMCV compatibility<a class="headerlink" href="#mmcv-compatibility" title="Permalink to this heading">¶</a></h3>
<p>In order to fix the wrong weight reference bug in BaseTransformerLayer, the logic in batch first mode of MultiheadAttention has been changed.
We recommend to use MMCV v1.3.17 or higher. For more details, please refer to <a class="reference external" href="https://github.com/open-mmlab/mmcv/pull/1418">MMCV PR #1418</a>.</p>
</section>
</section>
<section id="mmdetection-2-18-0">
<h2>MMDetection 2.18.0<a class="headerlink" href="#mmdetection-2-18-0" title="Permalink to this heading">¶</a></h2>
<section id="diihead-compatibility">
<h3>DIIHead compatibility<a class="headerlink" href="#diihead-compatibility" title="Permalink to this heading">¶</a></h3>
<p>In order to support QueryInst, attn_feats is added into the returned tuple of DIIHead.</p>
</section>
</section>
<section id="mmdetection-2-14-0">
<h2>MMDetection 2.14.0<a class="headerlink" href="#mmdetection-2-14-0" title="Permalink to this heading">¶</a></h2>
<section id="mmcv-version">
<h3>MMCV Version<a class="headerlink" href="#mmcv-version" title="Permalink to this heading">¶</a></h3>
<p>In order to fix the problem that the priority of EvalHook is too low, all hook priorities have been re-adjusted in 1.3.8, so MMDetection 2.14.0 needs to rely on the latest MMCV 1.3.8 version. For related information, please refer to <a class="reference external" href="https://github.com/open-mmlab/mmcv/pull/1120">#1120</a>, for related issues, please refer to <a class="reference external" href="https://github.com/open-mmlab/rsidetection/issues/5343">#5343</a>.</p>
</section>
<section id="ssd-compatibility">
<h3>SSD compatibility<a class="headerlink" href="#ssd-compatibility" title="Permalink to this heading">¶</a></h3>
<p>In v2.14.0, to make SSD more flexible to use, <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/5291">PR5291</a> refactored its backbone, neck and head. The users can use the script <code class="docutils literal notranslate"><span class="pre">tools/model_converters/upgrade_ssd_version.py</span></code> to convert their models.</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python tools/model_converters/upgrade_ssd_version.py <span class="si">${</span><span class="nv">OLD_MODEL_PATH</span><span class="si">}</span> <span class="si">${</span><span class="nv">NEW_MODEL_PATH</span><span class="si">}</span>
</pre></div>
</div>
<ul class="simple">
<li><p>OLD_MODEL_PATH: the path to load the old version SSD model.</p></li>
<li><p>NEW_MODEL_PATH: the path to save the converted model weights.</p></li>
</ul>
</section>
</section>
<section id="mmdetection-2-12-0">
<h2>MMDetection 2.12.0<a class="headerlink" href="#mmdetection-2-12-0" title="Permalink to this heading">¶</a></h2>
<p>MMDetection is going through big refactoring for more general and convenient usages during the releases from v2.12.0 to v2.18.0 (maybe longer).
In v2.12.0 MMDetection inevitably brings some BC-breakings, including the MMCV dependency, model initialization, model registry, and mask AP evaluation.</p>
<section id="id1">
<h3>MMCV Version<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<p>MMDetection v2.12.0 relies on the newest features in MMCV 1.3.3, including <code class="docutils literal notranslate"><span class="pre">BaseModule</span></code> for unified parameter initialization, model registry, and the CUDA operator <code class="docutils literal notranslate"><span class="pre">MultiScaleDeformableAttn</span></code> for <a class="reference external" href="https://arxiv.org/abs/2010.04159">Deformable DETR</a>. Note that MMCV 1.3.2 already contains all the features used by MMDet but has known issues. Therefore, we recommend users to skip MMCV v1.3.2 and use v1.3.2, though v1.3.2 might work for most of the cases.</p>
</section>
<section id="unified-model-initialization">
<h3>Unified model initialization<a class="headerlink" href="#unified-model-initialization" title="Permalink to this heading">¶</a></h3>
<p>To unify the parameter initialization in OpenMMLab projects, MMCV supports <code class="docutils literal notranslate"><span class="pre">BaseModule</span></code> that accepts <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> to allow the modules’ parameters initialized in a flexible and unified manner. Now the users need to explicitly call <code class="docutils literal notranslate"><span class="pre">model.init_weights()</span></code> in the training script to initialize the model (as in <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/train.py#L162">here</a>, previously this was handled by the detector. <strong>The downstream projects must update their model initialization accordingly to use MMDetection v2.12.0</strong>. Please refer to PR #4750 for details.</p>
</section>
<section id="unified-model-registry">
<h3>Unified model registry<a class="headerlink" href="#unified-model-registry" title="Permalink to this heading">¶</a></h3>
<p>To easily use backbones implemented in other OpenMMLab projects, MMDetection v2.12.0 inherits the model registry created in MMCV (#760). In this way, as long as the backbone is supported in an OpenMMLab project and that project also uses the registry in MMCV, users can use that backbone in MMDetection by simply modifying the config without copying the code of that backbone into MMDetection. Please refer to PR #5059 for more details.</p>
</section>
<section id="mask-ap-evaluation">
<h3>Mask AP evaluation<a class="headerlink" href="#mask-ap-evaluation" title="Permalink to this heading">¶</a></h3>
<p>Before <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/4898">PR 4898</a> and V2.12.0, the mask AP of small, medium, and large instances is calculated based on the bounding box area rather than the real mask area. This leads to higher <code class="docutils literal notranslate"><span class="pre">APs</span></code> and <code class="docutils literal notranslate"><span class="pre">APm</span></code> but lower <code class="docutils literal notranslate"><span class="pre">APl</span></code> but will not affect the overall mask AP. <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/4898">PR 4898</a> change it to use mask areas by deleting <code class="docutils literal notranslate"><span class="pre">bbox</span></code> in mask AP calculation.
The new calculation does not affect the overall mask AP evaluation and is consistent with <a class="reference external" href="https://github.com/facebookresearch/detectron2/">Detectron2</a>.</p>
</section>
</section>
<section id="compatibility-with-mmdetection-1-x">
<h2>Compatibility with MMDetection 1.x<a class="headerlink" href="#compatibility-with-mmdetection-1-x" title="Permalink to this heading">¶</a></h2>
<p>MMDetection 2.0 goes through a big refactoring and addresses many legacy issues. It is not compatible with the 1.x version, i.e., running inference with the same model weights in these two versions will produce different results. Thus, MMDetection 2.0 re-benchmarks all the models and provides their links and logs in the model zoo.</p>
<p>The major differences are in four folds: coordinate system, codebase conventions, training hyperparameters, and modular design.</p>
<section id="coordinate-system">
<h3>Coordinate System<a class="headerlink" href="#coordinate-system" title="Permalink to this heading">¶</a></h3>
<p>The new coordinate system is consistent with <a class="reference external" href="https://github.com/facebookresearch/detectron2/">Detectron2</a> and treats the center of the most left-top pixel as (0, 0) rather than the left-top corner of that pixel.
Accordingly, the system interprets the coordinates in COCO bounding box and segmentation annotations as coordinates in range <code class="docutils literal notranslate"><span class="pre">[0,</span> <span class="pre">width]</span></code> or <code class="docutils literal notranslate"><span class="pre">[0,</span> <span class="pre">height]</span></code>.
This modification affects all the computation related to the bbox and pixel selection,
which is more natural and accurate.</p>
<ul class="simple">
<li><p>The height and width of a box with corners (x1, y1) and (x2, y2) in the new coordinate system is computed as <code class="docutils literal notranslate"><span class="pre">width</span> <span class="pre">=</span> <span class="pre">x2</span> <span class="pre">-</span> <span class="pre">x1</span></code> and <code class="docutils literal notranslate"><span class="pre">height</span> <span class="pre">=</span> <span class="pre">y2</span> <span class="pre">-</span> <span class="pre">y1</span></code>.
In MMDetection 1.x and previous version, a “+ 1” was added both height and width.
This modification are in three folds:</p>
<ol class="arabic simple">
<li><p>Box transformation and encoding/decoding in regression.</p></li>
<li><p>IoU calculation. This affects the matching process between ground truth and bounding box and the NMS process. The effect to compatibility is very negligible, though.</p></li>
<li><p>The corners of bounding box is in float type and no longer quantized. This should provide more accurate bounding box results. This also makes the bounding box and RoIs not required to have minimum size of 1, whose effect is small, though.</p></li>
</ol>
</li>
<li><p>The anchors are center-aligned to feature grid points and in float type.
In MMDetection 1.x and previous version, the anchors are in <code class="docutils literal notranslate"><span class="pre">int</span></code> type and not center-aligned.
This affects the anchor generation in RPN and all the anchor-based methods.</p></li>
<li><p>ROIAlign is better aligned with the image coordinate system. The new implementation is adopted from <a class="reference external" href="https://github.com/facebookresearch/detectron2/tree/master/detectron2/layers/csrc/ROIAlign">Detectron2</a>.
The RoIs are shifted by half a pixel by default when they are used to cropping RoI features, compared to MMDetection 1.x.
The old behavior is still available by setting <code class="docutils literal notranslate"><span class="pre">aligned=False</span></code> instead of <code class="docutils literal notranslate"><span class="pre">aligned=True</span></code>.</p></li>
<li><p>Mask cropping and pasting are more accurate.</p>
<ol class="arabic simple">
<li><p>We use the new RoIAlign to crop mask targets. In MMDetection 1.x, the bounding box is quantized before it is used to crop mask target, and the crop process is implemented by numpy. In new implementation, the bounding box for crop is not quantized and sent to RoIAlign. This implementation accelerates the training speed by a large margin (~0.1s per iter, ~2 hour when training Mask R50 for 1x schedule) and should be more accurate.</p></li>
<li><p>In MMDetection 2.0, the “<code class="docutils literal notranslate"><span class="pre">paste_mask()</span></code>” function is different and should be more accurate than those in previous versions. This change follows the modification in <a class="reference external" href="https://github.com/facebookresearch/detectron2/blob/master/detectron2/structures/masks.py">Detectron2</a> and can improve mask AP on COCO by ~0.5% absolute.</p></li>
</ol>
</li>
</ul>
</section>
<section id="codebase-conventions">
<h3>Codebase Conventions<a class="headerlink" href="#codebase-conventions" title="Permalink to this heading">¶</a></h3>
<ul>
<li><p>MMDetection 2.0 changes the order of class labels to reduce unused parameters in regression and mask branch more naturally (without +1 and -1).
This effect all the classification layers of the model to have a different ordering of class labels. The final layers of regression branch and mask head no longer keep K+1 channels for K categories, and their class orders are consistent with the classification branch.</p>
<ul class="simple">
<li><p>In MMDetection 2.0, label “K” means background, and labels [0, K-1] correspond to the K = num_categories object categories.</p></li>
<li><p>In MMDetection 1.x and previous version, label “0” means background, and labels [1, K] correspond to the K categories.</p></li>
<li><p><strong>Note</strong>: The class order of softmax RPN is still the same as that in 1.x in versions&lt;=2.4.0 while sigmoid RPN is not affected. The class orders in all heads are unified since MMDetection v2.5.0.</p></li>
</ul>
</li>
<li><p>Low quality matching in R-CNN is not used. In MMDetection 1.x and previous versions, the <code class="docutils literal notranslate"><span class="pre">max_iou_assigner</span></code> will match low quality boxes for each ground truth box in both RPN and R-CNN training. We observe this sometimes does not assign the most perfect GT box to some bounding boxes,
thus MMDetection 2.0 do not allow low quality matching by default in R-CNN training in the new system. This sometimes may slightly improve the box AP (~0.1% absolute).</p></li>
<li><p>Separate scale factors for width and height. In MMDetection 1.x and previous versions, the scale factor is a single float in mode <code class="docutils literal notranslate"><span class="pre">keep_ratio=True</span></code>. This is slightly inaccurate because the scale factors for width and height have slight difference. MMDetection 2.0 adopts separate scale factors for width and height, the improvement on AP ~0.1% absolute.</p></li>
<li><p>Configs name conventions are changed. MMDetection V2.0 adopts the new name convention to maintain the gradually growing model zoo as the following:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="o">[</span>model<span class="o">]</span>_<span class="o">(</span>model setting<span class="o">)</span>_<span class="o">[</span>backbone<span class="o">]</span>_<span class="o">[</span>neck<span class="o">]</span>_<span class="o">(</span>norm setting<span class="o">)</span>_<span class="o">(</span>misc<span class="o">)</span>_<span class="o">(</span>gpu x batch<span class="o">)</span>_<span class="o">[</span>schedule<span class="o">]</span>_<span class="o">[</span>dataset<span class="o">]</span>.py,
</pre></div>
</div>
<p>where the (<code class="docutils literal notranslate"><span class="pre">misc</span></code>) includes DCN and GCBlock, etc. More details are illustrated in the <a class="reference internal" href="tutorials/config.html"><span class="doc std std-doc">documentation for config</span></a></p>
</li>
<li><p>MMDetection V2.0 uses new ResNet Caffe backbones to reduce warnings when loading pre-trained models. Most of the new backbones’ weights are the same as the former ones but do not have <code class="docutils literal notranslate"><span class="pre">conv.bias</span></code>, except that they use a different <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code>. Thus, the new backbone will not cause warning of unexpected keys.</p></li>
</ul>
</section>
<section id="training-hyperparameters">
<h3>Training Hyperparameters<a class="headerlink" href="#training-hyperparameters" title="Permalink to this heading">¶</a></h3>
<p>The change in training hyperparameters does not affect
model-level compatibility but slightly improves the performance. The major ones are:</p>
<ul class="simple">
<li><p>The number of proposals after nms is changed from 2000 to 1000 by setting <code class="docutils literal notranslate"><span class="pre">nms_post=1000</span></code> and <code class="docutils literal notranslate"><span class="pre">max_num=1000</span></code>.
This slightly improves both mask AP and bbox AP by ~0.2% absolute.</p></li>
<li><p>The default box regression losses for Mask R-CNN, Faster R-CNN and RetinaNet are changed from smooth L1 Loss to L1 loss. This leads to an overall improvement in box AP (~0.6% absolute). However, using L1-loss for other methods such as Cascade R-CNN and HTC does not improve the performance, so we keep the original settings for these methods.</p></li>
<li><p>The sample num of RoIAlign layer is set to be 0 for simplicity. This leads to slightly improvement on mask AP (~0.2% absolute).</p></li>
<li><p>The default setting does not use gradient clipping anymore during training for faster training speed. This does not degrade performance of the most of models. For some models such as RepPoints we keep using gradient clipping to stabilize the training process and to obtain better performance.</p></li>
<li><p>The default warmup ratio is changed from 1/3 to 0.001 for a more smooth warming up process since the gradient clipping is usually not used. The effect is found negligible during our re-benchmarking, though.</p></li>
</ul>
</section>
<section id="upgrade-models-from-1-x-to-2-0">
<h3>Upgrade Models from 1.x to 2.0<a class="headerlink" href="#upgrade-models-from-1-x-to-2-0" title="Permalink to this heading">¶</a></h3>
<p>To convert the models trained by MMDetection V1.x to MMDetection V2.0, the users can use the script <code class="docutils literal notranslate"><span class="pre">tools/model_converters/upgrade_model_version.py</span></code> to convert
their models. The converted models can be run in MMDetection V2.0 with slightly dropped performance (less than 1% AP absolute).
Details can be found in <code class="docutils literal notranslate"><span class="pre">configs/legacy</span></code>.</p>
</section>
</section>
<section id="pycocotools-compatibility">
<h2>pycocotools compatibility<a class="headerlink" href="#pycocotools-compatibility" title="Permalink to this heading">¶</a></h2>
<p><code class="docutils literal notranslate"><span class="pre">mmpycocotools</span></code> is the OpenMMlab’s fork of official <code class="docutils literal notranslate"><span class="pre">pycocotools</span></code>, which works for both MMDetection and Detectron2.
Before <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/4939">PR 4939</a>, since <code class="docutils literal notranslate"><span class="pre">pycocotools</span></code> and <code class="docutils literal notranslate"><span class="pre">mmpycocotool</span></code> have the same package name, if users already installed <code class="docutils literal notranslate"><span class="pre">pycocotools</span></code> (installed Detectron2 first under the same environment), then the setup of MMDetection will skip installing <code class="docutils literal notranslate"><span class="pre">mmpycocotool</span></code>. Thus MMDetection fails due to the missing <code class="docutils literal notranslate"><span class="pre">mmpycocotools</span></code>.
If MMDetection is installed before Detectron2, they could work under the same environment.
<a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/4939">PR 4939</a> deprecates mmpycocotools in favor of official pycocotools.
Users may install MMDetection and Detectron2 under the same environment after <a class="reference external" href="https://github.com/open-mmlab/rsidetection/pull/4939">PR 4939</a>, no matter what the installation order is.</p>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="projects.html" class="btn btn-neutral float-right" title="Projects based on MMDetection" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="conventions.html" class="btn btn-neutral" title="Conventions" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Compatibility of MMDetection 2.x</a><ul>
<li><a class="reference internal" href="#mmdetection-2-25-0">MMDetection 2.25.0</a></li>
<li><a class="reference internal" href="#mmdetection-2-21-0">MMDetection 2.21.0</a></li>
<li><a class="reference internal" href="#mmdetection-2-18-1">MMDetection 2.18.1</a><ul>
<li><a class="reference internal" href="#mmcv-compatibility">MMCV compatibility</a></li>
</ul>
</li>
<li><a class="reference internal" href="#mmdetection-2-18-0">MMDetection 2.18.0</a><ul>
<li><a class="reference internal" href="#diihead-compatibility">DIIHead compatibility</a></li>
</ul>
</li>
<li><a class="reference internal" href="#mmdetection-2-14-0">MMDetection 2.14.0</a><ul>
<li><a class="reference internal" href="#mmcv-version">MMCV Version</a></li>
<li><a class="reference internal" href="#ssd-compatibility">SSD compatibility</a></li>
</ul>
</li>
<li><a class="reference internal" href="#mmdetection-2-12-0">MMDetection 2.12.0</a><ul>
<li><a class="reference internal" href="#id1">MMCV Version</a></li>
<li><a class="reference internal" href="#unified-model-initialization">Unified model initialization</a></li>
<li><a class="reference internal" href="#unified-model-registry">Unified model registry</a></li>
<li><a class="reference internal" href="#mask-ap-evaluation">Mask AP evaluation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#compatibility-with-mmdetection-1-x">Compatibility with MMDetection 1.x</a><ul>
<li><a class="reference internal" href="#coordinate-system">Coordinate System</a></li>
<li><a class="reference internal" href="#codebase-conventions">Codebase Conventions</a></li>
<li><a class="reference internal" href="#training-hyperparameters">Training Hyperparameters</a></li>
<li><a class="reference internal" href="#upgrade-models-from-1-x-to-2-0">Upgrade Models from 1.x to 2.0</a></li>
</ul>
</li>
<li><a class="reference internal" href="#pycocotools-compatibility">pycocotools compatibility</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>