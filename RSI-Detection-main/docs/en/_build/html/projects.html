


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Projects based on MMDetection &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="Changelog" href="changelog.html" />
  <link rel="prev" title="Compatibility of MMDetection 2.x" href="compatibility.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>Projects based on MMDetection</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/projects.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="projects-based-on-mmdetection">
<h1>Projects based on MMDetection<a class="headerlink" href="#projects-based-on-mmdetection" title="Permalink to this heading">¶</a></h1>
<p>There are many projects built upon MMDetection.
We list some of them as examples of how to extend MMDetection for your own projects.
As the page might not be completed, please feel free to create a PR to update this page.</p>
<section id="projects-as-an-extension">
<h2>Projects as an extension<a class="headerlink" href="#projects-as-an-extension" title="Permalink to this heading">¶</a></h2>
<p>Some projects extend the boundary of MMDetection for deployment or other research fields.
They reveal the potential of what MMDetection can do. We list several of them as below.</p>
<ul class="simple">
<li><p><a class="reference external" href="https://github.com/opencv/rsidetection">OTEDetection</a>: OpenVINO training extensions for object detection.</p></li>
<li><p><a class="reference external" href="https://github.com/open-mmlab/rsidetection3d">MMDetection3d</a>: OpenMMLab’s next-generation platform for general 3D object detection.</p></li>
</ul>
</section>
<section id="projects-of-papers">
<h2>Projects of papers<a class="headerlink" href="#projects-of-papers" title="Permalink to this heading">¶</a></h2>
<p>There are also projects released with papers.
Some of the papers are published in top-tier conferences (CVPR, ICCV, and ECCV), the others are also highly influential.
To make this list also a reference for the community to develop and compare new object detection algorithms, we list them following the time order of top-tier conferences.
Methods already supported and maintained by MMDetection are not listed.</p>
<ul class="simple">
<li><p>Anchor Pruning for Object Detection, CVIU 2022. <a class="reference external" href="https://doi.org/10.1016/j.cviu.2022.103445">[paper]</a><a class="reference external" href="https://github.com/Mxbonn/anchor_pruning">[github]</a></p></li>
<li><p>Involution: Inverting the Inherence of Convolution for Visual Recognition, CVPR21. <a class="reference external" href="https://arxiv.org/abs/2103.06255">[paper]</a><a class="reference external" href="https://github.com/d-li14/involution">[github]</a></p></li>
<li><p>Multiple Instance Active Learning for Object Detection, CVPR 2021. <a class="reference external" href="https://openaccess.thecvf.com/content/CVPR2021/papers/Yuan_Multiple_Instance_Active_Learning_for_Object_Detection_CVPR_2021_paper.pdf">[paper]</a><a class="reference external" href="https://github.com/yuantn/MI-AOD">[github]</a></p></li>
<li><p>Adaptive Class Suppression Loss for Long-Tail Object Detection, CVPR 2021. <a class="reference external" href="https://arxiv.org/abs/2104.00885">[paper]</a><a class="reference external" href="https://github.com/CASIA-IVA-Lab/ACSL">[github]</a></p></li>
<li><p>Generalizable Pedestrian Detection: The Elephant In The Room, CVPR2021. <a class="reference external" href="https://arxiv.org/abs/2003.08799">[paper]</a><a class="reference external" href="https://github.com/hasanirtiza/Pedestron">[github]</a></p></li>
<li><p>Group Fisher Pruning for Practical Network Compression, ICML2021. <a class="reference external" href="https://github.com/jshilong/FisherPruning/blob/main/resources/paper.pdf">[paper]</a><a class="reference external" href="https://github.com/jshilong/FisherPruning">[github]</a></p></li>
<li><p>Overcoming Classifier Imbalance for Long-tail Object Detection with Balanced Group Softmax, CVPR2020. <a class="reference external" href="http://openaccess.thecvf.com/content_CVPR_2020/papers/Li_Overcoming_Classifier_Imbalance_for_Long-Tail_Object_Detection_With_Balanced_Group_CVPR_2020_paper.pdf">[paper]</a><a class="reference external" href="https://github.com/FishYuLi/BalancedGroupSoftmax">[github]</a></p></li>
<li><p>Coherent Reconstruction of Multiple Humans from a Single Image, CVPR2020. <a class="reference external" href="https://jiangwenpl.github.io/multiperson/">[paper]</a><a class="reference external" href="https://github.com/JiangWenPL/multiperson">[github]</a></p></li>
<li><p>Look-into-Object: Self-supervised Structure Modeling for Object Recognition, CVPR 2020. <a class="reference external" href="http://openaccess.thecvf.com/content_CVPR_2020/papers/Zhou_Look-Into-Object_Self-Supervised_Structure_Modeling_for_Object_Recognition_CVPR_2020_paper.pdf">[paper]</a><a class="reference external" href="https://github.com/JDAI-CV/LIO">[github]</a></p></li>
<li><p>Video Panoptic Segmentation, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2006.11339">[paper]</a><a class="reference external" href="https://github.com/mcahny/vps">[github]</a></p></li>
<li><p>D2Det: Towards High Quality Object Detection and Instance Segmentation, CVPR2020. <a class="reference external" href="http://openaccess.thecvf.com/content_CVPR_2020/html/Cao_D2Det_Towards_High_Quality_Object_Detection_and_Instance_Segmentation_CVPR_2020_paper.html">[paper]</a><a class="reference external" href="https://github.com/JialeCao001/D2Det">[github]</a></p></li>
<li><p>CentripetalNet: Pursuing High-quality Keypoint Pairs for Object Detection, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2003.09119">[paper]</a><a class="reference external" href="https://github.com/KiveeDong/CentripetalNet">[github]</a></p></li>
<li><p>Learning a Unified Sample Weighting Network for Object Detection, CVPR 2020. <a class="reference external" href="http://openaccess.thecvf.com/content_CVPR_2020/html/Cai_Learning_a_Unified_Sample_Weighting_Network_for_Object_Detection_CVPR_2020_paper.html">[paper]</a><a class="reference external" href="https://github.com/caiqi/sample-weighting-network">[github]</a></p></li>
<li><p>Scale-equalizing Pyramid Convolution for Object Detection, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2005.03101">[paper]</a> <a class="reference external" href="https://github.com/jshilong/SEPC">[github]</a></p></li>
<li><p>Revisiting the Sibling Head in Object Detector, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2003.07540">[paper]</a><a class="reference external" href="https://github.com/Sense-X/TSD">[github]</a></p></li>
<li><p>PolarMask: Single Shot Instance Segmentation with Polar Representation, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/1909.13226">[paper]</a><a class="reference external" href="https://github.com/xieenze/PolarMask">[github]</a></p></li>
<li><p>Hit-Detector: Hierarchical Trinity Architecture Search for Object Detection, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2003.11818">[paper]</a><a class="reference external" href="https://github.com/ggjy/HitDet.pytorch">[github]</a></p></li>
<li><p>ZeroQ: A Novel Zero Shot Quantization Framework, CVPR2020. <a class="reference external" href="https://arxiv.org/abs/2001.00281">[paper]</a><a class="reference external" href="https://github.com/amirgholami/ZeroQ">[github]</a></p></li>
<li><p>CBNet: A Novel Composite Backbone Network Architecture for Object Detection, AAAI2020. <a class="reference external" href="https://aaai.org/Papers/AAAI/2020GB/AAAI-LiuY.1833.pdf">[paper]</a><a class="reference external" href="https://github.com/VDIGPKU/CBNet">[github]</a></p></li>
<li><p>RDSNet: A New Deep Architecture for Reciprocal Object Detection and Instance Segmentation, AAAI2020. <a class="reference external" href="https://arxiv.org/abs/1912.05070">[paper]</a><a class="reference external" href="https://github.com/wangsr126/RDSNet">[github]</a></p></li>
<li><p>Training-Time-Friendly Network for Real-Time Object Detection, AAAI2020. <a class="reference external" href="https://arxiv.org/abs/1909.00700">[paper]</a><a class="reference external" href="https://github.com/ZJULearning/ttfnet">[github]</a></p></li>
<li><p>Cascade RPN: Delving into High-Quality Region Proposal Network with Adaptive Convolution, NeurIPS 2019. <a class="reference external" href="https://arxiv.org/abs/1909.06720">[paper]</a><a class="reference external" href="https://github.com/thangvubk/Cascade-RPN">[github]</a></p></li>
<li><p>Reasoning R-CNN: Unifying Adaptive Global Reasoning into Large-scale Object Detection, CVPR2019. <a class="reference external" href="http://openaccess.thecvf.com/content_CVPR_2019/papers/Xu_Reasoning-RCNN_Unifying_Adaptive_Global_Reasoning_Into_Large-Scale_Object_Detection_CVPR_2019_paper.pdf">[paper]</a><a class="reference external" href="https://github.com/chanyn/Reasoning-RCNN">[github]</a></p></li>
<li><p>Learning RoI Transformer for Oriented Object Detection in Aerial Images, CVPR2019. <a class="reference external" href="https://arxiv.org/abs/1812.00155">[paper]</a><a class="reference external" href="https://github.com/dingjiansw101/AerialDetection">[github]</a></p></li>
<li><p>SOLO: Segmenting Objects by Locations. <a class="reference external" href="https://arxiv.org/abs/1912.04488">[paper]</a><a class="reference external" href="https://github.com/WXinlong/SOLO">[github]</a></p></li>
<li><p>SOLOv2: Dynamic, Faster and Stronger. <a class="reference external" href="https://arxiv.org/abs/2003.10152">[paper]</a><a class="reference external" href="https://github.com/WXinlong/SOLO">[github]</a></p></li>
<li><p>Dense Peppoints: Representing Visual Objects with Dense Point Sets. <a class="reference external" href="https://arxiv.org/abs/1912.11473">[paper]</a><a class="reference external" href="https://github.com/justimyhxu/Dense-RepPoints">[github]</a></p></li>
<li><p>IterDet: Iterative Scheme for Object Detection in Crowded Environments. <a class="reference external" href="https://arxiv.org/abs/2005.05708">[paper]</a><a class="reference external" href="https://github.com/saic-vul/iterdet">[github]</a></p></li>
<li><p>Cross-Iteration Batch Normalization. <a class="reference external" href="https://arxiv.org/abs/2002.05712">[paper]</a><a class="reference external" href="https://github.com/Howal/Cross-iterationBatchNorm">[github]</a></p></li>
<li><p>A Ranking-based, Balanced Loss Function Unifying Classification and Localisation in Object Detection, NeurIPS2020 <a class="reference external" href="https://arxiv.org/abs/2009.13592">[paper]</a><a class="reference external" href="https://github.com/kemaloksuz/aLRPLoss">[github]</a></p></li>
<li><p>RelationNet++: Bridging Visual Representations for Object Detection via Transformer Decoder, NeurIPS2020 <a class="reference external" href="https://arxiv.org/abs/2010.15831">[paper]</a><a class="reference external" href="https://github.com/microsoft/RelationNet2">[github]</a></p></li>
<li><p>Generalized Focal Loss V2: Learning Reliable Localization Quality Estimation for Dense Object Detection, CVPR2021<a class="reference external" href="https://arxiv.org/abs/2011.12885">[paper]</a><a class="reference external" href="https://github.com/implus/GFocalV2">[github]</a></p></li>
<li><p>Swin Transformer: Hierarchical Vision Transformer using Shifted Windows, ICCV2021<a class="reference external" href="https://arxiv.org/abs/2103.14030">[paper]</a><a class="reference external" href="https://github.com/SwinTransformer/">[github]</a></p></li>
<li><p>Focal Transformer: Focal Self-attention for Local-Global Interactions in Vision Transformers, NeurIPS2021<a class="reference external" href="https://arxiv.org/abs/2107.00641">[paper]</a><a class="reference external" href="https://github.com/microsoft/Focal-Transformer">[github]</a></p></li>
<li><p>End-to-End Semi-Supervised Object Detection with Soft Teacher, ICCV2021<a class="reference external" href="https://arxiv.org/abs/2106.09018">[paper]</a><a class="reference external" href="https://github.com/microsoft/SoftTeacher">[github]</a></p></li>
<li><p>CBNetV2: A Novel Composite Backbone Network Architecture for Object Detection <a class="reference external" href="http://arxiv.org/abs/2107.00420">[paper]</a><a class="reference external" href="https://github.com/VDIGPKU/CBNetV2">[github]</a></p></li>
<li><p>Instances as Queries, ICCV2021 <a class="reference external" href="https://openaccess.thecvf.com/content/ICCV2021/papers/Fang_Instances_As_Queries_ICCV_2021_paper.pdf">[paper]</a><a class="reference external" href="https://github.com/hustvl/QueryInst">[github]</a></p></li>
</ul>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="changelog.html" class="btn btn-neutral float-right" title="Changelog" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="compatibility.html" class="btn btn-neutral" title="Compatibility of MMDetection 2.x" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Projects based on MMDetection</a><ul>
<li><a class="reference internal" href="#projects-as-an-extension">Projects as an extension</a></li>
<li><a class="reference internal" href="#projects-of-papers">Projects of papers</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>