


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Welcome to RSI-Detection’s documentation! &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="Prerequisites" href="get_started.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Dataset Preparation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="dataset_prepare.html">Prepare datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Model Zoo</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="#">
            Docs
        </a> &gt;
      </li>

        
      <li>Welcome to RSI-Detection’s documentation!</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/index.rst.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="welcome-to-rsi-detection-s-documentation">
<h1>Welcome to RSI-Detection’s documentation!<a class="headerlink" href="#welcome-to-rsi-detection-s-documentation" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="get_started.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="get_started.html#verify-the-installation">Verify the installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="get_started.html#customize-installation">Customize Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="get_started.html#trouble-shooting">Trouble shooting</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Dataset Preparation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="dataset_prepare.html">Prepare datasets</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Model Zoo</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a><ul>
<li class="toctree-l2"><a class="reference internal" href="1_exist_data_model.html#inference-with-existing-models">Inference with existing models</a></li>
<li class="toctree-l2"><a class="reference internal" href="1_exist_data_model.html#test-existing-models-on-standard-datasets">Test existing models on standard datasets</a></li>
<li class="toctree-l2"><a class="reference internal" href="1_exist_data_model.html#train-predefined-models-on-standard-datasets">Train predefined models on standard datasets</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a><ul>
<li class="toctree-l2"><a class="reference internal" href="2_new_data_model.html#prepare-the-customized-dataset">Prepare the customized dataset</a></li>
<li class="toctree-l2"><a class="reference internal" href="2_new_data_model.html#prepare-a-config">Prepare a config</a></li>
<li class="toctree-l2"><a class="reference internal" href="2_new_data_model.html#train-a-new-model">Train a new model</a></li>
<li class="toctree-l2"><a class="reference internal" href="2_new_data_model.html#test-and-inference">Test and inference</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a><ul>
<li class="toctree-l2"><a class="reference internal" href="3_exist_data_new_model.html#prepare-the-standard-dataset">Prepare the standard dataset</a></li>
<li class="toctree-l2"><a class="reference internal" href="3_exist_data_new_model.html#prepare-your-own-customized-model">Prepare your own customized model</a></li>
<li class="toctree-l2"><a class="reference internal" href="3_exist_data_new_model.html#prepare-a-config">Prepare a config</a></li>
<li class="toctree-l2"><a class="reference internal" href="3_exist_data_new_model.html#train-a-new-model">Train a new model</a></li>
<li class="toctree-l2"><a class="reference internal" href="3_exist_data_new_model.html#test-and-inference">Test and inference</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#modify-config-through-script-arguments">Modify config through script arguments</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#config-file-structure">Config File Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#config-name-style">Config Name Style</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#deprecated-train-cfg-test-cfg">Deprecated train_cfg/test_cfg</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#an-example-of-mask-r-cnn">An Example of Mask R-CNN</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/config.html#faq">FAQ</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_dataset.html#support-new-data-format">Support new data format</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_dataset.html#customize-datasets-by-dataset-wrappers">Customize datasets by dataset wrappers</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_dataset.html#modify-dataset-classes">Modify Dataset Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_dataset.html#coco-panoptic-dataset">COCO Panoptic Dataset</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/data_pipeline.html#design-of-data-pipelines">Design of Data pipelines</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/data_pipeline.html#extend-and-use-custom-pipelines">Extend and use custom pipelines</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_models.html#develop-new-components">Develop new components</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_runtime.html#customize-optimization-settings">Customize optimization settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_runtime.html#customize-training-schedules">Customize training schedules</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_runtime.html#customize-workflow">Customize workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_runtime.html#customize-hooks">Customize hooks</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_losses.html#computation-pipeline-of-a-loss">Computation pipeline of a loss</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_losses.html#set-sampling-method-step-1">Set sampling method (step 1)</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_losses.html#tweaking-loss">Tweaking loss</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/customize_losses.html#weighting-loss-step-3">Weighting loss (step 3)</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/finetune.html#inherit-base-configs">Inherit base configs</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/finetune.html#modify-head">Modify head</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/finetune.html#modify-dataset">Modify dataset</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/finetune.html#modify-training-schedule">Modify training schedule</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/finetune.html#use-pre-trained-model">Use pre-trained model</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#try-the-new-mmdeploy-to-deploy-your-model">Try the new MMDeploy to deploy your model</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#how-to-convert-models-from-pytorch-to-onnx">How to convert models from Pytorch to ONNX</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#how-to-evaluate-the-exported-models">How to evaluate the exported models</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#list-of-supported-models-exportable-to-onnx">List of supported models exportable to ONNX</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#the-parameters-of-non-maximum-suppression-in-onnx-export">The Parameters of Non-Maximum Suppression in ONNX Export</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#reminders">Reminders</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/pytorch2onnx.html#faqs">FAQs</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#try-the-new-mmdeploy-to-deploy-your-model">Try the new MMDeploy to deploy your model</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#how-to-convert-models-from-onnx-to-tensorrt">How to convert models from ONNX to TensorRT</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#how-to-evaluate-the-exported-models">How to evaluate the exported models</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#list-of-supported-models-convertible-to-tensorrt">List of supported models convertible to TensorRT</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#reminders">Reminders</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/onnx2tensorrt.html#faqs">FAQs</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/init_cfg.html#description">Description</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/init_cfg.html#initialize-parameters">Initialize parameters</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/init_cfg.html#usage-of-init-cfg">Usage of init_cfg</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/how_to.html#use-backbone-network-through-mmclassification">Use backbone network through MMClassification</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/how_to.html#use-mosaic-augmentation">Use Mosaic augmentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/how_to.html#unfreeze-backbone-network-after-freezing-the-backbone-in-the-config">Unfreeze backbone network after freezing the backbone in the config</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/how_to.html#get-the-channels-of-a-new-backbone">Get the channels of a new backbone</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/test_results_submission.html#panoptic-segmentation-test-results-submission">Panoptic segmentation test results submission</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#checkinvalidlosshook">CheckInvalidLossHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#evalhook-and-distevalhook">EvalHook and DistEvalHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#expmomentumemahook-and-linearmomentumemahook">ExpMomentumEMAHook and LinearMomentumEMAHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#numclasscheckhook">NumClassCheckHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#memoryprofilerhook">MemoryProfilerHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#setepochinfohook">SetEpochInfoHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#syncnormhook">SyncNormHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#syncrandomsizehook">SyncRandomSizeHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#yoloxlrupdaterhook">YOLOXLrUpdaterHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#yoloxmodeswitchhook">YOLOXModeSwitchHook</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorials/useful_hooks.html#how-to-implement-a-custom-hook">How to implement a custom hook</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#visualize-datasets">Visualize Datasets</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#visualize-models">Visualize Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#visualize-predictions">Visualize Predictions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#convert-model-from-mmdetection-to-torchserve">1. Convert model from MMDetection to TorchServe</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#build-rsidet-serve-docker-image">2. Build <code class="docutils literal notranslate"><span class="pre">rsidet-serve</span></code> docker image</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#run-rsidet-serve">3. Run <code class="docutils literal notranslate"><span class="pre">rsidet-serve</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#test-deployment">4. Test deployment</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#mmdetection-model-to-onnx-experimental">MMDetection model to ONNX (experimental)</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#mmdetection-1-x-model-to-mmdetection-2-x">MMDetection 1.x model to MMDetection 2.x</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#regnet-model-to-mmdetection">RegNet model to MMDetection</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#detectron-resnet-to-pytorch">Detectron ResNet to Pytorch</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#prepare-a-model-for-publishing">Prepare a model for publishing</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#robust-detection-benchmark">Robust Detection Benchmark</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#fps-benchmark">FPS Benchmark</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#evaluating-a-metric">Evaluating a metric</a></li>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#print-the-entire-config">Print the entire config</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a><ul>
<li class="toctree-l2"><a class="reference internal" href="useful_tools.html#yolo-anchor-optimization">YOLO Anchor Optimization</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="conventions.html#loss">Loss</a></li>
<li class="toctree-l2"><a class="reference internal" href="conventions.html#empty-proposals">Empty Proposals</a></li>
<li class="toctree-l2"><a class="reference internal" href="conventions.html#coco-panoptic-dataset">Coco Panoptic Dataset</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a><ul>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-25-0">MMDetection 2.25.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-21-0">MMDetection 2.21.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-18-1">MMDetection 2.18.1</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-18-0">MMDetection 2.18.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-14-0">MMDetection 2.14.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#mmdetection-2-12-0">MMDetection 2.12.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#compatibility-with-mmdetection-1-x">Compatibility with MMDetection 1.x</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility.html#pycocotools-compatibility">pycocotools compatibility</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a><ul>
<li class="toctree-l2"><a class="reference internal" href="projects.html#projects-as-an-extension">Projects as an extension</a></li>
<li class="toctree-l2"><a class="reference internal" href="projects.html#projects-of-papers">Projects of papers</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a><ul>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-25-1-29-7-2022">v2.25.1 (29/7/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-25-0-31-5-2022">v2.25.0 (31/5/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-24-0-26-4-2022">v2.24.0 (26/4/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-23-0-28-3-2022">v2.23.0 (28/3/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-22-0-24-2-2022">v2.22.0 (24/2/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-21-0-8-2-2022">v2.21.0 (8/2/2022)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#id19">Breaking Changes</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-20-0-27-12-2021">v2.20.0 (27/12/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-19-1-14-12-2021">v2.19.1 (14/12/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-19-0-29-11-2021">v2.19.0 (29/11/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-18-1-15-11-2021">v2.18.1 (15/11/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-18-0-27-10-2021">v2.18.0 (27/10/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-17-0-28-9-2021">v2.17.0 (28/9/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-16-0-30-8-2021">v2.16.0 (30/8/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-15-1-11-8-2021">v2.15.1 (11/8/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-15-0-02-8-2021">v2.15.0 (02/8/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-14-0-29-6-2021">v2.14.0 (29/6/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-13-0-01-6-2021">v2.13.0 (01/6/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-12-0-01-5-2021">v2.12.0 (01/5/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-11-0-01-4-2021">v2.11.0 (01/4/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-10-0-01-03-2021">v2.10.0 (01/03/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-9-0-01-02-2021">v2.9.0 (01/02/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-8-0-04-01-2021">v2.8.0 (04/01/2021)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-7-0-30-11-2020">v2.7.0 (30/11/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-6-0-1-11-2020">v2.6.0 (1/11/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-5-0-5-10-2020">v2.5.0 (5/10/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-4-0-5-9-2020">v2.4.0 (5/9/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-3-0-5-8-2020">v2.3.0 (5/8/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-2-0-1-7-2020">v2.2.0 (1/7/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-1-0-8-6-2020">v2.1.0 (8/6/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v2-0-0-6-5-2020">v2.0.0 (6/5/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v1-1-0-24-2-2020">v1.1.0 (24/2/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v1-0-0-30-1-2020">v1.0.0 (30/1/2020)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v1-0rc1-13-12-2019">v1.0rc1 (13/12/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v1-0rc0-27-07-2019">v1.0rc0 (27/07/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-6-0-14-04-2019">v0.6.0 (14/04/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-6rc0-06-02-2019">v0.6rc0(06/02/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-7-06-02-2019">v0.5.7 (06/02/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-6-17-01-2019">v0.5.6 (17/01/2019)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-5-22-12-2018">v0.5.5 (22/12/2018)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-4-27-11-2018">v0.5.4 (27/11/2018)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-3-26-11-2018">v0.5.3 (26/11/2018)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-2-21-10-2018">v0.5.2 (21/10/2018)</a></li>
<li class="toctree-l2"><a class="reference internal" href="changelog.html#v0-5-1-20-10-2018">v0.5.1 (20/10/2018)</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="faq.html#installation">Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#coding">Coding</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#pytorch-cuda-environment">PyTorch/CUDA Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#training">Training</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#evaluation">Evaluation</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#model">Model</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Permalink to this heading">¶</a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="get_started.html" class="btn btn-neutral float-right" title="Prerequisites" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Welcome to RSI-Detection’s documentation!</a><ul>
</ul>
</li>
<li><a class="reference internal" href="#indices-and-tables">Indices and tables</a></li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>