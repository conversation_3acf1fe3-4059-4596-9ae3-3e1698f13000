


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>rsidet.apis &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Dataset Preparation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="dataset_prepare.html">Prepare datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Model Zoo</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>rsidet.apis</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/api.rst.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="rsidet-apis">
<h1>rsidet.apis<a class="headerlink" href="#rsidet-apis" title="Permalink to this heading">¶</a></h1>
</section>
<section id="rsidet-core">
<h1>rsidet.core<a class="headerlink" href="#rsidet-core" title="Permalink to this heading">¶</a></h1>
<section id="anchor">
<h2>anchor<a class="headerlink" href="#anchor" title="Permalink to this heading">¶</a></h2>
</section>
<section id="bbox">
<h2>bbox<a class="headerlink" href="#bbox" title="Permalink to this heading">¶</a></h2>
</section>
<section id="export">
<h2>export<a class="headerlink" href="#export" title="Permalink to this heading">¶</a></h2>
</section>
<section id="mask">
<h2>mask<a class="headerlink" href="#mask" title="Permalink to this heading">¶</a></h2>
</section>
<section id="evaluation">
<h2>evaluation<a class="headerlink" href="#evaluation" title="Permalink to this heading">¶</a></h2>
</section>
<section id="post-processing">
<h2>post_processing<a class="headerlink" href="#post-processing" title="Permalink to this heading">¶</a></h2>
</section>
<section id="utils">
<h2>utils<a class="headerlink" href="#utils" title="Permalink to this heading">¶</a></h2>
</section>
</section>
<section id="rsidet-datasets">
<h1>rsidet.datasets<a class="headerlink" href="#rsidet-datasets" title="Permalink to this heading">¶</a></h1>
<section id="datasets">
<h2>datasets<a class="headerlink" href="#datasets" title="Permalink to this heading">¶</a></h2>
</section>
<section id="pipelines">
<h2>pipelines<a class="headerlink" href="#pipelines" title="Permalink to this heading">¶</a></h2>
</section>
<section id="samplers">
<h2>samplers<a class="headerlink" href="#samplers" title="Permalink to this heading">¶</a></h2>
</section>
<section id="api-wrappers">
<h2>api_wrappers<a class="headerlink" href="#api-wrappers" title="Permalink to this heading">¶</a></h2>
</section>
</section>
<section id="rsidet-models">
<h1>rsidet.models<a class="headerlink" href="#rsidet-models" title="Permalink to this heading">¶</a></h1>
<section id="detectors">
<h2>detectors<a class="headerlink" href="#detectors" title="Permalink to this heading">¶</a></h2>
</section>
<section id="backbones">
<h2>backbones<a class="headerlink" href="#backbones" title="Permalink to this heading">¶</a></h2>
</section>
<section id="necks">
<h2>necks<a class="headerlink" href="#necks" title="Permalink to this heading">¶</a></h2>
</section>
<section id="dense-heads">
<h2>dense_heads<a class="headerlink" href="#dense-heads" title="Permalink to this heading">¶</a></h2>
</section>
<section id="roi-heads">
<h2>roi_heads<a class="headerlink" href="#roi-heads" title="Permalink to this heading">¶</a></h2>
</section>
<section id="losses">
<h2>losses<a class="headerlink" href="#losses" title="Permalink to this heading">¶</a></h2>
</section>
<section id="id1">
<h2>utils<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h2>
</section>
</section>
<section id="rsidet-utils">
<h1>rsidet.utils<a class="headerlink" href="#rsidet-utils" title="Permalink to this heading">¶</a></h1>
</section>


              </article>
              
            </div>
            <footer>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">rsidet.apis</a></li>
<li><a class="reference internal" href="#rsidet-core">rsidet.core</a><ul>
<li><a class="reference internal" href="#anchor">anchor</a></li>
<li><a class="reference internal" href="#bbox">bbox</a></li>
<li><a class="reference internal" href="#export">export</a></li>
<li><a class="reference internal" href="#mask">mask</a></li>
<li><a class="reference internal" href="#evaluation">evaluation</a></li>
<li><a class="reference internal" href="#post-processing">post_processing</a></li>
<li><a class="reference internal" href="#utils">utils</a></li>
</ul>
</li>
<li><a class="reference internal" href="#rsidet-datasets">rsidet.datasets</a><ul>
<li><a class="reference internal" href="#datasets">datasets</a></li>
<li><a class="reference internal" href="#pipelines">pipelines</a></li>
<li><a class="reference internal" href="#samplers">samplers</a></li>
<li><a class="reference internal" href="#api-wrappers">api_wrappers</a></li>
</ul>
</li>
<li><a class="reference internal" href="#rsidet-models">rsidet.models</a><ul>
<li><a class="reference internal" href="#detectors">detectors</a></li>
<li><a class="reference internal" href="#backbones">backbones</a></li>
<li><a class="reference internal" href="#necks">necks</a></li>
<li><a class="reference internal" href="#dense-heads">dense_heads</a></li>
<li><a class="reference internal" href="#roi-heads">roi_heads</a></li>
<li><a class="reference internal" href="#losses">losses</a></li>
<li><a class="reference internal" href="#id1">utils</a></li>
</ul>
</li>
<li><a class="reference internal" href="#rsidet-utils">rsidet.utils</a></li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>