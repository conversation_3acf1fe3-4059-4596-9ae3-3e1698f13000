Search.setIndex({"docnames": ["1_exist_data_model", "2_new_data_model", "3_exist_data_new_model", "api", "changelog", "compatibility", "conventions", "dataset_prepare", "faq", "get_started", "index", "model_zoo", "projects", "robustness_benchmarking", "switch_language", "tutorials/config", "tutorials/customize_dataset", "tutorials/customize_losses", "tutorials/customize_models", "tutorials/customize_runtime", "tutorials/data_pipeline", "tutorials/finetune", "tutorials/how_to", "tutorials/index", "tutorials/init_cfg", "tutorials/onnx2tensorrt", "tutorials/pytorch2onnx", "tutorials/test_results_submission", "tutorials/useful_hooks", "useful_tools"], "filenames": ["1_exist_data_model.md", "2_new_data_model.md", "3_exist_data_new_model.md", "api.rst", "changelog.md", "compatibility.md", "conventions.md", "dataset_prepare.md", "faq.md", "get_started.md", "index.rst", "model_zoo.md", "projects.md", "robustness_benchmarking.md", "switch_language.md", "tutorials/config.md", "tutorials/customize_dataset.md", "tutorials/customize_losses.md", "tutorials/customize_models.md", "tutorials/customize_runtime.md", "tutorials/data_pipeline.md", "tutorials/finetune.md", "tutorials/how_to.md", "tutorials/index.rst", "tutorials/init_cfg.md", "tutorials/onnx2tensorrt.md", "tutorials/pytorch2onnx.md", "tutorials/test_results_submission.md", "tutorials/useful_hooks.md", "useful_tools.md"], "titles": ["1: Inference and train with existing models and standard datasets", "2: Train with customized datasets", "3: Train with customized models and standard datasets", "rsidet.apis", "Changelog", "Compatibility of MMDetection 2.x", "Conventions", "Prepare datasets", "Frequently Asked Questions", "Prerequisites", "Welcome to RSI-Detection\u2019s documentation!", "Benchmark and Model Zoo", "Projects based on MMDetection", "Corruption Benchmarking", "<a href='https://rsidetection.readthedocs.io/en/latest/'>English</a>", "Tutorial 1: Learn about Configs", "Tutorial 2: Customize Datasets", "Tutorial 6: Customize Losses", "Tutorial 4: Customize Models", "Tutorial 5: Customize Runtime Settings", "Tutorial 3: Customize Data Pipelines", "Tutorial 7: Finetuning Models", "Tutorial 11: How to xxx", "&lt;no title&gt;", "Tutorial 10: Weight initialization", "Tutorial 9: ONNX to TensorRT (Experimental)", "Tutorial 8: Pytorch to ONNX (Experimental)", "Tutorial 12: Test Results Submission", "Tutorial 13: Useful Hooks", "Log Analysis"], "terms": {"mmdetect": [0, 1, 2, 4, 6, 8, 10, 11, 15, 16, 17, 18, 19, 21, 22, 24, 25, 26, 28], "provid": [0, 4, 5, 7, 8, 9, 11, 13, 16, 17, 21, 22, 24, 25, 28, 29], "hundr": 0, "detect": [0, 4, 8, 9, 12, 13, 15, 18, 19, 20, 26], "zoo": [0, 4, 5, 21, 29], "includ": [0, 4, 5, 11, 13, 16, 18, 25, 26, 28, 29], "pascal": [0, 4, 11, 16, 26, 29], "voc": [0, 4, 11, 16, 21, 26, 29], "coco": [0, 2, 4, 5, 8, 10, 13, 15, 21, 22, 23, 26, 29], "cityscap": [0, 2, 4, 11, 15, 16, 21, 29], "lvi": [0, 4, 8, 29], "etc": [0, 2, 4, 5, 8, 9, 11, 15, 20, 28], "thi": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 19, 21, 22, 25, 26, 27, 28, 29], "note": [0, 1, 2, 4, 5, 8, 11, 15, 16, 19, 24, 25, 26, 27, 29], "show": [0, 4, 11, 16, 18, 24, 25, 26, 29], "how": [0, 1, 2, 4, 7, 8, 9, 10, 12, 13, 17, 18, 19, 20, 23, 27, 29], "perform": [0, 1, 2, 4, 5, 8, 13, 15, 19, 21, 24, 26, 27], "common": [0, 4, 8, 13, 15, 16, 19], "task": [0, 1, 4, 13, 17, 18, 29], "us": [0, 1, 2, 4, 5, 6, 7, 8, 11, 12, 13, 17, 23, 24, 25, 26, 27, 29], "given": [0, 4, 15, 17, 29], "By": [0, 4, 6, 7, 19, 21, 25, 26], "we": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22, 24, 25, 26, 27, 29], "mean": [0, 2, 4, 5, 7, 8, 9, 11, 15, 16, 17, 18, 19, 20, 22, 26, 29], "object": [0, 4, 5, 8, 12, 13, 18, 19, 20, 29], "In": [0, 1, 2, 4, 5, 6, 7, 8, 9, 12, 15, 16, 18, 19, 26, 27, 28, 29], "i": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 13, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29], "defin": [0, 4, 13, 15, 16, 20, 24], "configur": [0, 4, 8, 17, 24], "file": [0, 1, 4, 5, 7, 8, 9, 10, 20, 23, 24, 25, 26, 28, 29], "paramet": [0, 4, 5, 8, 10, 15, 19, 22, 23], "ar": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 24, 25, 26, 27, 28, 29], "save": [0, 4, 5, 8, 15, 16, 19, 20, 26, 29], "checkpoint": [0, 1, 4, 9, 11, 15, 22, 24, 25, 26, 27, 29], "To": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 28], "start": [0, 4, 6, 8, 15, 19], "recommend": [0, 1, 2, 4, 5, 7, 8, 9, 15, 16, 26, 29], "faster": [0, 4, 5, 12, 13, 15, 19, 20, 22, 25, 26, 29], "rcnn": [0, 1, 4, 15, 18, 21], "It": [0, 2, 4, 5, 7, 8, 9, 11, 15, 16, 20, 21, 24, 25, 26, 29], "download": [0, 1, 2, 4, 7, 9, 10, 11, 21, 22, 27], "directori": [0, 1, 2, 4, 7, 9, 15, 19, 20, 21, 26, 27, 29], "here": [0, 1, 2, 4, 5, 6, 7, 8, 15, 16, 17, 18, 19, 20, 24, 25, 29], "an": [0, 1, 2, 4, 5, 7, 8, 9, 10, 17, 18, 19, 21, 22, 23, 24, 25, 26, 29], "build": [0, 4, 8, 9, 10, 15, 21, 22, 24, 25, 26], "from": [0, 2, 4, 6, 7, 8, 9, 10, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 28], "rsidet": [0, 2, 4, 6, 8, 9, 10, 15, 16, 18, 19, 20, 22, 24, 28], "import": [0, 1, 4, 8, 9, 16, 19, 20, 22, 24, 28, 29], "init_detector": [0, 9], "inference_detector": [0, 4, 9], "mmcv": [0, 1, 4, 8, 15, 16, 20, 22, 24, 25, 26, 28, 29], "specifi": [0, 4, 9, 15, 16, 17, 20, 25, 26, 29], "path": [0, 1, 2, 4, 5, 7, 15, 16, 20, 25, 26, 29], "config": [0, 4, 5, 7, 8, 9, 10, 11, 17, 20, 23, 24, 25, 26, 27, 28], "config_fil": [0, 9, 13, 26, 27, 29], "faster_rcnn": [0, 15, 18, 22, 25, 26, 29], "faster_rcnn_r50_fpn_1x_coco": [0, 15, 18, 22, 25, 26, 29], "py": [0, 1, 2, 4, 5, 7, 8, 9, 11, 13, 15, 16, 18, 19, 20, 21, 22, 25, 26, 27, 29], "checkpoint_fil": [0, 9, 13, 26, 27, 29], "faster_rcnn_r50_fpn_1x_coco_20200130": [0, 29], "047c8118": [0, 29], "pth": [0, 1, 2, 8, 9, 21, 22, 26, 27, 29], "devic": [0, 4, 8, 9, 29], "cuda": [0, 4, 5, 10, 11, 25, 26, 29], "0": [0, 1, 2, 6, 7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 29], "result": [0, 4, 5, 6, 8, 9, 10, 11, 17, 20, 23, 25], "img": [0, 2, 4, 11, 15, 20, 22, 25, 26, 29], "jpg": [0, 1, 9, 16, 25, 26, 29], "imread": [0, 1], "which": [0, 1, 2, 4, 5, 7, 8, 9, 11, 13, 15, 16, 17, 19, 24, 26, 28, 29], "onli": [0, 1, 4, 6, 7, 11, 15, 16, 19, 21, 22, 24, 26, 27, 29], "load": [0, 1, 2, 4, 5, 11, 15, 16, 18, 22, 29], "onc": [0, 15], "visual": [0, 4, 10, 12, 13, 16, 20], "new": [0, 5, 8, 9, 10, 12, 15, 17, 20, 21, 23, 24, 28, 29], "window": [0, 4, 9, 12], "show_result": 0, "out_fil": [0, 1, 4, 29], "videoread": 0, "mp4": 0, "frame": [0, 4], "wait_tim": [0, 29], "A": [0, 4, 8, 12, 16, 19, 20, 29], "notebook": [0, 4], "can": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 24, 26, 28, 29], "found": [0, 4, 5, 7, 8, 9, 11, 19], "inference_demo": 0, "ipynb": [0, 4], "now": [0, 1, 4, 5, 15, 16, 19, 26, 29], "For": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 13, 15, 16, 17, 19, 20, 21, 22, 25, 26, 28, 29], "also": [0, 1, 2, 4, 5, 7, 8, 11, 12, 13, 15, 16, 19, 20, 21, 22, 24, 26, 29], "async": [0, 4], "util": [0, 4, 8, 18, 19], "stream": 0, "allow": [0, 4, 5, 8, 15, 16, 22], "block": [0, 4, 8, 15, 20, 29], "bound": [0, 4, 5, 6, 8, 9, 15, 16, 18, 20, 26, 29], "code": [0, 1, 2, 4, 5, 9, 10, 16, 17, 18, 19, 24, 27, 28], "enabl": [0, 4, 18, 19], "better": [0, 4, 5, 9, 11, 21], "thread": 0, "applic": [0, 13, 16, 17], "done": [0, 9, 29], "concurr": 0, "either": [0, 2, 8, 16, 18], "between": [0, 4, 5, 8, 15, 17, 18, 19, 25, 26, 29], "differ": [0, 2, 4, 5, 6, 7, 8, 11, 13, 15, 16, 17, 19, 20, 24, 28, 29], "input": [0, 2, 4, 8, 15, 17, 18, 20, 22, 25, 26, 29], "data": [0, 1, 2, 4, 6, 7, 8, 9, 10, 11, 13, 15, 22, 23, 26, 27, 29], "sampl": [0, 4, 5, 8, 9, 10, 12, 15, 23], "some": [0, 4, 5, 8, 9, 11, 12, 16, 17, 19, 22, 24, 26, 29], "pipelin": [0, 2, 4, 6, 10, 15, 16, 22, 23], "see": [0, 4, 8, 9, 15, 19, 20, 28], "async_benchmark": [0, 4], "compar": [0, 4, 5, 11, 12, 13, 29], "speed": [0, 4, 5, 8, 24, 29], "synchron": [0, 4, 15], "asyncio": 0, "torch": [0, 4, 8, 11, 18, 19, 22, 24, 26, 28, 29], "async_inference_detector": 0, "contextmanag": 0, "def": [0, 1, 2, 4, 6, 8, 16, 17, 18, 19, 20, 22, 24, 28], "main": [0, 19, 26], "queue": 0, "streamqueu": 0, "size": [0, 1, 4, 5, 8, 9, 13, 15, 18, 20, 21, 22, 25, 26], "streamqueue_s": 0, "_": [0, 1, 5, 6, 15], "rang": [0, 4, 5, 6, 22], "put_nowait": 0, "await": 0, "run": [0, 1, 2, 4, 5, 7, 8, 9, 11, 15, 19, 26, 27], "three": [0, 1, 5, 16, 18, 28], "script": [0, 4, 5, 7, 8, 11, 13, 16, 20, 22, 23, 29], "implement": [0, 1, 2, 4, 5, 6, 10, 11, 15, 16, 18, 23, 26], "function": [0, 1, 4, 5, 8, 9, 12, 13, 15, 16, 17, 18, 19, 22, 26, 28], "sourc": [0, 9, 11, 16, 25, 26, 29], "avail": [0, 4, 5, 8], "image_demo": [0, 4, 9], "image_fil": [0, 29], "gpu_id": [0, 4], "score": [0, 4, 26, 29], "thr": [0, 26, 29], "score_thr": [0, 15, 29], "live": 0, "webcam_demo": 0, "camera": 0, "id": [0, 1, 4, 11, 16, 29], "video_demo": 0, "video_fil": 0, "out": [0, 2, 4, 8, 9, 13, 15, 16, 26, 29], "wait": [0, 4, 29], "time": [0, 2, 4, 6, 11, 12, 16, 21, 22, 26, 29], "video_gpuaccel_demo": 0, "nvdecod": 0, "evalu": [0, 1, 4, 6, 10, 13, 15, 16, 23, 27, 28], "": [0, 1, 2, 4, 5, 9, 11, 12, 13, 15, 16, 18, 19, 24, 25, 27, 29], "accuraci": [0, 4, 6, 8, 19], "one": [0, 4, 8, 15, 16, 17, 18, 22], "usual": [0, 1, 5, 7, 8, 9, 11, 15, 18, 19, 20, 21, 24], "public": 0, "more": [0, 1, 2, 4, 5, 7, 8, 9, 15, 16, 17, 19, 20, 24, 25, 26, 28, 29], "section": [0, 9, 27], "like": [0, 4, 6, 7, 8, 15, 16, 19, 24, 27, 29], "mirror": 0, "offici": [0, 4, 5, 7, 8, 9, 11, 29], "websit": [0, 9, 27], "2012": [0, 7], "extens": [0, 4, 8, 9, 10], "2007": 0, "overlap": [0, 8], "them": [0, 7, 8, 12, 15, 16, 17, 18, 26, 27, 28, 29], "togeth": [0, 7], "extract": [0, 15, 18], "somewher": 0, "outsid": 0, "project": [0, 4, 5, 6, 9, 10, 29], "symlink": [0, 2, 7], "root": [0, 2, 4, 7, 15, 19, 22], "below": [0, 1, 2, 4, 8, 11, 12, 13, 15, 16, 19, 22, 25, 26, 27, 28], "If": [0, 1, 2, 4, 5, 6, 7, 8, 9, 15, 16, 19, 22, 24, 25, 26, 29], "your": [0, 6, 7, 8, 9, 10, 12, 15, 16, 19, 20, 23, 27], "folder": [0, 2, 4, 7, 8, 9, 15, 29], "structur": [0, 2, 4, 7, 8, 10, 12, 19, 21, 23, 27], "you": [0, 1, 2, 4, 6, 7, 8, 9, 11, 13, 15, 16, 17, 18, 19, 22, 25, 26, 27, 28, 29], "mai": [0, 2, 4, 5, 7, 8, 9, 13, 15, 16, 17, 19, 20, 21, 25, 26, 29], "need": [0, 1, 2, 4, 5, 6, 7, 8, 9, 15, 16, 17, 18, 19, 21, 24, 26, 27, 29], "chang": [0, 1, 2, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 21, 25, 26, 29], "correspond": [0, 2, 4, 5, 7, 8, 11, 13, 15, 16, 17, 18, 20], "tool": [0, 1, 2, 4, 5, 7, 8, 11, 13, 15, 20, 25, 26, 27, 29], "misc": [0, 5, 15, 20, 27, 29], "download_dataset": [0, 29], "name": [0, 1, 2, 4, 5, 8, 9, 10, 16, 19, 23, 24, 27, 29], "coco2017": [0, 29], "usag": [0, 1, 2, 4, 5, 10, 15, 19, 23, 29], "pleas": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15, 16, 19, 22, 25, 26, 28, 29], "refer": [0, 1, 2, 4, 5, 6, 7, 8, 11, 12, 13, 15, 16, 19, 20, 22, 24, 25, 26, 29], "rsidetect": [0, 2, 4, 8, 9, 11, 15, 21, 22], "train2017": [0, 2, 7, 15, 22], "val2017": [0, 2, 7, 15], "test2017": [0, 2, 27], "leftimg8bit": [0, 2, 7], "val": [0, 1, 2, 4, 7, 15, 16, 19, 24, 27], "gtfine": [0, 2, 7], "vocdevkit": [0, 2, 7], "voc2007": [0, 2, 29], "voc2012": [0, 2, 4, 7], "requir": [0, 4, 5, 7, 8, 9, 15, 21, 25, 26], "addit": [0, 4, 16], "stuff": [0, 4], "htc": [0, 4, 5, 15], "detector": [0, 1, 2, 4, 5, 9, 12, 15, 16, 21, 29], "scnet": [0, 4], "unzip": [0, 7, 27], "move": [0, 4], "The": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29], "should": [0, 1, 4, 5, 7, 8, 9, 11, 15, 16, 18, 19, 22, 26, 27, 28, 29], "stuffthingmap": 0, "panopt": [0, 4, 5, 8, 10, 12, 23], "segment": [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 15, 16, 23, 29], "panopticfpn": [0, 4], "panoptic_train2017": [0, 27], "json": [0, 1, 7, 15, 16, 22, 27, 29], "panoptic_val2017": [0, 27], "convert": [0, 1, 2, 4, 5, 7, 8, 10, 11, 15, 16, 22, 23], "format": [0, 2, 4, 6, 7, 8, 10, 15, 23, 26, 27, 29], "dataset_convert": [0, 2, 29], "pip": [0, 2, 4, 8, 9, 13, 26, 28, 29], "instal": [0, 2, 4, 5, 7, 10, 11, 13, 22, 25, 26, 28, 29], "cityscapesscript": [0, 2, 7], "nproc": [0, 2, 4, 7, 29], "8": [0, 2, 5, 7, 8, 9, 10, 11, 13, 15, 16, 21, 22, 23, 27, 28, 29], "dir": [0, 2, 4, 26, 29], "todo": 0, "TO": [0, 15], "THE": 0, "whole": [0, 1, 2, 6, 9, 16, 21, 24, 29], "follow": [0, 2, 5, 6, 7, 8, 9, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29], "environ": [0, 4, 5, 9, 10, 12, 26], "node": [0, 4], "choos": [0, 8, 9, 16, 21], "proper": [0, 7, 24], "depend": [0, 4, 5, 8, 9, 13, 16, 19, 26, 29], "result_fil": [0, 13], "eval": [0, 1, 2, 4, 13, 22, 26, 27, 29], "eval_metr": [0, 13, 29], "disabl": [0, 4, 16], "export": [0, 2, 4, 10, 23], "cuda_visible_devic": [0, 27], "multi": [0, 4, 11, 15, 17], "bash": [0, 27], "dist_test": [0, 27], "sh": [0, 4, 27], "gpu_num": 0, "reli": [0, 4, 5, 16], "pytorch": [0, 4, 9, 10, 11, 13, 15, 22, 23, 24, 25], "option": [0, 4, 8, 9, 15, 16, 19, 26, 27, 29], "argument": [0, 4, 10, 18, 19, 20, 23, 24, 25, 29], "filenam": [0, 1, 4, 16, 29], "output": [0, 4, 8, 9, 11, 15, 20, 22, 25, 26, 28, 29], "pickl": [0, 4, 16, 26, 29], "item": [0, 1, 4, 20], "valu": [0, 1, 4, 6, 8, 11, 15, 16, 17, 22, 24, 26, 29], "e": [0, 4, 5, 8, 9, 11, 13, 15, 16, 17, 19, 20, 21, 24, 26, 29], "g": [0, 4, 8, 9, 11, 13, 15, 16, 17, 19, 20, 21, 24, 26, 29], "proposal_fast": 0, "propos": [0, 4, 5, 10, 12, 15, 18, 20, 26, 29], "bbox": [0, 1, 2, 4, 5, 6, 15, 16, 17, 18, 19, 20, 26, 29], "segm": [0, 1, 2, 15, 26, 29], "map": [0, 4, 6, 8, 15, 16, 17, 18, 26, 29], "recal": [0, 4, 26], "could": [0, 1, 2, 4, 5, 6, 7, 8, 15, 16, 18, 19, 29], "well": [0, 7, 17, 29], "all": [0, 1, 4, 5, 7, 8, 9, 11, 15, 16, 19, 20, 22, 24, 25, 29], "metric": [0, 4, 6, 10, 15, 19, 26], "plot": [0, 4, 9, 29], "shown": [0, 8, 13, 28], "debug": [0, 15, 25, 26], "make": [0, 4, 5, 7, 8, 9, 12, 15, 16, 19, 20, 22, 29], "sure": [0, 4, 8, 9, 16, 20, 22], "gui": [0, 29], "otherwis": [0, 2, 9, 13], "encount": 0, "error": [0, 2, 4, 8, 9, 10], "cannot": [0, 8, 16, 19], "connect": 0, "x": [0, 1, 4, 9, 10, 11, 13, 15, 18], "server": [0, 7, 11, 26, 27, 29], "do": [0, 4, 5, 8, 12, 15, 16, 19, 22, 26, 28, 29], "NOT": 0, "threshold": [0, 4, 15, 26, 29], "remov": [0, 4, 8, 13, 20, 22, 26, 27], "cfg": [0, 4, 8, 15, 26, 27, 29], "kei": [0, 1, 2, 4, 5, 6, 8, 15, 16, 19, 20, 22, 24, 26, 29], "pair": [0, 12, 26, 29], "merg": [0, 4, 26, 29], "kwarg": [0, 4, 8, 18, 26], "assum": [0, 1, 2, 16, 18, 19, 29], "have": [0, 2, 4, 5, 6, 7, 8, 9, 11, 13, 15, 16, 17, 19, 22, 24, 26, 27, 29], "alreadi": [0, 4, 5, 8, 9, 12, 18, 19, 21, 22, 29], "r": [0, 1, 2, 4, 5, 8, 10, 12, 13, 16, 18, 20, 22, 23, 25, 26, 29], "cnn": [0, 1, 2, 4, 5, 8, 10, 12, 13, 16, 18, 20, 22, 23, 25, 26, 29], "press": 0, "ani": [0, 4, 8, 9, 13, 15, 22, 25, 26], "next": [0, 9, 12, 20], "paint": [0, 26, 29], "futur": [0, 4, 16], "faster_rcnn_r50_fpn_1x_result": 0, "pascal_voc": [0, 16, 29], "faster_rcnn_r50_fpn_1x_voc": 0, "faster_rcnn_r50_fpn_1x_voc0712_20200624": 0, "c9895d40": 0, "mask": [0, 1, 2, 4, 7, 8, 10, 13, 16, 18, 21, 23, 25, 26, 27, 29], "ap": [0, 1, 4, 8, 13, 16, 26], "mask_rcnn_r50_fpn_1x_coco": [0, 15, 25, 26, 29], "mask_rcnn_r50_fpn_1x_coco_20200205": [0, 29], "d4b0c5d6": [0, 29], "pkl": [0, 11, 29], "classwis": [0, 4], "mask_rcnn": [0, 1, 8, 11, 15, 21, 25, 26, 29], "true": [0, 2, 4, 5, 8, 11, 15, 16, 17, 18, 19, 20, 21, 22, 26], "dev": [0, 4, 15], "gener": [0, 4, 5, 7, 8, 12, 15, 28], "submit": [0, 4, 7, 13, 15, 26, 27], "jsonfile_prefix": [0, 27, 29], "mask_rcnn_test": 0, "dev_result": 0, "command": [0, 4, 7, 8, 9, 13, 27, 29], "two": [0, 1, 4, 5, 6, 8, 9, 15, 16, 17, 19, 21, 24, 27, 29], "txt": [0, 4, 7, 8, 16], "png": [0, 7], "mask_rcnn_r50_fpn_1x_cityscap": 0, "mask_rcnn_r50_fpn_1x_cityscapes_20200227": 0, "afe51d5a": 0, "txtfile_prefix": 0, "mask_rcnn_cityscapes_test_result": 0, "would": [0, 2, 6, 7, 8, 13, 15, 25, 26], "under": [0, 1, 2, 5, 8, 13, 15, 29], "cocodataset": [0, 1, 4, 7, 8, 15, 16, 22, 27], "directli": [0, 4, 8, 9, 16, 19, 22, 24, 26, 27, 29], "rest": 0, "images2coco": 0, "img_path": [0, 1], "class": [0, 1, 2, 4, 5, 6, 10, 12, 15, 17, 18, 19, 20, 21, 22, 23, 24, 26, 28, 29], "exclud": [0, 11], "text": [0, 4, 16], "list": [0, 4, 6, 8, 9, 10, 11, 12, 15, 16, 19, 20, 23, 24, 29], "categori": [0, 1, 4, 5, 6, 16, 27, 29], "same": [0, 4, 5, 6, 8, 11, 15, 16, 17, 18, 20, 22, 24, 25, 29], "suffix": 0, "bmp": 0, "after": [0, 1, 4, 5, 7, 8, 10, 13, 15, 16, 19, 23, 26, 27, 28, 29], "convers": [0, 4, 7, 10, 16, 25, 26], "complet": [0, 1, 4, 9, 12, 15, 27], "been": [0, 4, 5, 8, 11], "mode": [0, 4, 5, 8, 9, 15, 29], "default": [0, 2, 4, 5, 6, 7, 8, 11, 15, 16, 17, 20, 21, 25, 26, 28, 29], "modifi": [0, 1, 4, 5, 6, 8, 9, 10, 13, 17, 22, 23], "samples_per_gpu": [0, 2, 4, 15, 16], "dict": [0, 1, 2, 4, 6, 8, 11, 15, 16, 17, 18, 19, 20, 21, 22, 24, 28], "2": [0, 6, 8, 9, 10, 11, 13, 15, 20, 21, 22, 23, 24, 25, 26], "Or": [0, 2, 7, 13], "set": [0, 1, 2, 4, 5, 6, 7, 8, 10, 12, 13, 15, 16, 18, 21, 22, 23, 25, 26, 27, 29], "through": [0, 2, 4, 5, 8, 10, 16, 19, 23, 26], "replac": [0, 2, 4, 15, 22, 27], "defaultformatbundl": [0, 2, 15, 20, 22], "manual": [0, 4, 8, 9, 19, 26], "type": [0, 2, 4, 5, 8, 11, 15, 16, 17, 18, 19, 20, 21, 22, 24, 28, 29], "loadimagefromfil": [0, 2, 4, 15, 20, 22], "multiscaleflipaug": [0, 4, 15, 20], "img_scal": [0, 2, 15, 20, 22], "1333": [0, 4, 15, 20, 22], "800": [0, 1, 2, 15, 20, 22, 26, 29], "flip": [0, 4, 15, 20, 26], "fals": [0, 2, 4, 5, 8, 11, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 29], "transform": [0, 4, 5, 8, 12, 15, 20, 22], "resiz": [0, 2, 4, 15, 20], "keep_ratio": [0, 2, 4, 5, 15, 20], "randomflip": [0, 2, 4, 15, 20, 22], "normal": [0, 2, 4, 12, 15, 19, 20, 22, 26], "std": [0, 2, 4, 11, 15, 20, 22, 29], "pad": [0, 2, 4, 6, 15, 20, 22, 26], "size_divisor": [0, 2, 15, 20, 22], "32": [0, 2, 8, 13, 15, 20, 22, 29], "collect": [0, 2, 4, 15, 20, 22, 29], "box": [0, 4, 5, 6, 8, 9, 13, 15, 16, 18, 20, 26, 29], "too": [0, 4, 5, 8], "abov": [0, 1, 6, 8, 9, 15, 16, 19, 22, 25, 26, 27], "detail": [0, 1, 2, 4, 5, 7, 8, 11, 13, 15, 16, 19, 20, 24, 26, 29], "current": [0, 2, 4, 7, 8, 9, 11, 15, 16, 26, 28, 29], "pretrain": [0, 2, 4, 15, 21, 22, 24, 27, 29], "weight": [0, 2, 4, 5, 8, 10, 12, 15, 18, 19, 21, 22, 23, 29], "initi": [0, 2, 4, 10, 11, 15, 19, 23], "advanc": [0, 2], "network": [0, 2, 4, 8, 9, 10, 11, 12, 13, 18, 23], "unavail": [0, 2, 4], "slow": [0, 2, 8], "caus": [0, 2, 4, 5, 8], "begin": [0, 2, 19, 28], "per": [0, 2, 4, 5, 15, 26, 29], "16": [0, 6, 8, 10, 13, 15, 16, 25], "And": [0, 8, 19, 28, 29], "had": [0, 13], "auto_scale_lr": [0, 4], "base_batch_s": [0, 4], "_base_": [0, 1, 2, 15, 16, 18, 21, 22], "default_runtim": [0, 2, 15, 19, 21, 22], "base": [0, 1, 2, 4, 5, 7, 9, 10, 16, 18, 19, 23, 26, 29], "when": [0, 4, 5, 8, 9, 13, 15, 16, 17, 19, 24, 26, 28, 29], "meanwhil": [0, 16, 22], "order": [0, 4, 5, 12, 15, 16, 19, 29], "affect": [0, 4, 5, 9, 16, 19], "other": [0, 4, 5, 8, 9, 12, 15, 16, 17, 18, 19, 20, 21, 22, 24, 26, 29], "codebas": [0, 4, 11, 19], "flag": [0, 4, 8], "want": [0, 1, 2, 15, 16, 17, 18, 19, 22, 26, 28, 29], "featur": [0, 5, 15, 16, 18, 25, 26], "add": [0, 2, 4, 8, 15, 16, 20, 21, 22, 26, 28], "auto": [0, 2, 4, 8], "lr": [0, 2, 4, 11, 13, 15, 19, 21, 22], "check": [0, 4, 6, 8, 19, 28, 29], "befor": [0, 4, 5, 7, 8, 15, 16, 19, 21, 26, 29], "process": [0, 1, 2, 4, 5, 6, 7, 8, 9, 11, 15, 16, 19, 21, 22, 26, 28, 29], "becaus": [0, 4, 5, 8, 9, 15, 16, 19, 25, 26], "indic": [0, 1, 4, 8, 9, 15, 16, 24], "faster_rcnn_r50_caffe_fpn_90k_coco": 0, "pisa_faster_rcnn_x101_32x4d_fpn_1x_coco": 0, "case": [0, 1, 2, 4, 5, 8, 9, 11, 16, 19], "_nxm_": 0, "dictat": 0, "cornernet_hourglass104_mstest_32x3_210e_coco": 0, "96": [0, 8, 22], "scnet_x101_64x4d_fpn_8x1_20e_coco": 0, "rememb": [0, 16], "bottom": 0, "specif": [0, 13, 15, 16, 17, 18, 19, 21, 24, 26], "t": [0, 2, 4, 8, 9, 24, 29], "find": [0, 1, 4, 8, 9, 11, 17, 18, 19, 22], "those": [0, 4, 5, 8, 15, 19, 20], "xxx": [0, 4, 8, 10, 15, 18, 23, 24, 26], "its": [0, 5, 9, 16, 17, 24, 29], "basic": [0, 1, 2, 8, 13, 15, 18, 21], "accord": [0, 4, 8, 11, 15, 18, 19, 21, 26, 27, 29], "number": [0, 2, 4, 5, 8, 15, 16, 19, 21, 26, 29], "linear": [0, 2, 15, 19, 21, 24], "rule": [0, 4], "4": [0, 2, 5, 8, 9, 10, 11, 13, 15, 16, 17, 21, 22, 23, 24, 26, 27, 28], "pictur": 0, "each": [0, 1, 4, 5, 6, 8, 15, 16, 17, 18, 19, 20, 22], "01": [0, 2, 10, 21, 22], "08": [0, 11, 28], "don": [0, 2, 4, 24], "calcul": [0, 4, 5, 8, 11, 18, 19, 29], "optim": [0, 2, 4, 8, 10, 15, 21, 22, 23], "dure": [0, 4, 5, 6, 8, 9, 15, 16, 19, 21, 24, 26, 28], "log": [0, 4, 5, 10, 11, 15, 25, 28], "work": [0, 4, 5, 7, 8, 9, 15, 16, 24, 26, 29], "work_dir": [0, 1, 2, 8, 15, 27, 29], "via": [0, 4, 12], "cli": [0, 4], "valid": [0, 4, 7, 8, 15, 16, 19, 22, 28], "everi": [0, 15, 28], "epoch": [0, 4, 15, 19, 21, 22, 29], "interv": [0, 4, 11, 15, 19, 21, 28, 29], "12": [0, 2, 8, 10, 11, 15, 20, 22, 23, 29], "accept": [0, 4, 5], "sever": [0, 9, 12, 13], "suggest": [0, 8, 16], "overrid": [0, 4, 19, 24, 26], "resum": [0, 4, 8, 15], "previou": [0, 4, 5, 29], "both": [0, 4, 5, 11, 16, 20, 29], "statu": 0, "inherit": [0, 1, 2, 4, 5, 8, 10, 15, 16, 17, 18, 19, 22, 23, 24, 28], "interrupt": 0, "accident": 0, "finetun": [0, 4, 10, 16, 23], "consist": [0, 4, 5, 6, 11, 15, 16, 20], "just": [0, 8, 9, 24], "user": [0, 1, 4, 5, 8, 9, 15, 16, 17, 18, 19, 20, 21, 22, 24, 26, 28, 29], "conveni": [0, 4, 5, 11, 15], "dist_train": [0, 4], "remain": 0, "state": [0, 4, 19, 29], "port": [0, 15, 29], "29500": [0, 29], "avoid": [0, 2, 4, 8, 17, 18, 21], "commun": [0, 12], "conflict": 0, "5": [0, 1, 2, 5, 6, 8, 9, 10, 11, 13, 15, 16, 18, 20, 22, 23, 26, 29], "6": [0, 5, 8, 9, 10, 11, 13, 23, 25, 26], "29501": 0, "simpli": [0, 1, 2, 4, 5, 8, 16, 21], "ethernet": 0, "On": [0, 9], "first": [0, 1, 4, 5, 7, 8, 9, 11, 15, 17, 18, 19, 26, 28, 29], "nnode": 0, "node_rank": 0, "master_port": [0, 29], "master_addr": 0, "second": [0, 1, 2, 4, 8, 9, 15, 18], "infiniband": 0, "good": [0, 4, 21], "schedul": [0, 2, 4, 5, 10, 11, 15, 22, 23], "system": [0, 4, 15, 18, 28], "comput": [0, 4, 5, 8, 10, 23, 29], "cluster": [0, 29], "slurm_train": 0, "spawn": 0, "partit": [0, 27], "job_nam": [0, 27], "share": [0, 4, 15, 18], "mask_r50_1x": 0, "nf": 0, "xxxx": 0, "mask_rcnn_r50_fpn_1x": 0, "review": 0, "full": [0, 8, 9, 25], "variabl": [0, 4, 26], "wai": [0, 1, 4, 5, 8, 9, 15, 16, 19, 24], "sinc": [0, 4, 5, 6, 7, 8, 11, 15, 16, 18, 19, 20, 26, 27], "doe": [0, 4, 5, 8, 15, 16, 24, 27], "origin": [0, 2, 4, 5, 6, 7, 11, 13, 15, 16, 18, 26, 29], "config1": 0, "dist_param": [0, 15], "config2": 0, "backend": [0, 4, 15, 26, 29], "nccl": [0, 11, 15], "Then": [0, 1, 16, 18, 19, 21, 29], "know": [1, 2], "predefin": [1, 10], "balloon": 1, "exampl": [1, 2, 4, 6, 8, 9, 10, 11, 12, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], "describ": [1, 27, 29], "step": [1, 2, 9, 10, 15, 16, 19, 20, 21, 23, 24, 26, 28, 29], "There": [1, 4, 6, 8, 12, 15, 16, 19, 21], "support": [1, 4, 5, 6, 7, 8, 10, 12, 15, 18, 21, 23, 24, 29], "reorgan": [1, 4], "middl": 1, "method": [1, 4, 5, 6, 10, 11, 12, 15, 16, 19, 20, 23, 24, 28, 29], "easier": [1, 4, 9], "than": [1, 4, 5, 8, 11, 21], "third": [1, 2, 9], "give": [1, 16, 17, 19], "so": [1, 2, 4, 5, 8, 15, 16, 17, 19, 22, 24], "instanc": [1, 4, 5, 7, 8, 12, 13, 15, 16], "necessari": [1, 2, 15, 16, 17, 18], "imag": [1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 20, 22, 25, 26, 27], "int": [1, 5, 16, 22, 28], "width": [1, 4, 5, 8, 15, 16, 25, 26, 29], "height": [1, 4, 5, 8, 15, 16, 25, 26, 29], "file_nam": [1, 16], "str": [1, 24], "image_id": [1, 16], "category_id": [1, 6, 16], "rle": [1, 11], "polygon": [1, 4, 15], "area": [1, 4, 5, 8, 15, 16], "float": [1, 4, 5, 16, 20], "y": [1, 4, 9, 25, 26], "iscrowd": [1, 16], "1": [1, 6, 7, 8, 9, 10, 11, 13, 21, 22, 23, 24, 25, 26, 27], "supercategori": 1, "take": [1, 4, 8, 9, 15, 17, 20, 21, 22], "look": [1, 4, 12, 16], "base64_img_data": 1, "file_attribut": 1, "34020010494_e5cb88e1c4_k": 1, "fileref": 1, "region": [1, 12], "region_attribut": 1, "shape_attribut": 1, "all_points_x": 1, "1020": 1, "1000": [1, 5, 8, 15, 19, 26], "994": 1, "1003": 1, "1023": 1, "1050": 1, "1089": 1, "1134": 1, "1190": 1, "1265": [1, 4], "1321": 1, "1361": [1, 4], "1403": 1, "1428": 1, "1442": 1, "1445": 1, "1441": 1, "1427": 1, "1400": 1, "1316": 1, "1269": 1, "1228": [1, 4], "1198": 1, "1207": 1, "1210": 1, "1177": 1, "1172": 1, "1174": 1, "1170": 1, "1153": 1, "1127": [1, 4], "1104": 1, "1061": 1, "1032": 1, "all_points_i": 1, "963": 1, "899": 1, "841": 1, "787": 1, "738": 1, "700": 1, "663": 1, "638": 1, "621": [1, 4], "619": 1, "643": 1, "672": [1, 15], "720": [1, 16], "765": 1, "860": 1, "896": [1, 7], "942": 1, "990": 1, "1035": [1, 16], "1079": 1, "1112": 1, "1129": 1, "1144": 1, "1166": 1, "1150": [1, 4], "1136": [1, 4], "1122": 1, "1084": 1, "1037": 1, "989": 1, "1115004": 1, "where": [1, 4, 5, 8, 9, 20, 22, 26, 27, 28, 29], "o": [1, 29], "osp": 1, "convert_balloon_to_coco": 1, "ann_fil": [1, 15, 16, 22, 27], "image_prefix": 1, "data_info": [1, 16], "obj_count": 1, "idx": [1, 16], "v": [1, 9, 26], "enumer": [1, 16], "track_iter_progress": 1, "join": 1, "shape": [1, 4, 6, 17, 22, 25, 26, 29], "append": [1, 6, 16, 29], "label": [1, 4, 5, 6, 7, 16], "obj": 1, "assert": [1, 4, 18, 28], "px": 1, "poli": [1, 19], "zip": [1, 6, 7], "p": [1, 4, 20, 27], "x_min": 1, "y_min": 1, "x_max": 1, "y_max": 1, "min": [1, 25], "max": [1, 15, 25, 29], "data_anno": 1, "coco_format_json": 1, "dump": [1, 4], "successfulli": 1, "thu": [1, 4, 5, 8, 9, 16, 18, 19, 26], "fpn": [1, 2, 4, 13, 15, 16, 18], "mask_rcnn_r50_caffe_fpn_mstrain": [1, 21], "poly_1x_balloon": 1, "highlight": [1, 2, 16], "modif": [1, 2, 4, 5, 8, 9, 15, 16, 17, 18, 19], "poly_1x_coco": 1, "num_class": [1, 2, 4, 6, 15, 16, 18, 19, 21, 29], "head": [1, 2, 4, 5, 6, 10, 12, 15, 16, 17, 19, 23], "match": [1, 2, 4, 5, 8, 9, 15, 19], "roi_head": [1, 2, 15, 16, 18, 21], "bbox_head": [1, 2, 4, 6, 15, 16, 18, 21], "mask_head": [1, 2, 4, 6, 15, 16, 18, 21], "relat": [1, 4, 5, 17, 20, 29], "dataset_typ": [1, 15, 16, 22], "img_prefix": [1, 4, 15, 16, 22, 27], "annotation_coco": 1, "pre": [1, 2, 4, 5, 8, 10, 15, 16, 22, 23], "obtain": [1, 5, 8, 11, 21, 24, 29], "higher": [1, 4, 5, 11, 24], "load_from": [1, 2, 15, 21], "poly_3x_coco_bbox_map": [1, 21], "408__segm_map": [1, 21], "37_20200504_163245": [1, 21], "42aa3d00": [1, 21], "python": [1, 2, 4, 5, 7, 8, 9, 11, 13, 15, 25, 26, 27, 29], "latest": [1, 2, 4, 5, 25, 26, 29], "cascad": [2, 4, 5, 12, 13, 15, 16, 25, 26], "r50": [2, 4, 5, 15, 16], "demonstr": [2, 9], "rotat": [2, 4], "translat": [2, 4], "augment": [2, 4, 7, 8, 10, 15, 18, 23], "annot": [2, 4, 5, 7, 8, 15, 20, 22, 27, 29], "mmdet_dataset": [2, 4], "data_root": [2, 15, 22], "call": [2, 4, 5, 6, 8, 9, 15, 17, 19, 24], "exist": [2, 4, 8, 10, 15, 20, 21, 22, 27], "firstli": 2, "creat": [2, 4, 5, 8, 9, 12, 15, 16, 18, 19, 25, 26, 29], "builder": [2, 16, 18], "register_modul": [2, 4, 16, 17, 18, 19, 20, 22, 28], "nn": [2, 6, 17, 18, 24], "__init__": [2, 4, 17, 18, 19, 20, 22, 24, 28], "self": [2, 4, 6, 8, 12, 16, 17, 18, 20, 22, 24, 28], "in_channel": [2, 4, 15, 18, 21, 22], "out_channel": [2, 15, 18], "num_out": [2, 15, 18], "start_level": [2, 18, 22], "end_level": [2, 4, 18], "add_extra_conv": [2, 18], "pass": [2, 4, 15, 18, 19], "forward": [2, 6, 11, 18, 20, 22, 29], "ignor": [2, 4, 11, 16, 18, 24], "line": [2, 4, 8, 18], "altern": [2, 13, 15, 18], "custom_import": [2, 18, 19, 20, 22], "allow_failed_import": [2, 18, 19, 20, 22], "256": [2, 7, 15, 17, 18, 21, 22], "512": [2, 7, 15, 18, 22], "1024": [2, 8, 15, 18, 21], "2048": [2, 15, 18], "about": [2, 4, 6, 7, 10, 17, 19, 21, 22, 23, 26, 28, 29], "backbon": [2, 4, 5, 8, 10, 11, 12, 13, 15, 23, 27], "loss": [2, 4, 5, 8, 10, 12, 15, 19, 23, 28, 29], "runtim": [2, 4, 8, 9, 10, 21, 23, 26], "gradient": [2, 4, 5, 8, 15, 19], "clip": [2, 4, 5, 8, 15, 19], "hook": [2, 4, 5, 10, 15, 22, 23], "guidelin": 2, "respect": [2, 8, 15, 16, 17, 26], "cascade_mask_rcnn_r50_augfpn_autoaug_10e_cityscap": 2, "cascade_mask_rcnn_r50_fpn": 2, "cityscapes_inst": [2, 21], "none": [2, 4, 8, 15, 17, 18, 19, 21, 24, 25, 26], "imagenet": [2, 4, 8, 13, 15], "instead": [2, 4, 5, 9], "init_cfg": [2, 4, 5, 10, 15, 22, 23], "defaultli": 2, "our": [2, 5, 7, 9, 11, 15, 25, 29], "80": [2, 15, 16, 18], "involv": [2, 16, 26], "shared2fcbboxhead": [2, 15, 16, 21], "fc_out_channel": [2, 15, 18, 21], "roi_feat_s": [2, 15, 18, 21], "7": [2, 8, 10, 11, 13, 15, 18, 23, 25, 26, 29], "bbox_cod": [2, 15, 18, 21], "deltaxywhbboxcod": [2, 15, 18, 21], "target_mean": [2, 15, 18, 21], "target_std": [2, 15, 18, 21], "reg_class_agnost": [2, 4, 15, 18, 21], "loss_cl": [2, 6, 15, 17, 18, 21, 29], "crossentropyloss": [2, 4, 15, 17, 18, 21], "use_sigmoid": [2, 15, 17, 18, 21], "loss_weight": [2, 4, 15, 17, 18, 21], "loss_bbox": [2, 6, 15, 18, 21, 29], "smoothl1loss": [2, 18, 21], "beta": [2, 4, 15, 17, 18, 21], "05": [2, 15], "033": 2, "067": 2, "fcnmaskhead": [2, 15, 21], "num_conv": [2, 15, 18, 21], "conv_out_channel": [2, 15, 18, 21], "loss_mask": [2, 15, 21], "use_mask": [2, 15, 21], "over": [2, 4, 16, 29], "write": [2, 16, 20, 21, 22], "train_pipelin": [2, 15, 16, 20, 22], "ad": [2, 4, 5, 6, 7, 8, 15, 19, 20], "autoaug": 2, "img_norm_cfg": [2, 4, 5, 11, 15, 20, 22], "123": [2, 11, 15, 20, 22], "675": [2, 11, 15, 20, 22], "116": [2, 11, 15, 20, 22, 29], "28": [2, 10, 11, 13, 15, 20, 22, 29], "103": [2, 11, 15, 20, 22], "53": [2, 11, 15, 20, 22], "58": [2, 11, 15, 20, 22], "395": [2, 11, 15, 20, 22], "57": [2, 11, 15, 20, 22, 29], "375": [2, 11, 15, 20, 22], "to_rgb": [2, 11, 15, 20, 22], "loadannot": [2, 15, 20, 22], "with_bbox": [2, 15, 20, 22], "with_mask": [2, 6, 15], "polici": [2, 15, 19, 21], "level": [2, 4, 5, 15, 24], "img_fill_v": 2, "124": 2, "104": 2, "prob": 2, "scale": [2, 4, 5, 7, 11, 12, 15, 17, 20, 26], "flip_ratio": [2, 4, 15, 20, 22], "gt_bbox": [2, 4, 15, 18, 20, 22], "gt_label": [2, 15, 18, 20, 22], "gt_mask": [2, 15, 18, 20], "batch_siz": [2, 4], "gpu": [2, 4, 5, 8, 9, 11, 13, 15, 25, 26, 27, 29], "workers_per_gpu": [2, 4, 15, 16], "sgd": [2, 15, 19, 21, 22], "momentum": [2, 8, 15, 19, 21, 22], "9": [2, 8, 9, 10, 11, 13, 15, 16, 19, 21, 22, 23, 26, 28, 29], "weight_decai": [2, 15, 19, 21, 22], "0001": [2, 8, 15, 19, 21, 22], "optimizer_config": [2, 8, 15, 19, 21], "grad_clip": [2, 8, 15, 19, 21], "learn": [2, 4, 8, 10, 12, 17, 19, 21, 23], "lr_config": [2, 15, 19, 21], "warmup": [2, 4, 5, 8, 11, 15, 19, 21], "warmup_it": [2, 8, 15, 19, 21], "500": [2, 8, 11, 15, 21], "warmup_ratio": [2, 15, 19, 21], "001": [2, 5, 15, 21], "runner": [2, 4, 15, 19, 21, 22, 24, 28], "epochbasedrunn": [2, 4, 15], "max_epoch": [2, 15, 21], "10": [2, 8, 9, 10, 11, 15, 16, 19, 23, 25, 28, 29], "stabl": [2, 4, 8], "http": [2, 4, 7, 8, 9, 15, 18, 21, 22, 27, 29], "openmmlab": [2, 4, 5, 9, 12, 21, 22], "com": [2, 4, 8, 9, 15, 21, 22, 29], "v2": [2, 5, 10, 12, 16, 19, 21], "cascade_rcnn": [2, 25, 26], "cascade_mask_rcnn_r50_fpn_1x_coco": [2, 16, 25, 26], "cascade_mask_rcnn_r50_fpn_1x_coco_20200203": 2, "9d4dcb24": 2, "singl": [4, 5, 6, 11, 12, 13, 15, 16, 26, 27, 29], "distribut": [4, 11, 15, 16, 20, 29], "train": [4, 6, 7, 9, 10, 12, 15, 16, 20, 22, 23, 24, 27, 28, 29], "8176": 4, "polygonmask": 4, "filterannot": 4, "8136": 4, "mdformat": 4, "version": [4, 8, 26, 29], "python3": 4, "8195": 4, "gpg": 4, "dockerfil": [4, 9], "8215": 4, "wandbloggerhook": [4, 19], "8273": 4, "issu": [4, 5, 8, 9, 19, 25, 26], "8439": 4, "mim": 4, "extras_requir": 4, "setup": [4, 5, 15], "8194": 4, "get": [4, 8, 11, 17, 19, 23, 29], "maco": [4, 9], "8434": 4, "test": [4, 6, 7, 8, 9, 10, 11, 15, 16, 18, 23, 25, 26], "ci": 4, "8230": 4, "8240": 4, "updat": [4, 5, 8, 12, 15, 19, 20, 22, 27], "maskform": [4, 27], "compat": [4, 8, 9, 10, 16], "dictionari": 4, "8263": 4, "clean": [4, 13], "pillow": 4, "8229": 4, "tutori": [4, 13, 23], "8118": 4, "8120": 4, "metafil": 4, "releas": [4, 5, 11, 12, 21], "model": [4, 6, 7, 9, 12, 15, 16, 17, 19, 20, 22, 23, 24, 27, 28], "8294": 4, "link": [4, 5, 7, 11, 21, 25], "8391": 4, "total": [4, 11, 15, 19, 28], "develop": [4, 8, 9, 10, 12, 23], "contribut": 4, "thank": 4, "zwwwayn": 4, "ayulockin": 4, "mxbonn": 4, "mishra1": 4, "youth": 4, "got": [4, 29], "mixaill76": 4, "chhluo": 4, "jbwang1997": 4, "atinfin": 4, "shinya7i": 4, "duanzhihua": 4, "stland": 4, "admin": 4, "bigwangyudong": 4, "grimoir": 4, "xiaoyuan0203": 4, "dedic": 4, "wandblogg": 4, "convnext": 4, "ddod": 4, "solov2": [4, 12], "mask2form": [4, 5], "renam": [4, 5], "7571": [4, 5], "mask2former_xxx_coco": [4, 5], "repres": [4, 5, 8, 12], "7281": 4, "7279": 4, "7441": 4, "8032": 4, "yolox": [4, 8], "7912": 4, "7784": 4, "runtimeerror": [4, 8], "8083": 4, "7459": 4, "log_config": [4, 15, 19, 21], "mmdetwandbhook": 4, "init_kwarg": 4, "log_checkpoint": 4, "log_checkpoint_metadata": 4, "num_eval_imag": 4, "colab": [4, 8], "avoidoom": [4, 8], "oom": [4, 8], "7434": 4, "8091": 4, "try": [4, 8, 9, 10, 23], "avoidcudaoom": [4, 8], "memori": [4, 8, 15, 28, 29], "retri": [4, 8], "empty_cach": [4, 8], "still": [4, 5, 8, 16, 29], "fail": [4, 5, 8], "fp16": [4, 8], "copi": [4, 5, 8, 9, 11], "cpu": [4, 5, 8, 11, 26, 29], "continu": [4, 8, 16], "retry_if_cuda_oom": [4, 8], "some_funct": [4, 8], "input1": [4, 8], "input2": [4, 8], "decor": [4, 8, 18], "arg": [4, 8, 20, 22, 24, 28], "return": [4, 5, 6, 8, 16, 18, 19, 20], "read": [4, 16, 28, 29], "gpu_collect": 4, "7672": 4, "speedup": 4, "video": [4, 12], "infer": [4, 5, 9, 10, 25, 26, 29], "acceler": [4, 5, 15, 19], "stage": [4, 6, 15, 19, 22, 26, 29], "7832": 4, "7492": 4, "analysi": [4, 10, 11], "analyze_result": [4, 29], "minut": 4, "7891": 4, "block_dil": 4, "dilatedencod": 4, "7812": 4, "7922": 4, "dyhead": 4, "swin": [4, 12], "larg": [4, 5, 7, 8, 12], "7733": 4, "wrong": [4, 5], "act_cfg": 4, "swintransform": 4, "7794": 4, "7959": 4, "rewrit": 4, "guid": [4, 9], "7897": 4, "7810": 4, "anchor": [4, 5, 9, 10, 12, 15], "8006": 4, "markdownlint": 4, "rubi": 4, "8009": 4, "darththoma": 4, "solyah": 4, "lutingwang": 4, "chenxinfeng4": 4, "czm369": 4, "chenastron": 4, "austinmw": 4, "shanyaliux": 4, "hellock": 4, "m": [4, 16, 22, 29], "hhaandroid": 4, "irvingao": 4, "zhanggefan": 4, "keiku": 4, "petervennerstrom": 4, "simpl": [4, 15, 16, 29], "past": [4, 5, 9], "strong": [4, 11], "automat": [4, 7, 9, 16, 19], "awar": 4, "sampler": [4, 15, 17], "openimag": [4, 11], "dataset": [4, 5, 8, 15, 17, 19, 20, 22, 23, 26, 27], "7501": 4, "train_dataload": 4, "class_aware_sampl": 4, "num_sample_class": 4, "classawaresampl": 4, "7436": 4, "7482": 4, "n": [4, 8, 16, 26, 28], "batch": [4, 5, 6, 8, 12, 15, 21, 25, 26], "rate": [4, 8, 15, 19, 21], "equal": [4, 12, 16], "correct": [4, 8, 15, 16, 25, 26, 29], "custom": [4, 6, 10, 21, 22, 23, 25, 26, 29], "dataload": [4, 20], "handl": [4, 5, 6, 13], "7668": 4, "comparison": [4, 29], "old": [4, 5, 8, 15], "64": [4, 8, 13, 15, 22], "clear": [4, 9], "val_dataload": 4, "test_dataload": 4, "style": [4, 8, 10, 11, 13, 23, 29], "loader": 4, "profil": [4, 28], "monitor": [4, 6], "7560": 4, "custom_hook": [4, 8, 19, 22, 28], "memoryprofilerhook": [4, 10, 23], "50": [4, 11, 13, 15, 16, 19, 27, 28, 29], "mlu": 4, "chip": 4, "7578": 4, "re": [4, 5, 7, 17], "splite": 4, "tag": 4, "7641": 4, "dicecost": 4, "k": [4, 5, 16, 28, 29], "net": 4, "maskhungarianassign": 4, "7716": 4, "split": [4, 7, 16], "semi": [4, 12], "supervis": [4, 12], "7431": 4, "pathlib": 4, "fromfil": 4, "7685": 4, "client": 4, "7433": 4, "probabl": [4, 15, 20], "mosaic": [4, 10, 23], "7371": 4, "interpol": [4, 26], "7585": 4, "invalid": [4, 8, 24, 28], "deform_sampl": 4, "7567": 4, "color_them": 4, "effect": [4, 5, 8, 9, 13, 26], "confus": [4, 10, 16], "matrix": [4, 10], "7701": 4, "neck": [4, 5, 15, 22], "index": [4, 9, 10, 15, 29], "end": [4, 12, 28], "7502": 4, "mix_result": 4, "multiimagemixdataset": [4, 22], "7530": 4, "resnet": [4, 5, 8, 10, 11, 15, 18, 22, 27], "plugin": [4, 15, 25, 26], "7797": 4, "enhanc": 4, "load_json_log": 4, "analyze_log": [4, 11, 29], "7732": 4, "7676": 4, "mix": [4, 8, 16], "precis": [4, 8], "simotaassign": 4, "7516": 4, "inf": [4, 7], "100000": 4, "7778": 4, "channel": [4, 5, 10, 15, 23], "7642": 4, "unfreez": [4, 10, 23], "7570": 4, "fast_rcnn": 4, "7549": 4, "deform": [4, 5, 9], "detr": [4, 5, 26], "7690": 4, "scratch": 4, "get_start": [4, 25, 26], "md": [4, 7, 25, 26, 29], "7575": 4, "7595": 4, "7709": 4, "retinanet": [4, 5, 13, 22, 25, 26, 29], "7387": 4, "efficientnet": [4, 22], "7646": 4, "jovialio": 4, "zhangsanfeng2022": 4, "harryzj": 4, "jamiechoi1995": 4, "nestiank": 4, "peterh0323": 4, "rangek": 4, "mattcasey02": 4, "weiji14": 4, "yulv": 4, "git": [4, 8, 9], "xiefeifeihu": 4, "fang": 4, "ming": 4, "meng976537406": 4, "nijkah": 4, "sudz123": 4, "ccoding04": 4, "sheffieldcao": 4, "zytx121": 4, "jshilong": 4, "rangilyu": 4, "attent": [4, 12, 15], "univers": 4, "rethink": [4, 11], "convolut": [4, 8, 9, 12, 15], "neural": [4, 8, 13], "anymor": [4, 5], "recip": 4, "fine": [4, 16, 19], "tune": [4, 8, 19, 21], "high": [4, 8, 12, 24], "torchvis": [4, 8, 9, 11, 15, 24], "6938": 4, "7466": 4, "7471": 4, "7514": 4, "7386": 4, "seed": 4, "rank": [4, 12], "7432": 4, "launch": [4, 29], "machin": [4, 11], "without": [4, 5, 8, 11, 18, 22, 26], "slurm": [4, 27], "7415": 4, "7489": 4, "unit": [4, 6], "7270": 4, "adjust": [4, 5], "get_class": 4, "filecli": 4, "7276": 4, "forc": 4, "get_bbox": 4, "yolox_head": 4, "float32": [4, 6, 16], "7324": 4, "misplac": 4, "loadpanopticannot": 4, "7388": 4, "reduct": [4, 8, 18], "celoss": 4, "7449": 4, "crossentropycost": 4, "7537": 4, "leak": [4, 28], "panpot": [4, 5], "7538": 4, "broadcast": 4, "yolov3": [4, 25, 26, 29], "7551": 4, "chines": 4, "onnx2tensorrt": [4, 25], "7219": 4, "7310": 4, "inform": [4, 5, 8, 9, 16, 25, 26, 27, 28, 29], "local": [4, 8, 9, 12], "distil": 4, "7350": 4, "7178": 4, "non": [4, 10, 23], "squar": 4, "7235": 4, "coco_panopt": 4, "qualiti": [4, 5, 12, 15], "7315": 4, "channel_ord": 4, "7258": 4, "point": [4, 5, 12, 28], "mask_point_head": 4, "7353": 4, "7313": 4, "robust": [4, 10, 13], "7407": 4, "supplementari": 4, "sync_random_se": 4, "7440": 4, "docstr": 4, "cross": [4, 12, 15], "entropi": 4, "7472": 4, "7503": 4, "record": [4, 7, 15, 28], "question": [4, 10, 22], "7507": 4, "mmcl": [4, 22], "7438": 4, "produc": [4, 5, 8, 15, 27], "predict": [4, 7, 10, 15, 16, 17, 18, 21, 26, 27], "7430": 4, "haofanwang": 4, "yangrisheng": 4, "tripl": 4, "mu": 4, "hikaritju": 4, "imflash217": 4, "274869388": 4, "matrixgame2018": 4, "jingweizhang12": 4, "xiangxu": 4, "0103": 4, "osbm": 4, "ceroytr": 4, "bung": 4, "bedstraw": 4, "herb": 4, "daavoo": 4, "jiangyitong": 4, "yarkabl": 4, "pixel": [4, 5], "classif": [4, 5, 6, 11, 12, 15, 17, 29], "Not": 4, "semant": [4, 6, 7], "7212": 4, "dynam": [4, 12, 25, 26], "unifi": [4, 12], "6823": 4, "strike": 4, "back": [4, 6], "bring": [4, 5], "7001": 4, "open": [4, 8, 9, 15, 22], "6331": 4, "timm": 4, "7020": 4, "7041": 4, "get_palett": 4, "determin": [4, 25, 26, 29], "whether": [4, 8, 9, 15, 16, 19, 25, 26, 28, 29], "palett": 4, "best": [4, 8, 10], "key_scor": 4, "7101": 4, "mixup": 4, "filter": [4, 8, 15, 16, 29], "7080": 4, "miss": [4, 5, 27], "properti": 4, "sablhead": 4, "7091": 4, "nan": [4, 8, 28], "7147": 4, "attributeerror": 4, "downstream": [4, 5, 11], "7230": 4, "up": [4, 5, 24], "simota": 4, "7098": 4, "docs_zh": 4, "cn": 4, "7188": 4, "aronlin": 4, "luoochen": 4, "dvansa": 4, "siatwangmin": 4, "del": 4, "zhenwu": 4, "vikashranjan26": 4, "hjoonkwon": 4, "zhijian": 4, "liu": 4, "standard": [4, 10, 15, 29], "content": [4, 7, 8, 21], "readm": [4, 7], "meta": 4, "significantli": 4, "templat": [4, 8, 19], "algorithm": [4, 8, 9, 12, 22, 27, 29], "align": [4, 5, 18], "dcn": [4, 5, 9, 13], "put": [4, 27], "dcnv2": 4, "color": [4, 26], "6716": 4, "7016": 4, "7015": 4, "6973": 4, "__repr__": 4, "compos": [4, 15], "6951": 4, "badzipfil": 4, "docker": [4, 10], "6966": 4, "7019": 4, "clamp": 4, "7074": 4, "relax": 4, "wrapper": [4, 10, 22, 23], "7085": 4, "keep": [4, 5, 15], "reassign": 4, "paa": 4, "7032": 4, "demo": [4, 9, 25, 26, 29], "doc": [4, 15, 19, 22, 29], "7092": 4, "6974": 4, "7036": 4, "6897": 4, "cv2": 4, "multiprocess": 4, "6867": 4, "deprec": [4, 5, 10, 11, 23], "6998": 4, "organ": [4, 7], "7051": 4, "grad": 4, "problem": [4, 5, 8, 25, 26], "tood": 4, "sigmoidgeometricmean": 4, "7090": 4, "zimoqingfeng": 4, "srishilesh": 4, "imyhxi": 4, "jenhaoyang": 4, "jliu": 4, "ac": [4, 7], "kimnamu": 4, "shengliliu": 4, "garvan2021": 4, "ciusji": 4, "diyer22": 4, "q3394101": 4, "zhouzaida": 4, "gaotongxiao": 4, "topsy404": 4, "antoandgar": 4, "One": [4, 16, 29], "iccv": [4, 12], "oral": 4, "6746": 4, "6727": 4, "6744": 4, "gt_semantic_seg": [4, 20], "collat": [4, 5], "6837": 4, "6845": 4, "bc": [4, 5], "get_local_path": 4, "6719": 4, "sync_norm_hook": 4, "bn": [4, 11, 15, 18], "layer": [4, 5, 8, 15, 19, 22, 24], "6852": 4, "pycocotool": [4, 10], "matter": [4, 5, 16], "what": [4, 5, 11, 12, 19, 28], "platform": [4, 12, 26], "6838": 4, "6770": 4, "precommit": 4, "6802": 4, "select": [4, 5, 8, 13], "6781": 4, "gkagko": 4, "fcakyon": 4, "www516717402": 4, "vansin": 4, "zactodd": 4, "6698": 4, "densehead": 4, "6625": 4, "convfchead": 4, "6624": 4, "pseudosampl": [4, 15], "6622": 4, "pvt": 4, "6663": 4, "dtype": [4, 6], "basedensehead": 4, "6767": 4, "6733": 4, "combin": [4, 13, 16], "6621": 4, "get_ann_info": [4, 16], "dataset_wrapp": 4, "6526": 4, "ratio": [4, 5, 15], "6732": 4, "bbox_clip_bord": 4, "6730": 4, "6717": 4, "mmhuman3d": 4, "6699": 4, "faq": [4, 9, 10, 23], "6587": 4, "detect_anomalous_param": [4, 8], "6697": 4, "ljoson": 4, "zcmax": 4, "zhaoxin111": 4, "gt9505": 4, "assign": [4, 5, 8, 15], "persistent_work": 4, "6342": 4, "6435": 4, "repeatedli": 4, "warn": [4, 5], "messag": [4, 8, 25, 28], "6584": 4, "infinit": [4, 28], "dist": [4, 9], "6501": 4, "ssd512": 4, "6574": 4, "onnx": [4, 10, 23], "6558": 4, "6592": 4, "6443": 4, "reduc": [4, 5, 8, 17, 21], "cost": [4, 8], "photometricdistort": [4, 20], "6442": 4, "ohem": 4, "seesaw": 4, "6514": 4, "6567": 4, "floydhsiu": 4, "andreapi": 4, "st9007a": 4, "hachreak": 4, "vealocia": 4, "harboryuan": 4, "queryinst": [4, 5], "6460": 4, "6344": 4, "aug": 4, "6398": 4, "spatialreductionattent": 4, "6488": 4, "trunc_normal_init": 4, "6432": 4, "print": [4, 8, 9, 10, 15, 22, 25, 26], "api": [4, 8, 9, 15, 19, 28, 29], "logger": [4, 15, 19, 28], "6505": 4, "alwai": [4, 25, 26], "locat": [4, 12, 19, 20], "6405": 4, "random": [4, 20], "6457": 4, "corrupt": [4, 20, 29], "benchmark": [4, 5, 10], "6375": 4, "6396": 4, "groie": 4, "6401": 4, "6050": 4, "dens": [4, 12], "decoupl": [4, 16], "logic": [4, 5, 18], "5317": 4, "6003": 4, "6369": 4, "6268": 4, "6315": 4, "5996": 4, "init_weight": [4, 5, 24], "fcn_mask_head": [4, 15], "6378": 4, "imshow_bbox": 4, "rpn": [4, 5, 12, 15, 17], "6386": 4, "broken": 4, "6382": 4, "scale_factor": [4, 20], "6374": 4, "hardcod": 4, "6317": 4, "randomaffin": [4, 22], "coordin": [4, 26], "recorrect": 4, "6293": 4, "init": 4, "final": [4, 5, 13, 17, 21, 27, 29], "cl": [4, 7, 18, 24], "reg": [4, 18, 24], "convfc": 4, "6279": 4, "img_shap": [4, 16, 20, 26], "auto_aug": 4, "6259": 4, "two_stag": 4, "6256": 4, "interfac": 4, "6308": 4, "polish": 4, "6243": 4, "spell": 4, "commit": [4, 11], "typo": 4, "6306": 4, "6245": 4, "6190": 4, "6284": 4, "forward_dummi": [4, 18], "yolact": 4, "get_flop": [4, 29], "6079": 4, "6252": 4, "beautifi": 4, "6195": 4, "dense_head": [4, 15], "migrat": [4, 15], "prior_gener": 4, "boyden": 4, "onnkeat": 4, "yhcao6": 4, "dapangpangx": 4, "yellowdolphin": 4, "cclauss": 4, "kennymckormick": 4, "pingguokil": 4, "collinzrj": 4, "pvtv2": 4, "solo": [4, 12], "jitter": 4, "baselin": 4, "5780": 4, "5832": 4, "6132": 4, "structru": 4, "5508": 4, "5904": 4, "5991": 4, "6100": 4, "5983": 4, "workflow": [4, 10, 15, 23, 24], "5986": 4, "torchserv": [4, 10], "5936": 4, "onnxsim": 4, "6117": 4, "model_wrapp": 4, "5975": 4, "regress": [4, 5, 6, 8, 15, 17, 18, 26, 29], "empti": [4, 10, 16], "tensor": [4, 15, 17, 26, 29], "5976": 4, "contigu": 4, "centernet_head": 4, "6016": 4, "6034": 4, "aug_test": 4, "length": [4, 16, 19], "det_bbox": 4, "6088": 4, "5941": 4, "dynamic_ax": 4, "6104": 4, "dynamic_shap": 4, "syncrandomsizehook": [4, 10, 23], "6144": 4, "6172": 4, "5897": 4, "divisor": 4, "flop": [4, 29], "potenti": [4, 12, 28], "6076": 4, "customize_dataset": 4, "5915": 4, "convent": [4, 7, 10, 15, 20, 27], "5825": 4, "descript": [4, 8, 10, 11, 23, 25, 29], "5886": 4, "extra_repr": 4, "dropblock": 4, "6140": 4, "pytorch1": 4, "5862": 4, "6069": 4, "xml": 4, "5943": 4, "6017": 4, "opencv": [4, 8], "headless": [4, 8], "albument": [4, 8], "5868": 4, "5969": 4, "theme": 4, "sphinx": 4, "6146": 4, "paper": [4, 8, 10, 29], "field": [4, 8, 12, 16, 18, 19, 20, 22], "6043": 4, "6152": 4, "multipl": [4, 8, 12, 13, 19, 20, 21], "5747": 4, "morkovka1337": 4, "guillaumefrd": 4, "guigarfr": 4, "ypwh": 4, "martayang": 4, "justiceeem": 4, "zhaojinjian0000": 4, "vvsssssk": 4, "aravind": 4, "anantha": 4, "wangbo": 4, "zhao": 4, "czczup": 4, "whai362": 4, "marijnl": 4, "5577": 4, "5902": 4, "5748": 4, "3x": 4, "5636": 4, "unlabel": 4, "5643": 4, "5674": 4, "yolo": [4, 10, 25, 26], "5644": 4, "post": [4, 11, 26, 29], "5851": 4, "cocopanopticdataset": [4, 6, 16], "5896": 4, "adapt": [4, 12, 15, 17, 29], "browse_dataset": [4, 20, 29], "concaten": [4, 7], "5935": 4, "patchemb": 4, "patchmerg": 4, "adaptivepad": 4, "5952": 4, "5859": 4, "lose": 4, "imshow_det_bbox": 4, "5845": 4, "imagetotensor": [4, 15, 20], "5756": 4, "regress_by_class": [4, 6], "roihead": [4, 6, 15], "5884": 4, "ciou": 4, "alpha": [4, 17], "5835": 4, "multiscale_output": 4, "hrnet": [4, 15], "5887": 4, "prioriti": [4, 5, 8, 19, 24], "evalhook": [4, 5, 10, 19, 23], "low": [4, 5, 15, 29], "5882": 4, "appli": [4, 15], "rescal": [4, 8, 18], "5899": 4, "5947": 4, "5930": 4, "data_pipelin": 4, "5662": 4, "ema": 4, "publish": [4, 10, 12, 13], "5858": 4, "5881": 4, "5540": 4, "legaci": [4, 5], "5627": 4, "customize_loss": 4, "5826": 4, "model_zoo": 4, "5827": 4, "zywvvd": 4, "oceanpang": 4, "ddonatien": 4, "haotian": 4, "viibridg": 4, "muyun99": 4, "xvjiarui": 4, "5758": 4, "5760": 4, "5767": 4, "5770": 4, "5774": 4, "5777": 4, "5808": 4, "5828": 4, "5848": 4, "ssd": [4, 15, 25, 26], "5789": 4, "cast": 4, "5820": 4, "deploy": [4, 10, 12, 25, 26], "5790": 4, "5779": 4, "upsample_lik": 4, "interpolate_a": 4, "5788": 4, "haocheny": 4, "xiaohu2015": 4, "hslol": 4, "zhiqwang": 4, "adamdad": 4, "johnson": 4, "wang": 4, "mmeendez8": 4, "mobilenetv2": 4, "lite": 4, "5732": 4, "pdf": [4, 29], "epub": 4, "5738": 4, "5645": 4, "ignore_index": 4, "5646": 4, "5676": 4, "5510": 4, "5231": 4, "5486": 4, "5544": 4, "5526": 4, "multiclass_nm": 4, "5673": 4, "5603": 4, "5550": 4, "regnet": [4, 10, 11], "5655": 4, "5559": 4, "count": [4, 29], "5654": 4, "unittest": 4, "numclasscheckhook": [4, 10, 16, 23], "5626": 4, "5546": 4, "global": [4, 12, 28], "5592": 4, "valid_mask": 4, "rpnhead": [4, 15], "5562": 4, "5561": 4, "anchor_head": 4, "5555": 4, "5552": 4, "mmdet_tutori": 4, "5511": 4, "crash": 4, "get_root_logg": [4, 19], "log_level": [4, 15], "5521": 4, "5502": 4, "iterbasedrunn": [4, 15], "5490": 4, "mmtrack": 4, "5620": 4, "5718": 4, "5618": 4, "5558": 4, "5423": 4, "5593": 4, "5421": 4, "5408": 4, "5369": 4, "5419": 4, "5530": 4, "5531": 4, "resourc": [4, 25], "limit": [4, 25], "5697": 4, "instaboost": [4, 8], "5640": 4, "reduction_overrid": [4, 18], "5515": 4, "repeatdataset": [4, 16], "centernet": 4, "5509": 4, "unnecessari": [4, 19, 22], "autoassign": 4, "5519": 4, "5273": 4, "outsider565": 4, "electroniceleph": 4, "likyoo": 4, "noobi": 4, "yyz561": 4, "zeakei": 4, "chenyangliu": 4, "magic": [4, 9], "qingswu": 4, "buxianchen": 4, "simple_test": [4, 6, 18], "revert": 4, "test_mixin": [4, 18], "effici": 4, "readabl": 4, "moco": 4, "swav": 4, "5286": 4, "5179": 4, "5233": 4, "mseloss": 4, "5437": 4, "5168": 4, "pointrend": [4, 25, 26], "5440": 4, "mismatch": 4, "4980": 4, "multiscaledeformableattent": 4, "5338": 4, "gcnet": 4, "resnext101": 4, "5360": 4, "grid": [4, 5], "5357": 4, "onnx_export": 4, "5468": 4, "5478": 4, "web": 4, "5315": 4, "5264": 4, "diagon": 4, "tta": 4, "5403": 4, "5249": 4, "flexibl": [4, 5], "5218": 4, "5291": 4, "anchor_gener": [4, 15], "point_gener": 4, "5349": 4, "5389": 4, "delet": [4, 5, 22, 29], "5311": 4, "5370": 4, "friendli": [4, 12, 16], "5280": 4, "clarifi": [4, 6], "5316": 4, "5268": 4, "solut": [4, 8, 9], "5312": 4, "5313": 4, "5328": 4, "4602": 4, "long": [4, 5, 12, 19], "tail": [4, 12], "cvpr": [4, 12], "5128": 4, "invert": [4, 12], "residu": [4, 8], "5122": 4, "5143": 4, "cornernet": [4, 26], "5136": 4, "mask_soft": 4, "binari": [4, 8], "4615": 4, "pwc": 4, "5135": 4, "5172": 4, "cacscad": 4, "5221": 4, "iou_thr": [4, 15], "5195": 4, "drop": [4, 5, 19, 26], "5197": 4, "iter": [4, 5, 8, 11, 12, 15, 19, 28, 29], "5226": 4, "5205": 4, "4806": 4, "roi": [4, 5, 6, 9, 12, 15, 18], "extractor": [4, 15, 18], "5194": 4, "5181": 4, "5201": 4, "5229": 4, "101": [4, 8, 11, 13, 15], "4960": 4, "discard": 4, "min_bbox_s": [4, 15], "5011": 4, "duplic": 4, "5129": 4, "definit": [4, 8], "5180": 4, "5192": 4, "yolof": 4, "5039": 4, "go": [4, 5, 20, 28], "big": [4, 5], "mayb": [4, 5], "longer": [4, 5, 11], "inevit": [4, 5], "registri": [4, 18, 22], "newest": [4, 5], "basemodul": [4, 5, 24], "oper": [4, 5, 9, 20, 26, 29], "multiscaledeformableattn": [4, 5], "contain": [4, 5, 6, 8, 9, 11, 16, 19, 24, 27, 29], "mmdet": [4, 5, 8, 22], "ha": [4, 5, 6, 8, 9, 11, 16, 17, 19, 24, 26, 28], "known": [4, 5, 26], "therefor": [4, 5, 8, 16, 19, 22], "skip": [4, 5, 8, 9, 11, 26, 29], "though": [4, 5, 8, 26], "might": [4, 5, 8, 12, 19, 21], "most": [4, 5, 7, 15, 17], "4750": [4, 5], "modul": [4, 5, 6, 8, 9, 15, 17, 19, 22, 24], "manner": [4, 5, 16], "explicitli": [4, 5, 16, 24], "previous": [4, 5], "wa": [4, 5, 13], "ensur": [4, 9, 26], "pr": [4, 5, 12, 16, 19, 24], "accordingli": [4, 5], "5059": [4, 5], "easili": [4, 5, 15], "760": [4, 5], "4898": [4, 5], "small": [4, 5, 8, 19, 22], "medium": [4, 5, 8], "inde": 4, "pop": 4, "overal": [4, 5, 17], "similar": [4, 8, 29], "detectron2": [4, 5], "differenti": [4, 29], "4295": 4, "4778": 4, "iou": [4, 5, 15, 29], "bbox_overlap": [4, 9], "4889": 4, "4756": 4, "5052": 4, "fsaf": [4, 25, 26], "fco": [4, 9, 25, 26], "model_registri": 4, "4883": 4, "gt": [4, 5, 7, 8, 15, 16, 20, 29], "4928": 4, "test_robust": [4, 13, 29], "4917": 4, "mmpycocotool": [4, 5], "fulli": [4, 16], "4939": [4, 5], "serv": [4, 10, 11, 19, 21], "4954": 4, "4973": 4, "4763": 4, "fpg": 4, "5079": 4, "accur": [4, 5, 15, 29], "mean_ap": 4, "4875": 4, "4936": 4, "hang": 4, "vfnet": [4, 9], "gfl": 4, "place": [4, 8, 15], "reduce_mean": 4, "4923": 4, "4978": 4, "5058": 4, "asyncron": 4, "4941": 4, "dimension": 4, "unmatch": 4, "4982": 4, "randperm": 4, "whtn": 4, "5014": 4, "caraf": [4, 9], "5062": 4, "supplement_mask": 4, "zero": [4, 6, 8, 12], "5065": 4, "5081": 4, "pytorch2onnx": [4, 25, 26, 29], "4758": 4, "mainstream": 4, "4796": 4, "4699": 4, "dimens": 4, "4785": 4, "coder": [4, 15], "4721": 4, "ann_id": 4, "uniqu": 4, "4789": 4, "4716": 4, "grid_anchor": 4, "4684": 4, "4880": 4, "tridentnet": 4, "4717": 4, "fasf": 4, "4735": 4, "4732": 4, "4569": 4, "pyramid": [4, 12, 15], "4645": 4, "4420": 4, "4665": 4, "4570": 4, "4669": 4, "buffer": 4, "4582": 4, "gif": 4, "4573": 4, "configdict": 4, "4643": 4, "4631": 4, "4581": 4, "syntax": 4, "upgrad": [4, 29], "4584": 4, "4600": 4, "nm": [4, 5, 15, 26], "4636": 4, "rather": [4, 5, 8, 21], "string": [4, 16], "4619": 4, "doctest": 4, "target": [4, 5, 8, 15, 17, 18, 29], "4614": 4, "deep": [4, 8, 12], "4621": 4, "4642": 4, "4650": 4, "4620": 4, "4630": 4, "redund": 4, "import_modules_from_str": 4, "4601": 4, "4571": 4, "correctli": [4, 8, 9], "xmldataset": 4, "4555": 4, "4462": 4, "4526": 4, "4695": 4, "spars": 4, "train_cfg": [4, 10, 17, 23], "test_cfg": [4, 10, 23, 26], "4356": 4, "4219": 4, "4398": 4, "4452": 4, "4441": 4, "simplifi": [4, 26], "4468": 4, "4508": [4, 16], "4410": 4, "4409": 4, "typeerror": 4, "4411": 4, "background": [4, 5, 6, 16], "4391": 4, "destroi": 4, "4442": 4, "score_factor": 4, "decreas": 4, "4473": 4, "4516": 4, "4520": 4, "4382": 4, "4396": 4, "4405": 4, "4408": 4, "4430": 4, "switch": [4, 15], "matplotlib": 4, "4389": 4, "4400": 4, "4347": 4, "4489": 4, "reg_decoded_bbox": 4, "4467": 4, "4525": 4, "r101": 4, "4495": 4, "guidanc": 4, "4537": 4, "1900": 4, "3313": 4, "4367": 4, "factor": [4, 5, 15], "masktestmixin": [4, 18], "4366": 4, "4362": 4, "empir": 4, "resnext": [4, 8, 11, 15], "4300": 4, "4250": 4, "4287": 4, "4257": 4, "pafpn": [4, 15], "attribut": [4, 24, 27], "extra_convs_on_input": 4, "4235": 4, "url": [4, 9], "aw": [4, 29], "aliyun": [4, 11], "4349": 4, "atss": 4, "4359": 4, "4360": 4, "op": [4, 8, 9, 15, 25, 26], "4325": 4, "hungarian": 4, "4259": 4, "scipi": 4, "packag": [4, 5, 8, 9, 19], "4339": 4, "4326": 4, "subset": [4, 16], "4307": 4, "4303": 4, "doccument": 4, "4271": 4, "4264": 4, "kept": [4, 15, 26], "4251": 4, "gflhead": 4, "4210": 4, "4224": 4, "resnest": [4, 9], "dc5": 4, "4201": 4, "4206": 4, "3773": 4, "4175": 4, "4087": 4, "4083": 4, "2959": 4, "unclip": 4, "border": [4, 15, 22], "4076": 4, "tpfp": 4, "func": 4, "4069": 4, "4081": 4, "4043": 4, "4163": 4, "4032": 4, "4088": 4, "2_new_data_model": 4, "4041": 4, "offset": 4, "4198": 4, "4190": 4, "3818": 4, "seri": [4, 8, 9], "4176": 4, "4145": 4, "statist": [4, 8, 15], "titl": [4, 13, 29], "4140": 4, "neg": [4, 15, 17], "freeanchor": 4, "4082": 4, "expand": [4, 20, 29], "4089": 4, "4103": 4, "troubl": [4, 8, 10], "shoot": [4, 10], "page": [4, 9, 10, 12, 13], "resolv": 4, "fault": [4, 8], "4055": 4, "alrp": 4, "4078": 4, "4056": 4, "q": 4, "4045": 4, "varifocalnet": 4, "giou": 4, "bboxoverlaps2d": 4, "giou_loss": 4, "3936": 4, "3948": 4, "3666": 4, "4024": 4, "sabl": 4, "3913": 4, "divis": [4, 15], "num_po": 4, "3938": 4, "temporari": [4, 8], "4034": 4, "4017": 4, "4025": 4, "ga": 4, "3983": 4, "3947": 4, "core": [4, 15, 18, 19, 22, 29], "generate_inputs_and_wrap_model": 4, "3857": 4, "3912": 4, "3986": 4, "tensor2img": 4, "4010": 4, "4000": 4, "3994": 4, "3985": 4, "3966": 4, "roialign": [4, 5, 15, 26], "3930": 4, "4031": 4, "centripetalnet": [4, 9, 12], "3766": 4, "3822": 4, "force_fp32": 4, "auto_fp16": 4, "wrap_fp16_model": 4, "fp16optimizerhook": 4, "rais": [4, 8, 9], "attempt": 4, "foreground": [4, 6, 16], "3221": 4, "behavior": [4, 5, 6, 16, 19], "background_label": 4, "whose": [4, 5, 6, 15, 24], "softmax": [4, 5, 12], "get_subset_by_class": 4, "test_mod": [4, 16], "filter_empty_gt": [4, 16, 22], "3695": 4, "ambigu": 4, "mani": [4, 5, 8, 12, 15, 16, 19], "respons": [4, 29], "3844": 4, "3638": 4, "experi": [4, 15], "3764": 4, "shear": 4, "3656": 4, "3619": 4, "3687": 4, "constrast": 4, "bright": 4, "3643": 4, "3456": [4, 7], "3390": 4, "3905": 4, "ground": [4, 5, 8, 16], "truth": [4, 5, 8, 16], "3702": 4, "focal": [4, 12, 17], "browser": 4, "3708": 4, "stuck": 4, "posit": [4, 15, 17], "3713": 4, "rpn_head": [4, 15], "rpntestmixin": 4, "3808": 4, "conv2d": [4, 24], "3791": 4, "reppoint": [4, 5, 9, 15], "3836": 4, "3849": 4, "html": [4, 9], "3840": 4, "nonzero": 4, "3867": 4, "3883": 4, "bbox_flip": 4, "3886": 4, "liggl": 4, "3891": 4, "collect_env": [4, 8], "3779": 4, "3795": 4, "3778": 4, "3777": 4, "simple_test_bbox": 4, "3853": 4, "bool": 4, "3870": 4, "3821": 4, "lot": [4, 19, 29], "pypi": 4, "3564": 4, "3686": 4, "3705": 4, "influenc": [4, 16], "help": [4, 8, 15, 20, 25, 28, 29], "replace_imagetotensor": 4, "horizont": 4, "vertic": 4, "direct": 4, "3608": 4, "mmlvi": 4, "3727": 4, "uninstal": [4, 8, 26], "3491": 4, "3497": 4, "3516": 4, "3528": 4, "fuse_conv_bn": 4, "3529": 4, "gaussian_target": 4, "heatmap": 4, "3543": 4, "3553": 4, "3566": 4, "with_xxx_attribut": 4, "3567": 4, "3575": 4, "rfp": 4, "3591": 4, "fuse": [4, 11], "conv": [4, 5, 8, 11, 15, 18], "3606": 4, "webcam": 4, "3634": 4, "3658": 4, "3670": 4, "stride": [4, 8, 15], "3685": 4, "res2net": 4, "3714": 4, "ohemsampl": 4, "3677": 4, "cutout": 4, "3521": 4, "concatdataset": [4, 16], "3522": 4, "3547": 4, "3607": 4, "3083": 4, "3603": 4, "github": [4, 8, 9, 12, 15, 22], "action": 4, "3510": 4, "3641": 4, "3530": 4, "3532": 4, "3534": 4, "3537": 4, "init_ev": 4, "3550": 4, "include_bkg": 4, "classbalanceddataset": [4, 16], "3577": 4, "3611": 4, "3639": 4, "3665": 4, "3711": 4, "c": [4, 8, 9, 13, 16, 19], "warpper": 4, "diou": 4, "3187": 4, "nasfcoshead": 4, "3205": 4, "publish_model": [4, 29], "3237": 4, "inside_flag": 4, "3242": 4, "forget": 4, "3262": 4, "stem_channel": 4, "3333": 4, "3318": 4, "3316": 4, "topk": [4, 29], "smaller": [4, 8, 15, 21], "expect": [4, 8, 16, 29], "atssassign": 4, "3361": 4, "center_region_assign": 4, "3208": 4, "iou_loss": 4, "3394": 4, "actual": [4, 16, 17, 19], "3407": 4, "3435": 4, "incontigu": 4, "3412": 4, "3036": 4, "3151": 4, "3395": [4, 19], "3410": 4, "3075": 4, "3082": 4, "crop": [4, 5], "randomcrop": [4, 20], "3153": 4, "3155": 4, "3206": 4, "to_float32": 4, "norm_cfg": [4, 15, 18], "3210": 4, "beginn": 4, "3213": 4, "3273": 4, "3232": 4, "3457": 4, "3290": 4, "3320": 4, "3392": 4, "hash": [4, 29], "3466": 4, "3460": 4, "3176": 4, "3092": 4, "3161": 4, "na": [4, 9], "3145": 4, "3142": 4, "3119": 4, "rstrip": 4, "3106": 4, "ms_rcnn": 4, "3112": 4, "3056": 4, "2998": 4, "2999": 4, "3018": 4, "focalloss": [4, 15, 17, 18], "2964": 4, "pisa": 4, "2992": 4, "iou_calcul": 4, "2975": 4, "prevent": 4, "possibl": 4, "shallow": 4, "2967": 4, "3064": 4, "3097": 4, "2752": 4, "3040": 4, "deepfashion": 4, "2968": 4, "trick": [4, 19], "2935": 4, "2963": 4, "with_cp": [4, 8], "basicblock": 4, "2891": 4, "2954": 4, "free": [4, 8, 12, 19, 22], "2867": 4, "3137": 4, "3130": 4, "3125": 4, "3120": 4, "3060": 4, "concat": [4, 7, 16], "groi": 4, "3098": 4, "cmd": 4, "autorescal": 4, "3080": 4, "len": 4, "img_meta": [4, 6, 17, 18, 20], "num_sampl": 4, "3073": 4, "3053": 4, "2976": 4, "regnetx": 4, "nasfco": 4, "2651": 4, "cython": [4, 8], "2713": 4, "2754": 4, "2763": 4, "gcc": [4, 8], "2806": 4, "2820": 4, "encod": [4, 5, 11, 15], "decod": [4, 5, 12, 15], "2824": 4, "2858": 4, "2921": 4, "2944": 4, "comment": [4, 15], "2877": 4, "gt_bboxes_ignor": [4, 18, 20], "gt_label_ignor": 4, "gt_masks_ignor": 4, "miniourandomcrop": [4, 20], "2810": 4, "base_channel": 4, "2917": 4, "2936": 4, "2666": 4, "agnost": [4, 15], "2553": 4, "gather": [4, 15], "2676": 4, "2736": 4, "2767": 4, "2895": 4, "2779": 4, "2721": 4, "2237": 4, "2710": 4, "storag": 4, "2712": 4, "prime": 4, "2626": 4, "2682": 4, "2797": 4, "2088": 4, "2584": 4, "2629": 4, "2680": 4, "pure": [4, 11], "2657": 4, "2730": 4, "patch": 4, "2704": 4, "imgs_per_gpu": [4, 16], "2700": 4, "2645": 4, "2762": 4, "2866": 4, "2876": 4, "2879": 4, "2831": 4, "ori_filenam": 4, "2612": 4, "img_field": 4, "2800": 4, "upsample_cfg": 4, "2787": 4, "2809": 4, "mmlab": [4, 9, 15, 22], "resnet50_caff": [4, 11], "resnet50_caffe_bgr": 4, "detectron": [4, 10, 11], "2832": 4, "sleep": 4, "2847": 4, "c10": 4, "half": [4, 5], "2890": 4, "2918": 4, "2714": 4, "constructor": 4, "2947": 4, "made": [4, 8, 9, 13, 15], "major": [4, 5], "achiev": [4, 6, 8, 18, 19, 28], "hyperparamet": [4, 8, 15, 21], "lead": [4, 5, 8], "gain": 4, "bunch": 4, "smoothli": 4, "modular": [4, 5, 15], "design": [4, 5, 10, 15, 23, 29], "toward": [4, 12], "goal": [4, 18], "simplic": [4, 5], "encapsul": [4, 15], "while": [4, 5, 8, 15, 26, 29], "bboxcod": 4, "ioucalcul": 4, "optimizerconstructor": 4, "hierarchi": [4, 22], "simpler": 4, "part": [4, 8, 9, 16, 18, 21, 24], "cpp": 4, "2277": 4, "2216": 4, "1999": 4, "empericalattent": 4, "2345": 4, "2375": 4, "2380": 4, "2374": 4, "2419": 4, "2420": 4, "member": 4, "2422": 4, "2429": 4, "2480": 4, "2156": 4, "2474": 4, "2509": 4, "2502": 4, "2511": 4, "2569": 4, "2572": 4, "2524": 4, "f": [4, 9, 22, 29], "2531": 4, "aspect": [4, 16], "2039": 4, "max_num": [4, 5], "slice": 4, "2486": 4, "roipool": 4, "2490": 4, "2536": 4, "2578": 4, "deep_stem": [4, 22], "avg_down": 4, "resnetv1d": 4, "2252": 4, "l1": [4, 5, 15], "2376": 4, "bitmap": 4, "2353": 4, "2540": 4, "2385": 4, "complic": 4, "2397": 4, "2488": 4, "2392": 4, "2280": 4, "2408": 4, "2443": 4, "2340": 4, "2405": 4, "wise": [4, 17, 19], "last": [4, 18], "2459": 4, "2414": 4, "momentumupdaterhook": 4, "2571": 4, "2605": 4, "rewritten": 4, "mmddp": 4, "ddp": [4, 8], "store": [4, 16, 19, 27, 29], "numpi": [4, 5, 16], "arrai": [4, 9, 16], "h": [4, 16, 29], "w": [4, 16], "ignore_iof_thr": [4, 15], "pred": [4, 18], "2135": 4, "2116": 4, "pool": [4, 9], "2099": 4, "2103": 4, "uint8": 4, "2105": 4, "2098": 4, "2097": 4, "pad_val": [4, 22], "unus": [4, 5, 8], "2093": 4, "2032": 4, "2094": 4, "2090": 4, "2086": 4, "soft": [4, 12], "2056": 4, "2042": 4, "2087": 4, "2114": 4, "2128": 4, "2030": 4, "reassembli": 4, "1583": 4, "worker_init_fn": 4, "data_load": 4, "2066": 4, "2111": 4, "2035": 4, "mainli": [4, 18, 24], "onlin": [4, 16], "readthedoc": 4, "io": 4, "build_conv_lay": 4, "convmodul": 4, "2024": [4, 29], "1985": 4, "refine_bbox": 4, "1962": 4, "seg_prefix": [4, 16], "1906": 4, "1880": 4, "ga_shape_target_singl": 4, "1853": 4, "1819": 4, "1818": 4, "assignresult": 4, "samplingresult": 4, "1995": 4, "abil": 4, "overwrit": [4, 16, 17], "1982": 4, "imagecorrupt": [4, 13], "1969": 4, "ssdhead": 4, "1935": 4, "resn": 4, "conv_lay": 4, "1894": 4, "1889": 4, "construct": [4, 15, 17], "1865": 4, "segresizeflippadrescal": 4, "1852": 4, "init_dist": 4, "1851": 4, "1971": 4, "1938": 4, "1869": 4, "1838": 4, "1834": 4, "keep_all_stag": 4, "1806": 4, "crop_mask": 4, "rle_mask_encod": 4, "2013": 4, "grayscal": 4, "1975": 4, "bridg": [4, 12], "gap": [4, 26], "1872": 4, "1859": 4, "1864": 4, "gn": [4, 15, 29], "1850": 4, "env": 4, "info": [4, 15, 27, 28, 29], "1812": 4, "rc1": 4, "focus": 4, "foveabox": 4, "jupyt": [4, 9], "ap_": 4, "ap_m": 4, "ap_l": 4, "introduc": [4, 8, 16, 20, 26, 27, 28, 29], "1679": 4, "libra": 4, "1800": 4, "ssd300": 4, "wider": [4, 11, 21], "face": [4, 8, 11, 21], "1781": 4, "1730": 4, "1721": 4, "1492": 4, "1242": 4, "1108": 4, "1107": 4, "shuffl": 4, "build_dataload": 4, "1693": 4, "1688": 4, "corner": [4, 5], "1610": 4, "1404": 4, "1603": 4, "ghm": 4, "1578": 4, "1575": 4, "keyword": [4, 15, 19], "nms_cfg": 4, "1573": 4, "minioucrop": 4, "1550": 4, "1528": 4, "1498": 4, "1497": 4, "valid_flag": 4, "1478": 4, "1476": 4, "1390": 4, "coco_ev": 4, "1376": 4, "deformableconv": 4, "deformable_group": 4, "1359": 4, "deformconv": 4, "1326": 4, "1288": 4, "1255": 4, "inplac": 4, "1249": 4, "1196": 4, "roiextractor": 4, "1160": 4, "recurs": 4, "1099": 4, "lint": 4, "compil": [4, 8, 9], "travi": 4, "1715": 4, "1651": 4, "1615": 4, "1624": 4, "1614": 4, "1536": 4, "1475": 4, "erotem": 4, "1517": 4, "1506": 4, "1505": 4, "1491": 4, "1479": 4, "1477": 4, "1474": 4, "1399": 4, "1306": 4, "1284": 4, "1182": 4, "no_norm_on_later": 4, "1240": 4, "1235": 4, "separ": [4, 5, 13, 16], "1233": 4, "1158": 4, "1133": 4, "int64_t": 4, "kernel": [4, 8, 17], "1131": 4, "unsquar": 4, "1128": 4, "promot": 4, "1114": 4, "1093": 4, "scalar_typ": 4, "suppress": [4, 10, 12, 15, 23], "1070": 4, "with_ap": 4, "1549": 4, "1391": 4, "1354": 4, "beyond": 4, "1339": 4, "1273": 4, "1115": 4, "represent": [4, 12], "1251": 4, "1168": 4, "1155": 4, "counter": [4, 29], "arbitrari": [4, 13], "1078": 4, "compon": [4, 10, 15, 23, 24], "plu": 4, "collabor": 4, "adopt": [4, 5, 8, 15, 29], "hyper": [4, 10], "integr": 4, "nightli": [4, 8], "sigmoidfocalloss": 4, "convnet": 4, "author": [4, 13], "chengdazhi": 4, "group": [4, 8, 12, 13, 15], "retinahead": 4, "anchorhead": [4, 17], "singlestagedetector": 4, "bboxassign": 4, "bboxsampl": 4, "restructur": 4, "convfcroihead": 4, "sharedfcroihead": 4, "convfcbboxhead": 4, "sharedfcbboxhead": 4, "scatter": 5, "v1": [5, 7, 10, 11], "1621": 5, "fix": [5, 8], "bug": [5, 21, 28], "basetransformerlay": 5, "multiheadattent": [5, 24], "3": [5, 8, 9, 10, 11, 13, 15, 16, 22, 23, 24, 26, 27], "17": [5, 6, 8, 10, 11, 13, 16], "1418": 5, "attn_feat": 5, "tupl": [5, 8, 15, 16, 18, 22], "1120": 5, "5343": 5, "pr5291": 5, "refactor": [5, 16], "model_convert": [5, 29], "upgrade_ssd_vers": 5, "old_model_path": 5, "new_model_path": 5, "break": [5, 10, 29], "must": [5, 8, 9, 16], "real": [5, 12, 16], "apm": 5, "lower": [5, 11], "apl": 5, "goe": [5, 8, 28], "address": 5, "four": [5, 27], "fold": 5, "treat": 5, "center": 5, "left": 5, "top": [5, 12], "interpret": [5, 9], "natur": 5, "x1": [5, 16], "y1": [5, 16], "x2": 5, "y2": 5, "veri": [5, 8], "neglig": [5, 26], "quantiz": [5, 12], "minimum": [5, 9, 25, 26], "shift": [5, 9, 12, 20], "thei": [5, 8, 12, 17, 19, 26, 28], "sent": 5, "margin": 5, "hour": 5, "1x": [5, 11, 13, 15, 19], "paste_mask": 5, "improv": 5, "absolut": [5, 26, 29], "branch": [5, 8, 15], "num_categori": 5, "sigmoid": [5, 15], "max_iou_assign": [5, 15], "observ": 5, "sometim": [5, 15, 19], "perfect": 5, "slightli": [5, 11, 13], "inaccur": 5, "slight": 5, "maintain": [5, 11, 12], "gradual": 5, "grow": 5, "norm": [5, 15], "gcblock": 5, "illustr": [5, 15], "document": [5, 8, 15, 19, 22, 24, 28], "caff": [5, 8, 11, 15], "former": 5, "ones": 5, "bia": [5, 24], "except": [5, 19, 20, 21, 24, 26], "unexpect": 5, "2000": [5, 8, 11, 15], "nms_post": [5, 15], "smooth": [5, 15], "howev": [5, 9], "num": [5, 15, 17, 29], "degrad": 5, "stabil": [5, 8, 19], "warm": 5, "upgrade_model_vers": [5, 29], "less": [5, 8, 11, 21], "fork": 5, "due": [5, 8, 11, 13, 25], "favor": 5, "own": [6, 10, 12, 19], "bboxhead": [6, 18], "acc": 6, "propag": 6, "basedetector": 6, "train_step": 6, "special": 6, "deal": 6, "entir": [6, 10, 15], "cascaderoihead": 6, "bbox_result": [6, 18], "np": [6, 16], "num_img": 6, "mask_class": 6, "segm_result": 6, "els": [6, 8, 18, 22], "num_stag": [6, 15], "j": 6, "bbox_label": 6, "cls_score": [6, 18], "argmax": 6, "dim": 6, "refine_roi": 6, "bbox_pr": [6, 18], "refine_roi_list": 6, "few": [6, 8, 13], "stand": 6, "void": 6, "255": [6, 11], "seg": 6, "instance_id": 6, "instance_offset": 6, "feel": [8, 12, 19, 22], "enrich": 8, "solv": [8, 9, 26], "cover": [8, 16, 19], "fill": 8, "convw": 8, "regist": [7, 8, 15, 28], "assertionerror": 8, "incompat": 8, "master": [8, 15, 22, 29], "lt": 8, "25": [8, 10, 11, 15, 17, 26], "24": [8, 10, 15, 22], "23": [8, 9, 10, 11, 13], "22": [8, 10, 11, 13, 29], "21": [8, 10, 28], "20": [8, 10, 11, 13, 15, 16, 22, 29], "19": [8, 9, 10, 11, 29], "18": [8, 10, 11, 13, 22], "14": [8, 10, 11, 15], "15": [8, 10, 13, 29], "13": [8, 10, 23], "11": [8, 9, 10, 15, 23, 26, 29], "0rc0": [8, 10], "No": 8, "_ext": 8, "instruct": [8, 9, 17, 21], "albu": [8, 15], "u": [8, 9], "qudida": 8, "simultan": 8, "even": 8, "modulenotfounderror": 8, "extra": [7, 8, 15], "instaboostfast": 8, "panopticapi": 8, "reinstal": [8, 9], "practic": [8, 10, 12], "pythonpath": [8, 19], "dirnam": 8, "rtx": 8, "30": [8, 9, 10, 11, 16, 29], "card": 8, "around": 8, "mmcv_with_op": 8, "mmcv_cuda_arg": 8, "gencod": 8, "arch": [8, 22], "compute_80": 8, "sm_80": 8, "nvcc": 8, "fatal": 8, "unsupport": 8, "architectur": [8, 12, 15, 26], "compute_86": 8, "sm_86": 8, "nvidia": [8, 9, 11, 29], "toolkit": [8, 9], "tell": 8, "a100": [8, 9], "although": 8, "amper": [8, 9], "hurt": 8, "47585": 8, "abl": 8, "yet": 8, "execut": [8, 9, 15], "usr": 8, "conda": [8, 9], "cudatoolkit": [8, 9], "built": [8, 9, 12], "torch_cuda_arch_list": 8, "tabl": [8, 9, 11, 13, 25, 26, 29], "volta": 8, "happen": 8, "tesla": [8, 11], "k80": 8, "undefin": 8, "symbol": 8, "libcudart": 8, "glibcxx": 8, "cuda_hom": 8, "aten": 8, "th": 8, "setuptool": 8, "sandbox": 8, "unpickleableexcept": 8, "distutilssetuperror": 8, "element": [8, 16, 17, 18], "ext_modul": 8, "miniconda": [8, 9], "anaconda": 8, "3379": 8, "feedback": 8, "report": [8, 11, 29], "termin": [8, 28], "is_avail": 8, "man": 8, "ipdb": 8, "pdb": 8, "breakpoint": 8, "commonli": [8, 11, 24], "riski": 8, "reason": [8, 12, 26], "stabli": 8, "extend": [8, 10, 12, 23], "sensit": 8, "clippint": 8, "_delete_": [8, 15, 18, 19, 22], "max_norm": [8, 19], "35": [8, 11, 13, 19, 26], "norm_typ": [8, 19], "scenario": 8, "amount": 8, "gpu_assign_thr": 8, "sublinear": 8, "strategi": [8, 15, 17, 24], "loss_scal": 8, "further": 8, "finish": [8, 29], "prior": 8, "were": [8, 11, 28], "phenomenon": 8, "find_unused_paramet": 8, "down": 8, "turn": 8, "save_best": 8, "basi": 8, "expmomentumemahook": [8, 10, 23], "nor": 8, "resume_from": [8, 15], "restor": [8, 22], "yolox_s_8x8_300e_coco": 8, "epoch_x": 8, "reload": 8, "yolox_": 8, "49": [8, 13, 28], "9216": 8, "bottleneck": [8, 15], "stack": 8, "1x1": [8, 15], "3x3": [8, 15], "pyorch": 8, "conv1_strid": 8, "conv2_strid": 8, "come": [8, 11, 13, 29], "aggreg": 8, "cardin": 8, "control": [8, 17, 19, 26], "balanc": [8, 12, 17], "complex": [8, 9, 10, 16], "intern": 8, "basewidth": 8, "mask_rcnn_x101_64x4d_fpn_mstrain": 8, "poly_3x_coco": [8, 21], "x101": [8, 15], "64x4d": [8, 13], "norm_ev": [8, 15], "resolut": 8, "varianc": [8, 15], "batchnorm": [8, 19], "nasfpn": [8, 15], "rel": [8, 16, 20], "consid": 8, "syncbn": [8, 15], "prepar": [9, 20, 21, 25, 26, 27], "linux": [9, 13], "experienc": 9, "jump": 9, "activ": [9, 12], "cpuonli": 9, "highli": [9, 12, 17], "customiz": 9, "openmim": 9, "clone": 9, "cd": [7, 9, 27], "verbos": [9, 25], "edit": 9, "b": [9, 16, 19], "parti": 9, "yolov3_mobilenetv2_320_300e_coco": 9, "dest": 9, "yolov3_mobilenetv2_320_300e_coco_20210719_215349": 9, "d18dff72": 9, "car": [9, 16], "bench": 9, "cat": 9, "geforc": 9, "older": 9, "backward": 9, "offer": 9, "lightweight": [9, 29], "driver": 9, "satisfi": 9, "librari": [9, 13], "enough": 9, "hope": 9, "cu113": 9, "torch1": 9, "gone": 9, "syncbatchnorm": 9, "crisscrossattent": 9, "maskedconv2d": 9, "tempor": 9, "interlac": 9, "nms_cuda": 9, "sigmoid_focal_loss_cuda": 9, "cascaderpn": 9, "pip3": 9, "verif": [9, 26, 29], "__version__": 9, "within": 9, "exclam": 9, "mark": [9, 15, 20], "extern": 9, "03": [9, 10, 11, 16], "prefer": 9, "shm": 9, "8g": 9, "data_dir": 9, "view": 9, "prerequisit": 10, "verifi": [10, 25, 26, 29], "site": [], "tweak": [10, 23], "experiment": [10, 23], "mmdeploi": [10, 23], "deploi": [10, 23], "maximum": [10, 11, 15, 23, 25], "remind": [10, 23], "tensorrt": [10, 23, 26], "mmclassif": [10, 23], "freez": [10, 15, 23], "submiss": [10, 15, 23], "checkinvalidlosshook": [10, 23], "distevalhook": [10, 23], "linearmomentumemahook": [10, 23], "setepochinfohook": [10, 23], "syncnormhook": [10, 23], "yoloxlrupdaterhook": [10, 23], "yoloxmodeswitchhook": [10, 23], "fp": [10, 11], "miscellan": [10, 15], "changelog": 10, "29": [10, 28, 29], "2022": [10, 12, 28], "31": 10, "26": [10, 11, 13], "27": 10, "2021": [10, 12, 29], "02": [10, 15, 19], "04": [10, 25, 28], "2020": [10, 11, 12], "0rc1": 10, "2019": [10, 12, 13], "07": [10, 29], "v0": [10, 22], "6rc0": 10, "06": [10, 16], "2018": 10, "frequent": 10, "ask": 10, "search": [10, 12], "coco_2017_train": 11, "coco_2017_v": 11, "newli": [11, 19], "fair": 11, "max_memory_alloc": 11, "smi": 11, "averag": [11, 29], "open_mmlab": 11, "divid": [11, 17], "resnet50": [11, 15, 24], "resnet101": 11, "pycl": [11, 29], "530": 11, "280": 11, "msra": [11, 15], "resnet101_caff": 11, "caffe2": 11, "resnext101_32x8d": 11, "120": 11, "regnetx_3": 11, "2gf": 11, "regnetx_800mf": 11, "32x8d": 11, "fb": 11, "2g": 11, "These": [11, 15], "popular": 11, "framework": [11, 12], "mask_rcnn_r50_caffe_fpn_poly_1x_coco_v1": 11, "mask_rcnn_r_50_fpn_noaug_1x": 11, "yaml": 11, "throughput": 11, "100": [11, 15, 21, 26], "62": 11, "61": [11, 13, 29], "maskrcnn": [11, 15], "tensorpack": 11, "simpledet": 11, "39": [11, 26], "matterport": 11, "latenc": 11, "term": 11, "185c27e": 11, "v100": 11, "32g": 11, "intel": 11, "xeon": 11, "gold": 11, "6148": 11, "40ghz": 11, "cudnn": [11, 26], "schd": [11, 13], "37": [11, 13, 26, 29], "38": [11, 13, 26], "amp": 11, "36": [11, 13, 26], "measur": 11, "210": 11, "216": 11, "261": 11, "265": 11, "200": [11, 26], "205": 11, "parenthes": 11, "upon": 12, "As": [12, 17], "boundari": 12, "research": 12, "reveal": [12, 19], "otedetect": 12, "openvino": 12, "mmdetection3d": 12, "3d": [12, 19], "tier": 12, "confer": 12, "eccv": 12, "influenti": 12, "prune": 12, "cviu": 12, "involut": 12, "inher": 12, "recognit": 12, "cvpr21": 12, "generaliz": 12, "pedestrian": 12, "eleph": 12, "room": 12, "cvpr2021": 12, "fisher": 12, "compress": [12, 27], "icml2021": 12, "overcom": 12, "classifi": 12, "imbal": [12, 17], "cvpr2020": 12, "coher": 12, "reconstruct": 12, "human": 12, "d2det": 12, "pursu": 12, "keypoint": 12, "revisit": 12, "sibl": 12, "polarmask": 12, "shot": 12, "polar": 12, "hit": 12, "hierarch": 12, "triniti": 12, "zeroq": 12, "novel": 12, "cbnet": 12, "composit": 12, "aaai2020": 12, "rdsnet": 12, "reciproc": 12, "delv": 12, "neurip": 12, "cvpr2019": 12, "orient": 12, "aerial": [7, 12], "stronger": 12, "peppoint": 12, "iterdet": 12, "scheme": 12, "crowd": [12, 16], "localis": 12, "neurips2020": 12, "relationnet": 12, "reliabl": 12, "estim": 12, "vision": 12, "iccv2021": 12, "interact": 12, "neurips2021": 12, "teacher": 12, "cbnetv2": 12, "queri": 12, "autonom": [13, 29], "drive": [13, 29], "winter": [13, 29], "articl": 13, "michaelis2019wint": 13, "micha": 13, "claudio": 13, "mitzku": 13, "benjamin": 13, "geirho": 13, "robert": 13, "rusak": 13, "evgenia": 13, "bringmann": 13, "oliv": 13, "ecker": 13, "alexand": 13, "bethg": 13, "matthia": 13, "brendel": 13, "wieland": 13, "journal": 13, "arxiv": [13, 18], "1907": 13, "07484": 13, "year": 13, "visit": 13, "homepag": [7, 13], "perturb": 13, "iclr": 13, "dan": 13, "hendryck": 13, "thoma": 13, "dietterich": 13, "greyscal": 13, "motion": 13, "blur": 13, "snow": 13, "repositori": [13, 22], "analysis_tool": [13, 29], "nois": 13, "wetah": 13, "weather": 13, "digit": 13, "costom": 13, "gaussian": 13, "zoom": 13, "gaussian_nois": 13, "zoom_blur": 13, "chosen": 13, "increas": 13, "2017val": 13, "corr": 13, "54": 13, "32x4d": 13, "40": [13, 16, 22, 26, 29], "55": 13, "41": [13, 26], "56": [13, 28], "43": [13, 16], "34": [13, 26], "hybrid": 13, "vari": [13, 17, 21], "stochast": 13, "incorpor": 15, "conduct": 15, "variou": [15, 28], "wish": 15, "inspect": 15, "print_config": [15, 29], "job": 15, "chain": 15, "insid": 15, "loadimagefromwebcam": 15, "quotat": 15, "NO": 15, "white": 15, "space": 15, "primit": 15, "easi": 15, "understand": 15, "contributor": 15, "xxx_rcnn": 15, "advis": 15, "batch_per_gpu": 15, "yyi": [15, 26], "without_semant": 15, "moment": 15, "c4": 15, "norm_set": 15, "unless": 15, "dconv": 15, "gcb": 15, "mstrain": 15, "8x2": 15, "2x": 15, "20e": 15, "denot": 15, "decai": [15, 19], "16th": 15, "22th": 15, "19th": 15, "voc_0712": 15, "wider_fac": 15, "idea": [15, 29], "modern": 15, "brief": 15, "blob": [15, 22], "l308": 15, "depth": [15, 22, 29], "out_indic": [15, 22], "frozen_stag": [15, 22], "frozen": 15, "requires_grad": [15, 22], "gamma": [15, 17], "l10": 15, "garpnhead": 15, "l12": 15, "feat_channel": 15, "anchorgener": 15, "ssdanchorgener": 15, "base_s": 15, "taken": [15, 25, 26], "delta_xywh_bbox_cod": 15, "l9": 15, "l1loss": [15, 18], "smooth_l1_loss": 15, "l56": 15, "standardroihead": [15, 18], "standard_roi_head": [15, 18], "bbox_roi_extractor": [15, 18], "singleroiextractor": 15, "roi_extractor": 15, "single_level": 15, "roi_lay": 15, "deformroipoolingpack": 15, "modulateddeformroipoolingpack": 15, "roi_align": 15, "l79": 15, "output_s": 15, "sampling_ratio": 15, "featmap_strid": 15, "convfc_bbox_head": 15, "l177": 15, "fc": [15, 18], "mask_roi_extractor": [15, 18], "l21": 15, "maxiouassign": 15, "pos_iou_thr": 15, "neg_iou_thr": 15, "min_pos_i": 15, "minim": 15, "match_low_qu": 15, "iof": 15, "randomsampl": [15, 17], "random_sampl": 15, "l8": 15, "pos_fract": [15, 17], "neg_pos_ub": [15, 17], "upper": 15, "add_gt_as_propos": [15, 17], "allowed_bord": 15, "pos_weight": 15, "rpn_propos": 15, "nms_across_level": 15, "across": [15, 17], "naiv": 15, "nms_pre": [15, 26], "max_per_img": [15, 26], "iou_threshold": 15, "mask_siz": 15, "mask_thr_binari": 15, "poly2mask": 15, "largest": 15, "bundl": 15, "decid": [15, 16, 19], "test_pipelin": [15, 16, 20], "thought": 15, "worker": [15, 20], "fetch": 15, "l19": 15, "instances_train2017": [15, 22], "prefix": [15, 22], "instances_val2017": [15, 29], "eval_hook": 15, "l7": 15, "default_constructor": 15, "l13": 15, "lrupdat": 15, "cosineann": [15, 19], "cyclic": [15, 19], "lr_updat": 15, "exp": 15, "constant": [15, 24], "max_it": [15, 29], "checkpoint_config": [15, 19], "tensorboardloggerhook": [15, 19], "tensorboard": 15, "textloggerhook": [15, 19], "total_epoch": [15, 19], "hrnetv2_w32": 15, "stage1": 15, "num_modul": 15, "num_branch": 15, "num_block": 15, "num_channel": 15, "stage2": 15, "stage3": 15, "128": [15, 22], "stage4": 15, "worth": 15, "children": [15, 24], "again": 15, "640": [15, 16], "704": 15, "736": 15, "768": 15, "multiscale_mod": 15, "similarli": 15, "mmsyncbn": 15, "substitut": 15, "offlin": 16, "simplest": [16, 18], "coco_val2014_000000001268": 16, "427": 16, "1268": 16, "192": 16, "81": 16, "247": 16, "09": 16, "219": 16, "249": 16, "749": 16, "224": 16, "74": [16, 29], "73": 16, "33": [16, 26], "42986": 16, "my_custom_config": 16, "d": [7, 16, 27], "annotation_data": 16, "image_data": 16, "exactli": 16, "uncontinu": 16, "belong": 16, "ann": [16, 29], "least": 16, "difficult": 16, "bboxes_ignor": 16, "labels_ignor": 16, "1280": [16, 29], "ndarrai": 16, "int64": 16, "customdataset": 16, "load_annot": 16, "vocdataset": 16, "000001": 16, "60": 16, "000002": 16, "45": 16, "my_dataset": 16, "mydataset": 16, "person": 16, "bicycl": 16, "motorcycl": 16, "ann_list": 16, "list_from_fil": 16, "ann_lin": 16, "bbox_numb": 16, "astyp": 16, "dataset_a_train": 16, "image_list": 16, "suppos": [16, 22, 27], "dataset_a": 16, "frequenc": 16, "instanti": 16, "get_cat_id": 16, "oversample_thr": 16, "1e": [16, 19], "anno_file_1": 16, "anno_file_2": 16, "separate_ev": 16, "dataset_b_train": 16, "dataset_a_v": 16, "dataset_a_test": 16, "dataset_b_v": 16, "dataset_b": 16, "With": [16, 20], "undesir": 16, "themselv": [16, 26], "progress": 16, "000000001268": 16, "segments_info": 16, "8345037": 16, "51": 16, "outer": 16, "rectangl": 16, "24315": 16, "moreov": 16, "image_annotation_data": 16, "But": 17, "situat": 17, "elabor": 17, "categor": [17, 18, 20], "scalar": 17, "five": [17, 21], "mechan": 17, "ghmc": 17, "qualityfocalloss": 17, "fl": 17, "sniper": 17, "sai": 17, "sum": [17, 18], "multipli": 17, "entri": 17, "context": 17, "kind": 17, "label_weight": 17, "bbox_weight": 17, "get_target": 17, "atsshead": 17, "yield": 17, "anchor_list": 17, "valid_flag_list": 17, "gt_bboxes_list": 17, "gt_bboxes_ignore_list": 17, "gt_labels_list": 17, "label_channel": 17, "unmap_output": 17, "fcn": 18, "ghmloss": 18, "arg1": [18, 24], "arg2": [18, 24], "doubl": [18, 29], "double_bbox_head": 18, "doubleconvfcbboxhead": 18, "noqa": [18, 21], "w605": 18, "num_fc": 18, "conv_cfg": 18, "setdefault": 18, "with_avg_pool": 18, "super": [18, 24], "x_cl": 18, "x_reg": 18, "plan": 18, "doubleheadroihead": 18, "bbox2result": 18, "bbox2roi": 18, "build_assign": 18, "build_sampl": 18, "build_head": 18, "build_roi_extractor": 18, "base_roi_head": 18, "baseroihead": 18, "bboxtestmixin": 18, "init_assigner_sampl": 18, "init_bbox_head": 18, "init_mask_head": 18, "forward_train": 18, "proposal_list": 18, "_bbox_forward": 18, "_bbox_forward_train": 18, "sampling_result": 18, "_mask_forward_train": 18, "bbox_feat": 18, "_mask_forward": 18, "pos_ind": 18, "bbox_forward": 18, "double_roi_head": 18, "org": [7, 18, 27], "ab": 18, "1904": 18, "06493": 18, "reg_roi_scale_factor": 18, "bbox_cls_feat": 18, "num_input": 18, "bbox_reg_feat": 18, "roi_scale_factor": 18, "with_shared_head": 18, "shared_head": 18, "focu": 18, "myloss": 18, "my_loss": 18, "weighted_loss": 18, "numel": 18, "avg_factor": 18, "loss_xxx": 18, "adam": 19, "0003": 19, "myoptim": 19, "my_optim": 19, "namespac": 19, "program": 19, "a_valu": 19, "b_valu": 19, "c_valu": 19, "grain": 19, "build_from_cfg": [19, 24], "optimizer_build": 19, "myoptimizerconstructor": 19, "optimizer_cfg": 19, "paramwise_cfg": 19, "__call__": [19, 20], "converg": 19, "cycliclrupdat": 19, "cyclicmomentumupdat": 19, "target_ratio": 19, "cyclic_tim": 19, "step_ratio_up": 19, "momentum_config": 19, "85": 19, "95": 19, "steplrhook": 19, "power": 19, "min_lr": 19, "by_epoch": 19, "consineann": 19, "min_lr_ratio": 19, "phase": 19, "after_train_epoch": [19, 28], "after_val_epoch": [19, 28], "occas": 19, "myhook": 19, "before_run": [19, 28], "after_run": [19, 28], "before_epoch": 19, "after_epoch": 19, "before_it": 19, "after_it": 19, "my_hook": 19, "highest": [19, 29], "registr": [7, 19], "very_low": 19, "mention": 19, "checkpointhook": 19, "max_keep_ckpt": 19, "save_optim": 19, "wrap": 19, "mlflowloggerhook": 19, "typic": 20, "datacontain": 20, "decompos": 20, "sequenc": 20, "present": 20, "classic": 20, "figur": [20, 29], "blue": 20, "green": 20, "orang": 20, "ori_shap": 20, "bbox_field": 20, "mask_field": 20, "loadpropos": 20, "scale_idx": 20, "pad_shap": 20, "seg_field": 20, "pad_fixed_s": 20, "pad_size_divisor": 20, "segrescal": 20, "totensor": 20, "transpos": 20, "todatacontain": 20, "meta_kei": 20, "my_pipelin": 20, "mytransform": 20, "dummi": 20, "brows": [20, 29], "detial": 20, "useful_tool": 20, "kitti": 21, "discuss": 21, "burden": 21, "mask_rcnn_r50_fpn": 21, "mostli": 21, "reus": 21, "answer": 22, "meet": [22, 25, 26], "mmseg": [7, 22], "mobilenetv3": 22, "retinanet_r50_fpn": 22, "coco_detect": 22, "schedule_1x": 22, "trigger": 22, "mobilenet_v3": 22, "mobilenet_v3_smal": 22, "8427ecf0": 22, "48": 22, "b1": 22, "timm_exampl": 22, "retinanet_timm_efficientnet_b1_fpn_1x_coco": 22, "timmbackbon": 22, "model_nam": [22, 29], "efficientnet_b1": 22, "features_onli": 22, "112": [22, 29], "320": 22, "repo": 22, "principl": 22, "train_dataset": 22, "114": 22, "scaling_ratio_rang": 22, "enlarg": 22, "affin": 22, "faster_rcnn_r50_fpn": 22, "unfreezebackboneepochbasedhook": 22, "unfreeze_epoch": 22, "unfreeze_backbone_epoch_based_hook": 22, "parallel": 22, "is_module_wrapp": 22, "before_train_epoch": [22, 28], "stem": 22, "param": [22, 29], "norm1": 22, "conv1": [22, 24], "getattr": 22, "alon": 22, "pseudo": 22, "rand": 22, "level_output": 22, "level_out": 22, "benefici": 24, "model_cfg": 24, "parent": 24, "intial": 24, "basiclay": 24, "learnabl": 24, "deformconv2d": 24, "sub": 24, "deriv": 24, "foomodel": 24, "sequenti": 24, "modulelist": 24, "conv1d": 24, "feat": 24, "omit": [7, 24, 29], "anyth": 24, "780": 24, "onnxruntim": [25, 26], "trt": 25, "trt_file": 25, "input_image_path": [25, 26], "input_image_shap": 25, "min_image_shap": 25, "max_image_shap": 25, "workspac": 25, "workspace_s": 25, "engin": 25, "tmp": [25, 26], "trace": [25, 26], "400": 25, "600": 25, "gib": 25, "retinanet_r50_fpn_1x_coco": [25, 26], "deplopy": [25, 26], "guarante": [25, 26, 29], "ssd300_coco": [25, 26], "fsaf_r50_fpn_1x_coco": [25, 26], "fcos_r50_caffe_fpn_4x4_1x_coco": 25, "yolov3_d53_mstrain": [25, 26], "608_273e_coco": [25, 26], "cascade_rcnn_r50_fpn_1x_coco": [25, 26], "point_rend": [25, 26], "point_rend_r50_caffe_fpn_mstrain_1x_coco": [25, 26], "ubuntu": 25, "x86_64": 25, "gnu": 25, "cudnn8": 25, "care": [25, 26], "soon": [25, 26], "much": 25, "dig": [25, 26], "littl": [25, 26], "deeper": [25, 26], "yourself": [25, 26], "fast": [25, 26], "rsidetecion": [25, 26], "output_fil": 26, "image_shap": 26, "test_image_path": 26, "opset": 26, "opset_vers": 26, "cfg_option": [26, 29], "1216": 26, "postprocess": 26, "notic": 26, "608": [26, 29], "deploy_nms_pr": 26, "model_fil": 26, "format_onli": 26, "evaluation_metr": 26, "show_directori": 26, "show_score_threshold": 26, "evaluation_opt": 26, "onnx_backend": 26, "mmcvtensorrt": 26, "vice": 26, "versa": 26, "fcos_r50_caffe_fpn_gn": 26, "head_4x4_1x_coco": 26, "cornernet_hourglass104_mstest_10x5_210e_coco": 26, "detr_r50_8x2_150e_coco": 26, "preprocess": 26, "runnabl": 26, "cummax": 26, "huge": 26, "traceabl": 26, "img_shape_for_onnx": 26, "softnonmaxsuppress": 26, "max_output_boxes_per_class": 26, "bit": 26, "hopefulli": 26, "mkdir": [7, 27], "pv": 27, "wget": [7, 27], "image_info_test2017": 27, "panoptic_annotations_trainval2017": 27, "rm": [27, 29], "rf": 27, "isth": 27, "image_info_test": 27, "dev2017": 27, "gen_coco_panoptic_test_info": 27, "panoptic_image_info_test": 27, "slurm_test": 27, "maskformer_r50_mstrain_16x1_75e_coco": 27, "maskformer_r50_mstrain_16x1_75e_coco_20220221_141956": 27, "bc2699cb": 27, "algorithm_nam": 27, "mv": 27, "panoptic_test": 27, "dev2017_": 27, "_result": 27, "ur": 27, "virtual": 28, "swap": 28, "grasp": 28, "discov": 28, "memory_profil": 28, "psutil": 28, "250": 28, "gb": 28, "246360": 28, "mb": 28, "9407": 28, "5740": 28, "2452": 28, "consum": 28, "5434": 28, "881": 28, "available_memori": 28, "used_memori": 28, "memory_util": 28, "available_swap_memori": 28, "used_swap_memori": 28, "swap_memory_util": 28, "current_process_memori": 28, "insert": 28, "before_train_it": 28, "after_train_it": 28, "before_val_epoch": 28, "before_val_it": 28, "after_val_it": 28, "regularli": 28, "every_n_it": 28, "isfinit": 28, "becom": 28, "customize_runtim": 28, "apart": 29, "curv": 29, "seaborn": 29, "plot_curv": 29, "evaluation_interv": 29, "legend": 29, "log1": 29, "log2": 29, "bbox_map": 29, "run1": 29, "run2": 29, "cal_train_tim": 29, "outlier": 29, "analyz": 29, "some_exp": 29, "20190611_192040": 29, "slowest": 29, "fastest": 29, "1909": 29, "0028": 29, "1959": 29, "lowest": 29, "prediction_path": 29, "show_dir": 29, "show_score_thr": 29, "sort": 29, "skip_typ": 29, "output_dir": 29, "show_interv": 29, "later": 29, "netron": 29, "detvisgui": 29, "coco_error_analysi": 29, "criterion": 29, "out_dir": 29, "rsidet2torchserv": 29, "model_stor": 29, "p8080": 29, "8080": 29, "p8081": 29, "8081": 29, "p8082": 29, "8082": 29, "mount": 29, "bind": 29, "home": 29, "manag": 29, "curl": 29, "raw": 29, "githubusercont": 29, "3dog": 29, "127": 29, "class_nam": 29, "dog": 29, "294": 29, "63409423828125": 29, "203": 29, "99111938476562": 29, "417": 29, "048583984375": 29, "281": 29, "62744140625": 29, "9987992644309998": 29, "404": 29, "26019287109375": 29, "126": 29, "0080795288086": 29, "574": 29, "5091552734375": 29, "293": 29, "6662292480469": 29, "9979367256164551": 29, "197": 29, "2144775390625": 29, "93": 29, "3067855834961": 29, "307": 29, "8505554199219": 29, "276": 29, "7560119628906": 29, "993338406085968": 29, "test_torchserv": 29, "addr": 29, "inference_addr": 29, "yolov3_d53_320_273e_coco": 29, "421362b6": 29, "input_shap": 29, "239": 29, "gflop": 29, "technic": 29, "get_model_complexity_info": 29, "onnx_fil": 29, "in_fil": 29, "regnet2rsidet": 29, "src": 29, "dst": 29, "detectron2pytorch": 29, "upload": [7, 29], "input_filenam": 29, "output_filenam": 29, "faster_rcnn_r50_fpn_1x_20190801": 29, "data_convert": 29, "cityscapes_path": 29, "img_dir": [7, 29], "gt_dir": 29, "devkit_path": 29, "robustness_ev": 29, "robustness_benchmark": 29, "startup": 29, "nproc_per_nod": 29, "repeat": 29, "repeat_num": 29, "log_interv": 29, "launcher": 29, "certain": 29, "pkl_result": 29, "eval_opt": 29, "verbatim": 29, "optimize_anchor": 29, "darknet": 29, "anoth": 29, "evolut": 29, "differential_evolut": 29, "70": 29, "951": 29, "117266": 29, "15874": 29, "elaps": 29, "eta": 29, "753": 29, "849902": 29, "506055": 29, "489": 29, "386625": 29, "46": 29, "775": 29, "6133754253387451": 29, "776": 29, "59": 29, "89": 29, "154": 29, "198": 29, "349": 29, "336": 29, "798": 29, "anchor_optimize_result": 29, "summari": 29, "confusion_matrix": 29, "detection_result": 29, "save_dir": 29, "mmsegment": 7, "jpegimag": 7, "segmentationclass": 7, "imageset": 7, "voc2010": 7, "segmentationclasscontext": 7, "segmentationcontext": 7, "trainval_merg": 7, "vocaug": 7, "adechallengedata2016": 7, "coco_stuff10k": 7, "train2014": 7, "test2014": 7, "imageslist": 7, "coco_stuff164k": 7, "chase_db1": 7, "dark_zurich": 7, "gp": 7, "val_ref": 7, "licens": 7, "lists_file_nam": 7, "val_filenam": 7, "val_ref_filenam": 7, "rgb_anon": 7, "nighttimedrivingtest": 7, "gtcoarse_daytime_trainvaltest": 7, "night": 7, "ann_dir": 7, "labeltrainid": 7, "convert_dataset": 7, "besid": 7, "recent": 7, "exploit": 7, "voc_aug": 7, "pascal_context": 7, "calvin": 7, "ed": 7, "uk": 7, "wp": 7, "cocostuffdataset": 7, "cocostuff": 7, "2014": 7, "_labeltrainid": 7, "stuffthingmaps_trainval2017": 7, "2017": 7, "chasedb1": 7, "account": 7, "1st_manual": 7, "healthi": 7, "glaucoma": 7, "diabetic_retinopathi": 7, "healthy_manualsegm": 7, "glaucoma_manualsegm": 7, "diabetic_retinopathy_manualsegm": 7, "tar": 7, "ah": 7, "vk": 7, "googl": 7, "zenodo": 7, "5706578": 7, "urban": 7, "2d": 7, "contest": 7, "request": 7, "challeng": 7, "2_ortho_rgb": 7, "5_labels_all_noboundari": 7, "2016": 7, "isprs_semantic_labeling_vaihingen": 7, "isprs_semantic_labeling_vaihingen_ground_truth_eroded_complet": 7, "clip_siz": 7, "stride_s": 7, "344": 7, "398": 7, "dota": 7, "segmant": 7, "part1": 7, "part2": 7, "part3": 7, "semantic_mask": 7, "patch_width": 7, "patch_height": 7, "overlap_area": 7, "384": 7, "33978": 7, "11644": 7}, "objects": {}, "objtypes": {}, "objnames": {}, "titleterms": {"1": [0, 2, 4, 5, 15, 16, 17, 18, 19, 29], "infer": [0, 1, 2, 11, 13, 27], "train": [0, 1, 2, 5, 8, 11, 19, 21], "exist": [0, 16], "model": [0, 1, 2, 3, 5, 8, 10, 11, 13, 18, 21, 25, 26, 29], "standard": [0, 2, 11], "dataset": [0, 1, 2, 3, 6, 7, 10, 11, 13, 16, 21, 29], "high": 0, "level": 0, "api": [0, 3], "asynchron": 0, "interfac": 0, "support": [0, 16, 19, 25, 26], "python": 0, "3": [0, 2, 4, 17, 18, 19, 20, 29], "7": [0, 4, 21], "demo": 0, "imag": [0, 29], "webcam": 0, "video": 0, "gpu": 0, "acceler": 0, "test": [0, 1, 2, 13, 20, 27, 29], "prepar": [0, 1, 2, 7, 10, 29], "exampl": [0, 15, 16, 19], "without": [0, 9], "ground": 0, "truth": 0, "annot": [0, 1, 16], "batch": 0, "deprec": [0, 15], "imagetotensor": 0, "predefin": 0, "learn": [0, 15], "rate": 0, "automat": 0, "scale": 0, "singl": 0, "cpu": [0, 9], "multipl": 0, "launch": 0, "job": 0, "simultan": 0, "machin": 0, "manag": 0, "slurm": 0, "2": [1, 2, 4, 5, 16, 17, 18, 19, 29], "custom": [1, 2, 9, 16, 17, 18, 19, 20, 28], "coco": [1, 6, 7, 16, 27], "format": [1, 16, 20], "config": [1, 2, 15, 16, 18, 19, 21, 22, 29], "new": [1, 2, 4, 16, 18, 19, 22, 25, 26], "your": [2, 18, 25, 26], "own": 2, "defin": [2, 18, 19], "neck": [2, 3, 18], "e": [2, 18], "g": [2, 18], "augfpn": 2, "import": [2, 18], "modul": [2, 18], "modifi": [2, 15, 16, 18, 19, 21], "file": [2, 15, 16, 18, 19, 27], "rsidet": [3, 29], "core": 3, "anchor": [3, 11, 29], "bbox": 3, "export": [3, 25, 26], "mask": [3, 5, 11, 15], "evalu": [3, 5, 8, 19, 25, 26, 29], "post_process": 3, "util": 3, "pipelin": [3, 17, 20], "sampler": 3, "api_wrapp": 3, "detector": [3, 11], "backbon": [3, 18, 22], "dense_head": 3, "roi_head": 3, "loss": [3, 6, 11, 17, 18], "changelog": 4, "v2": [4, 11], "25": [4, 5], "29": 4, "2022": 4, "bug": 4, "fix": 4, "improv": 4, "document": [4, 10], "contributor": 4, "0": [4, 5], "31": 4, "5": [4, 17, 19], "highlight": 4, "backward": 4, "incompat": 4, "chang": 4, "featur": [4, 11], "24": 4, "26": 4, "4": [4, 18, 19, 29], "23": 4, "28": 4, "22": 4, "break": 4, "21": [4, 5], "8": [4, 26], "20": 4, "27": 4, "12": [4, 5, 27], "2021": 4, "19": 4, "14": [4, 5], "11": [4, 22], "18": [4, 5], "15": 4, "10": [4, 24], "refactor": 4, "17": 4, "9": [4, 25], "16": 4, "30": 4, "02": 4, "6": [4, 17], "13": [4, 28], "01": 4, "03": 4, "04": 4, "2020": 4, "v1": 4, "0rc1": 4, "2019": 4, "0rc0": 4, "07": 4, "v0": 4, "6rc0": 4, "06": 4, "2018": 4, "compat": 5, "mmdetect": [5, 9, 12, 29], "x": [5, 29], "mmcv": [5, 9, 19], "diihead": 5, "version": [5, 9], "ssd": [5, 11], "unifi": 5, "initi": [5, 24], "registri": [5, 19], "ap": 5, "coordin": 5, "system": 5, "codebas": 5, "convent": [5, 6], "hyperparamet": 5, "upgrad": 5, "from": [5, 11, 25, 26, 29], "pycocotool": 5, "empti": 6, "propos": [6, 11], "panopt": [6, 16, 27], "frequent": 8, "ask": 8, "question": 8, "instal": [8, 9], "code": 8, "pytorch": [8, 19, 26, 29], "cuda": [8, 9], "environ": [8, 11], "prerequisit": [9, 25, 26, 27], "best": 9, "practic": 9, "verifi": 9, "mim": 9, "onli": 9, "platform": 9, "googl": 9, "colab": 9, "us": [9, 10, 15, 16, 18, 19, 20, 21, 22, 28], "docker": [9, 29], "troubl": 9, "shoot": 9, "welcom": 10, "rsi": 10, "detect": [10, 29], "": 10, "get": [10, 22], "start": 10, "quick": 10, "run": [10, 29], "tutori": [10, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28], "tool": 10, "script": [10, 15], "note": 10, "indic": 10, "tabl": 10, "benchmark": [11, 13, 29], "zoo": [10, 11], "mirror": 11, "site": 11, "common": 11, "set": [11, 17, 19], "imagenet": 11, "pretrain": [11, 13], "baselin": 11, "rpn": 11, "faster": 11, "r": [11, 15], "cnn": [11, 15], "fast": 11, "pre": [11, 20, 21], "comput": [11, 17], "retinanet": 11, "cascad": 11, "hybrid": 11, "task": 11, "htc": 11, "group": 11, "normal": 11, "gn": 11, "weight": [11, 17, 24], "deform": 11, "convolut": 11, "caraf": 11, "content": 11, "awar": 11, "reassembli": 11, "instaboost": 11, "libra": 11, "guid": 11, "fco": 11, "foveabox": 11, "reppoint": 11, "freeanchor": 11, "grid": 11, "plu": 11, "ghm": 11, "gcnet": 11, "hrnet": 11, "score": 11, "scratch": 11, "na": 11, "fpn": 11, "atss": 11, "fsaf": 11, "regnetx": 11, "res2net": 11, "groie": 11, "dynam": 11, "pointrend": 11, "gener": 11, "focal": 11, "cornernet": 11, "yolov3": 11, "paa": 11, "sabl": 11, "centripetalnet": 11, "resnest": 11, "detr": 11, "autoassign": 11, "yolof": 11, "seesaw": 11, "centernet": 11, "yolox": 11, "pvt": 11, "solo": 11, "queryinst": 11, "panopticfpn": 11, "maskform": 11, "dyhead": 11, "mask2form": 11, "efficientnet": 11, "other": 11, "speed": 11, "comparison": 11, "detectron2": 11, "hardwar": 11, "softwar": 11, "perform": 11, "memori": 11, "project": 12, "base": [12, 15, 21], "an": [12, 15, 16], "extens": 12, "paper": 12, "corrupt": 13, "introduct": 13, "about": [13, 15], "result": [13, 26, 27, 28, 29], "modelzoo": 13, "href": 14, "http": 14, "rsidetect": 14, "readthedoc": 14, "io": 14, "en": 14, "latest": 14, "english": 14, "zh_cn": 14, "\u7b80\u4f53\u4e2d\u6587": 14, "through": [15, 22], "argument": [15, 26], "structur": 15, "name": 15, "style": 15, "train_cfg": 15, "test_cfg": 15, "faq": [15, 25, 26], "ignor": 15, "some": 15, "field": 15, "intermedi": 15, "variabl": 15, "data": [16, 20], "reorgan": 16, "check": 16, "middl": 16, "wrapper": 16, "repeat": 16, "class": 16, "balanc": 16, "concaten": 16, "sampl": 17, "method": 17, "step": 17, "tweak": 17, "hyper": [17, 29], "paramet": [17, 24, 26, 29], "wai": 17, "reduct": 17, "develop": 18, "compon": 18, "add": [18, 19], "mobilenet": 18, "pafpn": 18, "head": [18, 21], "runtim": 19, "optim": [19, 29], "self": 19, "implement": [19, 22, 28], "specifi": 19, "constructor": 19, "addit": 19, "schedul": [19, 21], "workflow": 19, "hook": [19, 28], "regist": 19, "numclasscheckhook": [19, 28], "default": 19, "checkpoint": 19, "log": [19, 29], "design": 20, "load": 20, "process": 20, "time": 20, "augment": [20, 22], "extend": 20, "finetun": 21, "inherit": 21, "how": [22, 25, 26, 28], "xxx": 22, "network": 22, "mmclassif": 22, "timm": 22, "mosaic": 22, "unfreez": 22, "after": 22, "freez": 22, "channel": 22, "descript": [24, 26], "usag": [24, 25, 26, 28], "init_cfg": 24, "onnx": [25, 26, 29], "tensorrt": 25, "experiment": [25, 26, 29], "try": [25, 26], "mmdeploi": [25, 26], "deploi": [25, 26], "convert": [25, 26, 29], "list": [25, 26], "remind": [25, 26], "all": 26, "The": 26, "non": 26, "maximum": 26, "suppress": 26, "submiss": 27, "segment": 27, "dev": 27, "renam": 27, "zip": 27, "checkinvalidlosshook": 28, "evalhook": 28, "distevalhook": 28, "expmomentumemahook": 28, "linearmomentumemahook": 28, "memoryprofilerhook": 28, "setepochinfohook": 28, "syncnormhook": 28, "syncrandomsizehook": 28, "yoloxlrupdaterhook": 28, "yoloxmodeswitchhook": 28, "analysi": 29, "visual": 29, "predict": 29, "error": 29, "serv": 29, "torchserv": 29, "build": 29, "deploy": 29, "complex": 29, "convers": 29, "regnet": 29, "detectron": 29, "resnet": 29, "publish": 29, "download": 29, "robust": 29, "fp": 29, "miscellan": 29, "metric": 29, "print": 29, "entir": 29, "yolo": 29, "confus": 29, "matrix": 29, "cityscap": 7, "pascal": 7, "voc": 7, "ade20k": 7, "context": 7, "stuff": 7, "10k": 7, "164k": 7, "chase": 7, "db1": 7, "drive": 7, "hrf": 7, "stare": 7, "dark": 7, "zurich": 7, "nighttim": 7, "loveda": 7, "ispr": 7, "potsdam": 7, "vaihingen": 7, "isaid": 7}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 6, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.viewcode": 1, "sphinx": 56}})