


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  <meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Changelog &mdash; RSI-Detection 2.25.1 documentation</title>
  

  <link rel="shortcut icon" href="_static/images/favicon.ico" />
  
  

  

  
  
  

  

  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!-- <link rel="stylesheet" href="_static/pygments.css" type="text/css" /> -->
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/copybutton.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/readthedocs.css" type="text/css" />
  <link rel="index" title="Index" href="genindex.html" />
  <link rel="search" title="Search" href="search.html" />
  <link rel="next" title="Frequently Asked Questions" href="faq.html" />
  <link rel="prev" title="Projects based on MMDetection" href="projects.html" />
  <!-- Google Analytics -->
  <script type="text/javascript">
    var collapsedSections = [];
  </script>
  
  <!-- End Google Analytics -->
  

  
  <script src="_static/js/modernizr.min.js"></script>
  <script>
    MathJax = {
        chtml: {
            scale: 1,
            minScale: 1,
        },
        svg: {
            scale: 1,
            minScale: 1,
        }
    }
</script>

  <!-- Preload the theme fonts -->

<link rel="preload" href="_static/fonts/FreightSans/freight-sans-book.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-Medium.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/FreightSans/freight-sans-medium-italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="_static/fonts/IBMPlexMono/IBMPlexMono-SemiBold.woff2" as="font" type="font/woff2" crossorigin="anonymous">

<!-- Preload the katex fonts -->

<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Math-Italic.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Main-Bold.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size1-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size4-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size2-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Size3-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.10.0/dist/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css"
    integrity="sha384-vSIIfh2YWi9wW0r9iZe7RJPrKwp6bG+s9QZMoITbCckVJqGCCRhc+ccxNcdpHuYu" crossorigin="anonymous">
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/"
        aria-label="OpenMMLab"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                About
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/open-mmlab/mmcv" target="_blank">
                  <span class="dropdown-title">MMCV </span>
                  <p>Foundational library for computer vision</p>
                </a>
              </div>
          </li>
          <li >
            <div id="resourcesDropdownButton" data-toggle="resources-dropdown" class="resources-dropdown">
              <a
                class="resource-option with-down-arrow">
                EarthNets
              </a>
              <div class="resources-dropdown-menu">
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/Dataset4EO" target="_blank">
                  <span class="dropdown-title">Dataset4EO </span>
                  <p>Re-organize remote sensing datasets.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Classification" target="_blank">
                  <span class="dropdown-title">RSI-Classification </span>
                  <p>Image/scene classification for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Segmentation" target="_blank">
                  <span class="dropdown-title">RSI-Segmentation </span>
                  <p>Pixel-wise semantic segmentation for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-Detection" target="_blank">
                  <span class="dropdown-title">RSI-Detection </span>
                  <p>Object detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-ChangeDetection" target="_blank">
                  <span class="dropdown-title">RSI-ChangeDetection </span>
                  <p>Change detection for RS images.</p>
                </a>
                <a class="doc-dropdown-option nav-dropdown-item"
                  href="https://github.com/EarthNets/RSI-SelfSupervised" target="_blank">
                  <span class="dropdown-title">RSI-SelfSupervised </span>
                  <p>Self-supervised learning for RS images.</p>
                </a>
              </div>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>
  </div>
</div>

<body class="pytorch-body">

   

  

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          

          
          
          
          

          



<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search Docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        
        
        
        
        
        
        <p class="caption" role="heading"><span class="caption-text">Get Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="get_started.html">Prerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="get_started.html#installation">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="model_zoo.html">Benchmark and Model Zoo</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Quick Run</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="1_exist_data_model.html">1: Inference and train with existing models and standard datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="2_new_data_model.html">2: Train with customized datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="3_exist_data_new_model.html">3: Train with customized models and standard datasets</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Tutorials</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorials/config.html">Tutorial 1: Learn about Configs</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_dataset.html">Tutorial 2: Customize Datasets</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/data_pipeline.html">Tutorial 3: Customize Data Pipelines</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_models.html">Tutorial 4: Customize Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_runtime.html">Tutorial 5: Customize Runtime Settings</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/customize_losses.html">Tutorial 6: Customize Losses</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/finetune.html">Tutorial 7: Finetuning Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/pytorch2onnx.html">Tutorial 8: Pytorch to ONNX (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/onnx2tensorrt.html">Tutorial 9: ONNX to TensorRT (Experimental)</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/init_cfg.html">Tutorial 10: Weight initialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/how_to.html">Tutorial 11: How to xxx</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/test_results_submission.html">Tutorial 12: Test Results Submission</a></li>
<li class="toctree-l1"><a class="reference internal" href="tutorials/useful_hooks.html">Tutorial 13: Useful Hooks</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Useful Tools and Scripts</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html">Log Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#result-analysis">Result Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#visualization">Visualization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#error-analysis">Error Analysis</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-serving">Model Serving</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-complexity">Model Complexity</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#model-conversion">Model conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-conversion">Dataset Conversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#dataset-download">Dataset Download</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#benchmark">Benchmark</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#miscellaneous">Miscellaneous</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#hyper-parameter-optimization">Hyper-parameter Optimization</a></li>
<li class="toctree-l1"><a class="reference internal" href="useful_tools.html#confusion-matrix">Confusion Matrix</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Notes</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="conventions.html">Conventions</a></li>
<li class="toctree-l1"><a class="reference internal" href="compatibility.html">Compatibility of MMDetection 2.x</a></li>
<li class="toctree-l1"><a class="reference internal" href="projects.html">Projects based on MMDetection</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">Frequently Asked Questions</a></li>
</ul>

        
        
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="pytorch-breadcrumbs">
    
      <li>
        <a href="index.html">
            Docs
        </a> &gt;
      </li>

        
      <li>Changelog</li>
    
    
      <li class="pytorch-breadcrumbs-aside">
        
            
            <a href="_sources/changelog.md.txt" rel="nofollow"><img src="_static/images/view-page-source-icon.svg"></a>
          
        
      </li>
    
  </ul>

  
</div>
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">
        
          <div class="rst-content">
            
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                
  <section id="changelog">
<h1>Changelog<a class="headerlink" href="#changelog" title="Permalink to this heading">¶</a></h1>
<section id="v2-25-1-29-7-2022">
<h2>v2.25.1 (29/7/2022)<a class="headerlink" href="#v2-25-1-29-7-2022" title="Permalink to this heading">¶</a></h2>
<section id="bug-fixes">
<h3>Bug Fixes<a class="headerlink" href="#bug-fixes" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix single GPU distributed training of cuda device specifying (#8176)</p></li>
<li><p>Fix PolygonMask bug in FilterAnnotations (#8136)</p></li>
<li><p>Fix mdformat version to support python3.6 (#8195)</p></li>
<li><p>Fix GPG key error in Dockerfile (#8215)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">WandbLoggerHook</span></code> error (#8273)</p></li>
<li><p>Fix Pytorch 1.10 incompatibility issues (#8439)</p></li>
</ul>
</section>
<section id="improvements">
<h3>Improvements<a class="headerlink" href="#improvements" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add <code class="docutils literal notranslate"><span class="pre">mim</span></code> to <code class="docutils literal notranslate"><span class="pre">extras_require</span></code> in setup.py (#8194)</p></li>
<li><p>Support get image shape on macOS (#8434)</p></li>
<li><p>Add test commands of <code class="docutils literal notranslate"><span class="pre">mim</span></code> in CI (#8230 &amp; #8240)</p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">maskformer</span></code> to be compatible when cfg is a dictionary (#8263)</p></li>
<li><p>Clean <code class="docutils literal notranslate"><span class="pre">Pillow</span></code> version check in CI (#8229)</p></li>
</ul>
</section>
<section id="documents">
<h3>Documents<a class="headerlink" href="#documents" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Change example hook name in tutorials (#8118)</p></li>
<li><p>Update projects (#8120)</p></li>
<li><p>Update metafile and release new models (#8294)</p></li>
<li><p>Add download link in tutorials (#8391)</p></li>
</ul>
</section>
<section id="contributors">
<h3>Contributors<a class="headerlink" href="#contributors" title="Permalink to this heading">¶</a></h3>
<p>A total of 15 developers contributed to this release.
Thanks &#64;ZwwWayne, &#64;ayulockin, &#64;Mxbonn, &#64;p-mishra1, &#64;Youth-Got, &#64;MiXaiLL76, &#64;chhluo, &#64;jbwang1997, &#64;atinfinity, &#64;shinya7y, &#64;duanzhihua, &#64;STLAND-admin, &#64;BIGWangYuDong, &#64;grimoire, &#64;xiaoyuan0203</p>
</section>
</section>
<section id="v2-25-0-31-5-2022">
<h2>v2.25.0 (31/5/2022)<a class="headerlink" href="#v2-25-0-31-5-2022" title="Permalink to this heading">¶</a></h2>
<section id="highlights">
<h3>Highlights<a class="headerlink" href="#highlights" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support dedicated <code class="docutils literal notranslate"><span class="pre">WandbLogger</span></code> hook</p></li>
<li><p>Support <span class="xref myst">ConvNeXt</span>, <span class="xref myst">DDOD</span>, <span class="xref myst">SOLOv2</span></p></li>
<li><p>Support <span class="xref myst">Mask2Former</span> for instance segmentation</p></li>
<li><p>Rename <span class="xref myst">config files of Mask2Former</span></p></li>
</ul>
</section>
<section id="backwards-incompatible-changes">
<h3>Backwards incompatible changes<a class="headerlink" href="#backwards-incompatible-changes" title="Permalink to this heading">¶</a></h3>
<ul>
<li><p>Rename <span class="xref myst">config files of Mask2Former</span> (#7571)</p>
<table align="center">
  <thead>
      <tr align='center'>
          <td>before v2.25.0</td>
          <td>after v2.25.0</td>
      </tr>
  </thead>
  <tbody><tr valign='top'>
  <th>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">mask2former_xxx_coco.py</span></code> represents config files for <strong>panoptic segmentation</strong>.</p></li>
</ul>
</th>
  <th>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">mask2former_xxx_coco.py</span></code> represents config files for <strong>instance segmentation</strong>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">mask2former_xxx_coco-panoptic.py</span></code> represents config files for <strong>panoptic segmentation</strong>.</p></li>
</ul>
</th></tr>
</tbody></table>
</li>
</ul>
</section>
<section id="new-features">
<h3>New Features<a class="headerlink" href="#new-features" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2201.03545">ConvNeXt</a> (#7281)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2107.02963">DDOD</a> (#7279)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2003.10152">SOLOv2</a> (#7441)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2112.01527">Mask2Former</a> for instance segmentation (#7571, #8032)</p></li>
</ul>
</section>
<section id="id1">
<h3>Bug Fixes<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Enable YOLOX training on different devices (#7912)</p></li>
<li><p>Fix the log plot error when evaluation with <code class="docutils literal notranslate"><span class="pre">interval</span> <span class="pre">!=</span> <span class="pre">1</span></code> (#7784)</p></li>
<li><p>Fix RuntimeError of HTC (#8083)</p></li>
</ul>
</section>
<section id="id2">
<h3>Improvements<a class="headerlink" href="#id2" title="Permalink to this heading">¶</a></h3>
<ul>
<li><p>Support dedicated <code class="docutils literal notranslate"><span class="pre">WandbLogger</span></code> hook (#7459)</p>
<p>Users can set</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">cfg</span><span class="o">.</span><span class="n">log_config</span><span class="o">.</span><span class="n">hooks</span> <span class="o">=</span> <span class="p">[</span>
  <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MMDetWandbHook&#39;</span><span class="p">,</span>
       <span class="n">init_kwargs</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;project&#39;</span><span class="p">:</span> <span class="s1">&#39;MMDetection-tutorial&#39;</span><span class="p">},</span>
       <span class="n">interval</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
       <span class="n">log_checkpoint</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
       <span class="n">log_checkpoint_metadata</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span>
       <span class="n">num_eval_images</span><span class="o">=</span><span class="mi">10</span><span class="p">)]</span>
</pre></div>
</div>
<p>in the config to use <code class="docutils literal notranslate"><span class="pre">MMDetWandbHook</span></code>. Example can be found in this <a class="reference external" href="https://colab.research.google.com/drive/1RCSXHZwDZvakFh3eo9RuNrJbCGqD0dru?usp=sharing#scrollTo=WTEdPDRaBz2C">colab tutorial</a></p>
</li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">AvoidOOM</span></code> to avoid OOM (#7434, #8091)</p>
<p>Try to use <code class="docutils literal notranslate"><span class="pre">AvoidCUDAOOM</span></code> to avoid GPU out of memory. It will first retry after calling <code class="docutils literal notranslate"><span class="pre">torch.cuda.empty_cache()</span></code>. If it still fails, it will then retry by converting the type of inputs to FP16 format. If it still fails, it will try to copy inputs from GPUs to CPUs to continue computing. Try AvoidOOM in code to make the code continue to run when GPU memory runs out:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">rsidet.utils</span> <span class="kn">import</span> <span class="n">AvoidCUDAOOM</span>

<span class="n">output</span> <span class="o">=</span> <span class="n">AvoidCUDAOOM</span><span class="o">.</span><span class="n">retry_if_cuda_oom</span><span class="p">(</span><span class="n">some_function</span><span class="p">)(</span><span class="n">input1</span><span class="p">,</span> <span class="n">input2</span><span class="p">)</span>
</pre></div>
</div>
<p>Users can also try <code class="docutils literal notranslate"><span class="pre">AvoidCUDAOOM</span></code> as a decorator to make the code continue to run when GPU memory runs out:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">rsidet.utils</span> <span class="kn">import</span> <span class="n">AvoidCUDAOOM</span>

<span class="nd">@AvoidCUDAOOM.retry_if_cuda_oom</span>
<span class="k">def</span> <span class="nf">function</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="o">...</span>
    <span class="k">return</span> <span class="n">xxx</span>
</pre></div>
</div>
</li>
<li><p>Support reading <code class="docutils literal notranslate"><span class="pre">gpu_collect</span></code> from <code class="docutils literal notranslate"><span class="pre">cfg.evaluation.gpu_collect</span></code> (#7672)</p></li>
<li><p>Speedup the Video Inference by Accelerating data-loading Stage (#7832)</p></li>
<li><p>Support replacing the <code class="docutils literal notranslate"><span class="pre">${key}</span></code> with the value of <code class="docutils literal notranslate"><span class="pre">cfg.key</span></code> (#7492)</p></li>
<li><p>Accelerate result analysis in <code class="docutils literal notranslate"><span class="pre">analyze_result.py</span></code>. The evaluation time is speedup by 10 ~ 15 times and only tasks 10 ~ 15 minutes now. (#7891)</p></li>
<li><p>Support to set <code class="docutils literal notranslate"><span class="pre">block_dilations</span></code> in <code class="docutils literal notranslate"><span class="pre">DilatedEncoder</span></code> (#7812)</p></li>
<li><p>Support panoptic segmentation result analysis (#7922)</p></li>
<li><p>Release DyHead with Swin-Large backbone (#7733)</p></li>
<li><p>Documentations updating and adding</p>
<ul class="simple">
<li><p>Fix wrong default type of <code class="docutils literal notranslate"><span class="pre">act_cfg</span></code> in <code class="docutils literal notranslate"><span class="pre">SwinTransformer</span></code> (#7794)</p></li>
<li><p>Fix text errors in the tutorials (#7959)</p></li>
<li><p>Rewrite the <span class="xref myst">installation guide</span> (#7897)</p></li>
<li><p><span class="xref myst">Useful hooks</span> (#7810)</p></li>
<li><p>Fix heading anchor in documentation  (#8006)</p></li>
<li><p>Replace <code class="docutils literal notranslate"><span class="pre">markdownlint</span></code> with <code class="docutils literal notranslate"><span class="pre">mdformat</span></code> for avoiding installing ruby (#8009)</p></li>
</ul>
</li>
</ul>
</section>
<section id="id3">
<h3>Contributors<a class="headerlink" href="#id3" title="Permalink to this heading">¶</a></h3>
<p>A total of 20 developers contributed to this release.</p>
<p>Thanks &#64;ZwwWayne, &#64;DarthThomas, &#64;solyaH, &#64;LutingWang, &#64;chenxinfeng4, &#64;Czm369, &#64;Chenastron, &#64;chhluo, &#64;austinmw, &#64;Shanyaliux &#64;hellock, &#64;Y-M-Y, &#64;jbwang1997, &#64;hhaAndroid, &#64;Irvingao, &#64;zhanggefan, &#64;BIGWangYuDong, &#64;Keiku, &#64;PeterVennerstrom, &#64;ayulockin</p>
</section>
</section>
<section id="v2-24-0-26-4-2022">
<h2>v2.24.0 (26/4/2022)<a class="headerlink" href="#v2-24-0-26-4-2022" title="Permalink to this heading">¶</a></h2>
<section id="id4">
<h3>Highlights<a class="headerlink" href="#id4" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2012.07177">Simple Copy-Paste is a Strong Data Augmentation Method for Instance Segmentation</a></p></li>
<li><p>Support automatically scaling LR according to GPU number and samples per GPU</p></li>
<li><p>Support Class Aware Sampler that improves performance on OpenImages Dataset</p></li>
</ul>
</section>
<section id="id5">
<h3>New Features<a class="headerlink" href="#id5" title="Permalink to this heading">¶</a></h3>
<ul>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2012.07177">Simple Copy-Paste is a Strong Data Augmentation Method for Instance Segmentation</a>, see <span class="xref myst">example configs</span> (#7501)</p></li>
<li><p>Support Class Aware Sampler, users can set</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">data</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">train_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">class_aware_sampler</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">num_sample_class</span><span class="o">=</span><span class="mi">1</span><span class="p">))))</span>
</pre></div>
</div>
<p>in the config to use <code class="docutils literal notranslate"><span class="pre">ClassAwareSampler</span></code>. Examples can be found in <a class="reference external" href="https://github.com/open-mmlab/rsidetection/tree/master/configs/openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages.py">the configs of OpenImages Dataset</a>.  (#7436)</p>
</li>
<li><p>Support automatically scaling LR according to GPU number and samples per GPU. (#7482)
In each config, there is a corresponding config of auto-scaling LR as below,</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">auto_scale_lr</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="n">enable</span><span class="o">=</span><span class="bp">True</span><span class="p">,</span> <span class="n">base_batch_size</span><span class="o">=</span><span class="n">N</span><span class="p">)</span>
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">N</span></code> is the batch size used for the current learning rate in the config (also equals to <code class="docutils literal notranslate"><span class="pre">samples_per_gpu</span></code> * gpu number to train this config).
By default, we set <code class="docutils literal notranslate"><span class="pre">enable=False</span></code> so that the original usages will not be affected. Users can set <code class="docutils literal notranslate"><span class="pre">enable=True</span></code> in each config or add <code class="docutils literal notranslate"><span class="pre">--auto-scale-lr</span></code> after the command line to enable this feature and should check the correctness of <code class="docutils literal notranslate"><span class="pre">base_batch_size</span></code> in customized configs.</p>
</li>
<li><p>Support setting dataloader arguments in config and add functions to handle config compatibility. (#7668)
The comparison between the old and new usages is as below.</p>
<table align="center">
  <thead>
      <tr align='center'>
          <td>v2.23.0</td>
          <td>v2.24.0</td>
      </tr>
  </thead>
  <tbody><tr valign='top'>
  <th>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">64</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
<span class="p">)</span>
</pre></div>
</div>
</th>
  <th>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># A recommended config that is clear</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="c1"># Use different batch size during inference.</span>
    <span class="n">train_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">64</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">4</span><span class="p">),</span>
    <span class="n">val_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">),</span>
    <span class="n">test_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">),</span>
<span class="p">)</span>

<span class="c1"># Old style still works but allows to set more arguments about data loaders</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span>
    <span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">64</span><span class="p">,</span>  <span class="c1"># only works for train_dataloader</span>
    <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>  <span class="c1"># only works for train_dataloader</span>
    <span class="n">train</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">val</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="n">test</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;xxx&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">),</span>
    <span class="c1"># Use different batch size during inference.</span>
    <span class="n">val_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">),</span>
    <span class="n">test_dataloader</span><span class="o">=</span><span class="nb">dict</span><span class="p">(</span><span class="n">samples_per_gpu</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span> <span class="n">workers_per_gpu</span><span class="o">=</span><span class="mi">2</span><span class="p">),</span>
<span class="p">)</span>
</pre></div>
</div>
</th></tr>
</tbody></table>
</li>
<li><p>Support memory profile hook. Users can use it to monitor the memory usages during training as below (#7560)</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">custom_hooks</span> <span class="o">=</span> <span class="p">[</span>
    <span class="nb">dict</span><span class="p">(</span><span class="nb">type</span><span class="o">=</span><span class="s1">&#39;MemoryProfilerHook&#39;</span><span class="p">,</span> <span class="n">interval</span><span class="o">=</span><span class="mi">50</span><span class="p">)</span>
<span class="p">]</span>
</pre></div>
</div>
</li>
<li><p>Support to run on PyTorch with MLU chip (#7578)</p></li>
<li><p>Support re-spliting data batch with tag (#7641)</p></li>
<li><p>Support the <code class="docutils literal notranslate"><span class="pre">DiceCost</span></code> used by <a class="reference external" href="https://arxiv.org/abs/2106.14855">K-Net</a> in <code class="docutils literal notranslate"><span class="pre">MaskHungarianAssigner</span></code> (#7716)</p></li>
<li><p>Support splitting COCO data for Semi-supervised object detection (#7431)</p></li>
<li><p>Support Pathlib for Config.fromfile (#7685)</p></li>
<li><p>Support to use file client in OpenImages dataset (#7433)</p></li>
<li><p>Add a probability parameter to Mosaic transformation (#7371)</p></li>
<li><p>Support specifying interpolation mode in <code class="docutils literal notranslate"><span class="pre">Resize</span></code> pipeline (#7585)</p></li>
</ul>
</section>
<section id="id6">
<h3>Bug Fixes<a class="headerlink" href="#id6" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Avoid invalid bbox after deform_sampling (#7567)</p></li>
<li><p>Fix the issue that argument color_theme does not take effect when exporting confusion matrix (#7701)</p></li>
<li><p>Fix the <code class="docutils literal notranslate"><span class="pre">end_level</span></code> in Necks, which should be the index of the end input backbone level (#7502)</p></li>
<li><p>Fix the bug that <code class="docutils literal notranslate"><span class="pre">mix_results</span></code> may be None in <code class="docutils literal notranslate"><span class="pre">MultiImageMixDataset</span></code> (#7530)</p></li>
<li><p>Fix the bug in ResNet plugin when two plugins are used (#7797)</p></li>
</ul>
</section>
<section id="id7">
<h3>Improvements<a class="headerlink" href="#id7" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Enhance <code class="docutils literal notranslate"><span class="pre">load_json_logs</span></code> of analyze_logs.py for resumed training logs (#7732)</p></li>
<li><p>Add argument <code class="docutils literal notranslate"><span class="pre">out_file</span></code> in image_demo.py (#7676)</p></li>
<li><p>Allow mixed precision training with <code class="docutils literal notranslate"><span class="pre">SimOTAAssigner</span></code> (#7516)</p></li>
<li><p>Updated INF to 100000.0 to be the same as that in the official YOLOX (#7778)</p></li>
<li><p>Add documentations of:</p>
<ul>
<li><p>how to get channels of a new backbone (#7642)</p></li>
<li><p>how to unfreeze the backbone network (#7570)</p></li>
<li><p>how to train fast_rcnn model (#7549)</p></li>
<li><p>proposals in Deformable DETR (#7690)</p></li>
<li><p>from-scratch install script in get_started.md (#7575)</p></li>
</ul>
</li>
<li><p>Release pre-trained models of</p>
<ul>
<li><p><span class="xref myst">Mask2Former</span> (#7595, #7709)</p></li>
<li><p>RetinaNet with ResNet-18 and release models (#7387)</p></li>
<li><p>RetinaNet with EfficientNet backbone (#7646)</p></li>
</ul>
</li>
</ul>
</section>
<section id="id8">
<h3>Contributors<a class="headerlink" href="#id8" title="Permalink to this heading">¶</a></h3>
<p>A total of 27 developers contributed to this release.
Thanks &#64;jovialio, &#64;zhangsanfeng2022, &#64;HarryZJ, &#64;jamiechoi1995, &#64;nestiank, &#64;PeterH0323, &#64;RangeKing, &#64;Y-M-Y, &#64;mattcasey02, &#64;weiji14, &#64;Yulv-git, &#64;xiefeifeihu, &#64;FANG-MING, &#64;meng976537406, &#64;nijkah, &#64;sudz123, &#64;CCODING04, &#64;SheffieldCao, &#64;Czm369, &#64;BIGWangYuDong, &#64;zytx121, &#64;jbwang1997, &#64;chhluo, &#64;jshilong, &#64;RangiLyu, &#64;hhaAndroid, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-23-0-28-3-2022">
<h2>v2.23.0 (28/3/2022)<a class="headerlink" href="#v2-23-0-28-3-2022" title="Permalink to this heading">¶</a></h2>
<section id="id9">
<h3>Highlights<a class="headerlink" href="#id9" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support Mask2Former: <a class="reference external" href="https://arxiv.org/abs/2112.01527">Masked-attention Mask Transformer for Universal Image Segmentation</a></p></li>
<li><p>Support EfficientNet: <a class="reference external" href="https://arxiv.org/abs/1905.11946">EfficientNet: Rethinking Model Scaling for Convolutional Neural Networks</a></p></li>
<li><p>Support setting data root through environment variable <code class="docutils literal notranslate"><span class="pre">MMDET_DATASETS</span></code>, users don’t have to modify the corresponding path in config files anymore.</p></li>
<li><p>Find a good recipe for fine-tuning high precision ResNet backbone pre-trained by Torchvision.</p></li>
</ul>
</section>
<section id="id10">
<h3>New Features<a class="headerlink" href="#id10" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <span class="xref myst">Mask2Former</span>(#6938)(#7466)(#7471)</p></li>
<li><p>Support <span class="xref myst">EfficientNet</span> (#7514)</p></li>
<li><p>Support setting data root through environment variable <code class="docutils literal notranslate"><span class="pre">MMDET_DATASETS</span></code>, users don’t have to modify the corresponding path in config files anymore. (#7386)</p></li>
<li><p>Support setting different seeds to different ranks (#7432)</p></li>
<li><p>Update the <code class="docutils literal notranslate"><span class="pre">dist_train.sh</span></code> so that the script can be used to support launching multi-node training on machines without slurm (#7415)</p></li>
<li><p>Find a good recipe for fine-tuning high precision ResNet backbone pre-trained by Torchvision (#7489)</p></li>
</ul>
</section>
<section id="id11">
<h3>Bug Fixes<a class="headerlink" href="#id11" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix bug in VOC unit test which removes the data directory (#7270)</p></li>
<li><p>Adjust the order of <code class="docutils literal notranslate"><span class="pre">get_classes</span></code> and <code class="docutils literal notranslate"><span class="pre">FileClient</span></code> (#7276)</p></li>
<li><p>Force the inputs of <code class="docutils literal notranslate"><span class="pre">get_bboxes</span></code> in yolox_head to float32 (#7324)</p></li>
<li><p>Fix misplaced arguments in LoadPanopticAnnotations (#7388)</p></li>
<li><p>Fix reduction=mean in CELoss. (#7449)</p></li>
<li><p>Update unit test of CrossEntropyCost (#7537)</p></li>
<li><p>Fix memory leaking in panpotic segmentation evaluation (#7538)</p></li>
<li><p>Fix the bug of shape broadcast in YOLOv3 (#7551)</p></li>
</ul>
</section>
<section id="id12">
<h3>Improvements<a class="headerlink" href="#id12" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add Chinese version of onnx2tensorrt.md (#7219)</p></li>
<li><p>Update colab tutorials (#7310)</p></li>
<li><p>Update information about Localization Distillation (#7350)</p></li>
<li><p>Add Chinese version of <code class="docutils literal notranslate"><span class="pre">finetune.md</span></code> (#7178)</p></li>
<li><p>Update YOLOX log for non square input (#7235)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">nproc</span></code> in <code class="docutils literal notranslate"><span class="pre">coco_panoptic.py</span></code> for panoptic quality computing (#7315)</p></li>
<li><p>Allow to set channel_order in LoadImageFromFile (#7258)</p></li>
<li><p>Take point sample related functions out of mask_point_head (#7353)</p></li>
<li><p>Add instance evaluation for coco_panoptic (#7313)</p></li>
<li><p>Enhance the robustness of analyze_logs.py (#7407)</p></li>
<li><p>Supplementary notes of sync_random_seed (#7440)</p></li>
<li><p>Update docstring of cross entropy loss (#7472)</p></li>
<li><p>Update pascal voc result (#7503)</p></li>
<li><p>We create How-to documentation to record any questions about How to xxx. In this version, we added</p>
<ul>
<li><p>How to use Mosaic augmentation (#7507)</p></li>
<li><p>How to use backbone in mmcls (#7438)</p></li>
<li><p>How to produce and submit the prediction results of panoptic segmentation models on COCO test-dev set (#7430))</p></li>
</ul>
</li>
</ul>
</section>
<section id="id13">
<h3>Contributors<a class="headerlink" href="#id13" title="Permalink to this heading">¶</a></h3>
<p>A total of 27 developers contributed to this release.
Thanks &#64;ZwwWayne, &#64;haofanwang, &#64;shinya7y, &#64;chhluo, &#64;yangrisheng, &#64;triple-Mu, &#64;jbwang1997, &#64;HikariTJU, &#64;imflash217, &#64;274869388, &#64;zytx121, &#64;matrixgame2018, &#64;jamiechoi1995, &#64;BIGWangYuDong, &#64;JingweiZhang12, &#64;Xiangxu-0103, &#64;hhaAndroid, &#64;jshilong, &#64;osbm, &#64;ceroytres, &#64;bunge-bedstraw-herb, &#64;Youth-Got, &#64;daavoo, &#64;jiangyitong, &#64;RangiLyu, &#64;CCODING04, &#64;yarkable</p>
</section>
</section>
<section id="v2-22-0-24-2-2022">
<h2>v2.22.0 (24/2/2022)<a class="headerlink" href="#v2-22-0-24-2-2022" title="Permalink to this heading">¶</a></h2>
<section id="id14">
<h3>Highlights<a class="headerlink" href="#id14" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support MaskFormer: <a class="reference external" href="https://arxiv.org/abs/2107.06278">Per-Pixel Classification is Not All You Need for Semantic Segmentation</a> (#7212)</p></li>
<li><p>Support DyHead: <a class="reference external" href="https://arxiv.org/abs/2106.08322">Dynamic Head: Unifying Object Detection Heads with Attentions</a> (#6823)</p></li>
<li><p>Release a good recipe of using ResNet in object detectors pre-trained by <a class="reference external" href="https://arxiv.org/abs/2110.00476">ResNet Strikes Back</a>, which consistently brings about 3~4 mAP improvements over RetinaNet, Faster/Mask/Cascade Mask R-CNN (#7001)</p></li>
<li><p>Support <a class="reference external" href="https://storage.googleapis.com/openimages/web/index.html">Open Images Dataset</a> (#6331)</p></li>
<li><p>Support TIMM backbone: <a class="reference external" href="https://github.com/rwightman/pytorch-image-models">PyTorch Image Models</a> (#7020)</p></li>
</ul>
</section>
<section id="id15">
<h3>New Features<a class="headerlink" href="#id15" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <span class="xref myst">MaskFormer</span> (#7212)</p></li>
<li><p>Support <span class="xref myst">DyHead</span> (#6823)</p></li>
<li><p>Support <span class="xref myst">ResNet Strikes Back</span> (#7001)</p></li>
<li><p>Support <span class="xref myst">OpenImages Dataset</span> (#6331)</p></li>
<li><p>Support <span class="xref myst">TIMM backbone</span> (#7020)</p></li>
<li><p>Support visualization for Panoptic Segmentation (#7041)</p></li>
</ul>
</section>
<section id="breaking-changes">
<h3>Breaking Changes<a class="headerlink" href="#breaking-changes" title="Permalink to this heading">¶</a></h3>
<p>In order to support the visualization for Panoptic Segmentation, the <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> can not be <code class="docutils literal notranslate"><span class="pre">None</span></code> when using the <code class="docutils literal notranslate"><span class="pre">get_palette</span></code> function to determine whether to use the panoptic palette.</p>
</section>
<section id="id16">
<h3>Bug Fixes<a class="headerlink" href="#id16" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix bug for the best checkpoints can not be saved when the <code class="docutils literal notranslate"><span class="pre">key_score</span></code> is None (#7101)</p></li>
<li><p>Fix MixUp transform filter boxes failing case (#7080)</p></li>
<li><p>Add missing properties in SABLHead (#7091)</p></li>
<li><p>Fix bug when NaNs exist in confusion matrix (#7147)</p></li>
<li><p>Fix PALETTE AttributeError in downstream task (#7230)</p></li>
</ul>
</section>
<section id="id17">
<h3>Improvements<a class="headerlink" href="#id17" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Speed up SimOTA matching (#7098)</p></li>
<li><p>Add Chinese translation of <code class="docutils literal notranslate"><span class="pre">docs_zh-CN/tutorials/init_cfg.md</span></code> (#7188)</p></li>
</ul>
</section>
<section id="id18">
<h3>Contributors<a class="headerlink" href="#id18" title="Permalink to this heading">¶</a></h3>
<p>A total of 20 developers contributed to this release.
Thanks &#64;ZwwWayne, &#64;hhaAndroid, &#64;RangiLyu, &#64;AronLin, &#64;BIGWangYuDong, &#64;jbwang1997, &#64;zytx121, &#64;chhluo, &#64;shinya7y, &#64;LuooChen, &#64;dvansa, &#64;siatwangmin, &#64;del-zhenwu, &#64;vikashranjan26, &#64;haofanwang, &#64;jamiechoi1995, &#64;HJoonKwon, &#64;yarkable, &#64;zhijian-liu, &#64;RangeKing</p>
</section>
</section>
<section id="v2-21-0-8-2-2022">
<h2>v2.21.0 (8/2/2022)<a class="headerlink" href="#v2-21-0-8-2-2022" title="Permalink to this heading">¶</a></h2>
</section>
<section id="id19">
<h2>Breaking Changes<a class="headerlink" href="#id19" title="Permalink to this heading">¶</a></h2>
<p>To standardize the contents in config READMEs and meta files of OpenMMLab projects, the READMEs and meta files in each config directory have been significantly changed. The template will be released in the future, for now, you can refer to the examples of README for <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/faster_rcnn/README.md">algorithm</a>, <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/deepfashion/README.md">dataset</a> and <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/configs/regnet/README.md">backbone</a>. To align with the standard, the configs in dcn are put into to two directories named <code class="docutils literal notranslate"><span class="pre">dcn</span></code> and <code class="docutils literal notranslate"><span class="pre">dcnv2</span></code>.</p>
<section id="id20">
<h3>New Features<a class="headerlink" href="#id20" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Allow to customize colors of different classes during visualization (#6716)</p></li>
<li><p>Support CPU training (#7016)</p></li>
<li><p>Add download script of COCO, LVIS, and VOC dataset (#7015)</p></li>
</ul>
</section>
<section id="id21">
<h3>Bug Fixes<a class="headerlink" href="#id21" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix weight conversion issue of RetinaNet with Swin-S (#6973)</p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">__repr__</span></code> of <code class="docutils literal notranslate"><span class="pre">Compose</span></code> (#6951)</p></li>
<li><p>Fix BadZipFile Error when build docker (#6966)</p></li>
<li><p>Fix bug in non-distributed multi-gpu training/testing (#7019)</p></li>
<li><p>Fix bbox clamp in PyTorch 1.10 (#7074)</p></li>
<li><p>Relax the requirement of PALETTE in dataset wrappers (#7085)</p></li>
<li><p>Keep the same weights before reassign in the PAA head (#7032)</p></li>
<li><p>Update code demo in doc (#7092)</p></li>
</ul>
</section>
<section id="id22">
<h3>Improvements<a class="headerlink" href="#id22" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Speed-up training by allow to set variables of multi-processing (#6974, #7036)</p></li>
<li><p>Add links of Chinese tutorials in readme (#6897)</p></li>
<li><p>Disable cv2 multiprocessing by default for acceleration (#6867)</p></li>
<li><p>Deprecate the support for “python setup.py test” (#6998)</p></li>
<li><p>Re-organize metafiles and config readmes (#7051)</p></li>
<li><p>Fix None grad problem during training TOOD by adding <code class="docutils literal notranslate"><span class="pre">SigmoidGeometricMean</span></code> (#7090)</p></li>
</ul>
</section>
<section id="id23">
<h3>Contributors<a class="headerlink" href="#id23" title="Permalink to this heading">¶</a></h3>
<p>A total of 26 developers contributed to this release.
Thanks &#64;del-zhenwu, &#64;zimoqingfeng, &#64;srishilesh, &#64;imyhxy, &#64;jenhaoyang, &#64;jliu-ac, &#64;kimnamu, &#64;ShengliLiu, &#64;garvan2021, &#64;ciusji, &#64;DIYer22, &#64;kimnamu, &#64;q3394101, &#64;zhouzaida, &#64;gaotongxiao, &#64;topsy404, &#64;AntoAndGar, &#64;jbwang1997, &#64;nijkah, &#64;ZwwWayne, &#64;Czm369, &#64;jshilong, &#64;RangiLyu, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;AronLin</p>
</section>
</section>
<section id="v2-20-0-27-12-2021">
<h2>v2.20.0 (27/12/2021)<a class="headerlink" href="#v2-20-0-27-12-2021" title="Permalink to this heading">¶</a></h2>
<section id="id24">
<h3>New Features<a class="headerlink" href="#id24" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <span class="xref myst">TOOD</span>: Task-aligned One-stage Object Detection (ICCV 2021 Oral) (#6746)</p></li>
<li><p>Support resuming from the latest checkpoint automatically (#6727)</p></li>
</ul>
</section>
<section id="id25">
<h3>Bug Fixes<a class="headerlink" href="#id25" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix wrong bbox <code class="docutils literal notranslate"><span class="pre">loss_weight</span></code> of the PAA head (#6744)</p></li>
<li><p>Fix the padding value of <code class="docutils literal notranslate"><span class="pre">gt_semantic_seg</span></code> in batch collating (#6837)</p></li>
<li><p>Fix test error of lvis when using <code class="docutils literal notranslate"><span class="pre">classwise</span></code> (#6845)</p></li>
<li><p>Avoid BC-breaking of <code class="docutils literal notranslate"><span class="pre">get_local_path</span></code>  (#6719)</p></li>
<li><p>Fix bug in <code class="docutils literal notranslate"><span class="pre">sync_norm_hook</span></code> when the BN layer does not exist (#6852)</p></li>
<li><p>Use pycocotools directly no matter what platform it is (#6838)</p></li>
</ul>
</section>
<section id="id26">
<h3>Improvements<a class="headerlink" href="#id26" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add unit test for SimOTA with no valid bbox (#6770)</p></li>
<li><p>Use precommit to check readme (#6802)</p></li>
<li><p>Support selecting GPU-ids in non-distributed testing time (#6781)</p></li>
</ul>
</section>
<section id="id27">
<h3>Contributors<a class="headerlink" href="#id27" title="Permalink to this heading">¶</a></h3>
<p>A total of 16 developers contributed to this release.
Thanks &#64;ZwwWayne, &#64;Czm369, &#64;jshilong, &#64;RangiLyu, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;jamiechoi1995, &#64;AronLin, &#64;Keiku, &#64;gkagkos, &#64;fcakyon, &#64;www516717402, &#64;vansin, &#64;zactodd, &#64;kimnamu, &#64;jenhaoyang</p>
</section>
</section>
<section id="v2-19-1-14-12-2021">
<h2>v2.19.1 (14/12/2021)<a class="headerlink" href="#v2-19-1-14-12-2021" title="Permalink to this heading">¶</a></h2>
<section id="id28">
<h3>New Features<a class="headerlink" href="#id28" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Release <span class="xref myst">YOLOX</span> COCO pretrained models (#6698)</p></li>
</ul>
</section>
<section id="id29">
<h3>Bug Fixes<a class="headerlink" href="#id29" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix DCN initialization in DenseHead (#6625)</p></li>
<li><p>Fix initialization of ConvFCHead (#6624)</p></li>
<li><p>Fix PseudoSampler in RCNN (#6622)</p></li>
<li><p>Fix weight initialization in Swin and PVT (#6663)</p></li>
<li><p>Fix dtype bug in BaseDenseHead (#6767)</p></li>
<li><p>Fix SimOTA with no valid bbox (#6733)</p></li>
</ul>
</section>
<section id="id30">
<h3>Improvements<a class="headerlink" href="#id30" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add an example of combining swin and one-stage models (#6621)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">get_ann_info</span></code> to dataset_wrappers (#6526)</p></li>
<li><p>Support keeping image ratio in the multi-scale training of YOLOX (#6732)</p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">bbox_clip_border</span></code> for the augmentations of YOLOX (#6730)</p></li>
</ul>
</section>
<section id="id31">
<h3>Documents<a class="headerlink" href="#id31" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Update metafile (#6717)</p></li>
<li><p>Add mmhuman3d in readme (#6699)</p></li>
<li><p>Update FAQ docs (#6587)</p></li>
<li><p>Add doc for <code class="docutils literal notranslate"><span class="pre">detect_anomalous_params</span></code> (#6697)</p></li>
</ul>
</section>
<section id="id32">
<h3>Contributors<a class="headerlink" href="#id32" title="Permalink to this heading">¶</a></h3>
<p>A total of 11 developers contributed to this release.
Thanks &#64;ZwwWayne, &#64;LJoson, &#64;Czm369, &#64;jshilong, &#64;ZCMax, &#64;RangiLyu, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;zhaoxin111, &#64;GT9505, &#64;shinya7y</p>
</section>
</section>
<section id="v2-19-0-29-11-2021">
<h2>v2.19.0 (29/11/2021)<a class="headerlink" href="#v2-19-0-29-11-2021" title="Permalink to this heading">¶</a></h2>
<section id="id33">
<h3>Highlights<a class="headerlink" href="#id33" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2108.10520">Label Assignment Distillation</a></p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">persistent_workers</span></code> for Pytorch &gt;= 1.7</p></li>
<li><p>Align accuracy to the updated official YOLOX</p></li>
</ul>
</section>
<section id="id34">
<h3>New Features<a class="headerlink" href="#id34" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2108.10520">Label Assignment Distillation</a> (#6342)</p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">persistent_workers</span></code> for Pytorch &gt;= 1.7 (#6435)</p></li>
</ul>
</section>
<section id="id35">
<h3>Bug Fixes<a class="headerlink" href="#id35" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix repeatedly output warning message (#6584)</p></li>
<li><p>Avoid infinite GPU waiting in dist training (#6501)</p></li>
<li><p>Fix SSD512 config error (#6574)</p></li>
<li><p>Fix MMDetection model to ONNX command (#6558)</p></li>
</ul>
</section>
<section id="id36">
<h3>Improvements<a class="headerlink" href="#id36" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Refactor configs of FP16 models (#6592)</p></li>
<li><p>Align accuracy to the updated official YOLOX (#6443)</p></li>
<li><p>Speed up training and reduce memory cost when using PhotoMetricDistortion. (#6442)</p></li>
<li><p>Make OHEM work with seesaw loss (#6514)</p></li>
</ul>
</section>
<section id="id37">
<h3>Documents<a class="headerlink" href="#id37" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Update README.md (#6567)</p></li>
</ul>
</section>
<section id="id38">
<h3>Contributors<a class="headerlink" href="#id38" title="Permalink to this heading">¶</a></h3>
<p>A total of 11 developers contributed to this release.
Thanks &#64;FloydHsiu, &#64;RangiLyu, &#64;ZwwWayne, &#64;AndreaPi, &#64;st9007a, &#64;hachreak, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;AronLin, &#64;chhluo, &#64;vealocia, &#64;HarborYuan, &#64;st9007a, &#64;jshilong</p>
</section>
</section>
<section id="v2-18-1-15-11-2021">
<h2>v2.18.1 (15/11/2021)<a class="headerlink" href="#v2-18-1-15-11-2021" title="Permalink to this heading">¶</a></h2>
<section id="id39">
<h3>Highlights<a class="headerlink" href="#id39" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Release <a class="reference external" href="http://arxiv.org/abs/2105.01928">QueryInst</a> pre-trained weights (#6460)</p></li>
<li><p>Support plot confusion matrix (#6344)</p></li>
</ul>
</section>
<section id="id40">
<h3>New Features<a class="headerlink" href="#id40" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Release <a class="reference external" href="http://arxiv.org/abs/2105.01928">QueryInst</a> pre-trained weights (#6460)</p></li>
<li><p>Support plot confusion matrix (#6344)</p></li>
</ul>
</section>
<section id="id41">
<h3>Bug Fixes<a class="headerlink" href="#id41" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix aug test error when the number of prediction bboxes is 0 (#6398)</p></li>
<li><p>Fix SpatialReductionAttention in PVT (#6488)</p></li>
<li><p>Fix wrong use of <code class="docutils literal notranslate"><span class="pre">trunc_normal_init</span></code> in PVT and Swin-Transformer (#6432)</p></li>
</ul>
</section>
<section id="id42">
<h3>Improvements<a class="headerlink" href="#id42" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Save the printed AP information of COCO API to logger (#6505)</p></li>
<li><p>Always map location to cpu when load checkpoint (#6405)</p></li>
<li><p>Set a random seed when the user does not set a seed (#6457)</p></li>
</ul>
</section>
<section id="id43">
<h3>Documents<a class="headerlink" href="#id43" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Chinese version of <a class="reference internal" href="robustness_benchmarking.html"><span class="doc std std-doc">Corruption Benchmarking</span></a> (#6375)</p></li>
<li><p>Fix config path in docs (#6396)</p></li>
<li><p>Update GRoIE readme (#6401)</p></li>
</ul>
</section>
<section id="id44">
<h3>Contributors<a class="headerlink" href="#id44" title="Permalink to this heading">¶</a></h3>
<p>A total of 11 developers contributed to this release.
Thanks &#64;st9007a, &#64;hachreak, &#64;HarborYuan, &#64;vealocia, &#64;chhluo, &#64;AndreaPi, &#64;AronLin, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;RangiLyu, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-18-0-27-10-2021">
<h2>v2.18.0 (27/10/2021)<a class="headerlink" href="#v2-18-0-27-10-2021" title="Permalink to this heading">¶</a></h2>
<section id="id45">
<h3>Highlights<a class="headerlink" href="#id45" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="http://arxiv.org/abs/2105.01928">QueryInst</a> (#6050)</p></li>
<li><p>Refactor dense heads to decouple onnx export logics from <code class="docutils literal notranslate"><span class="pre">get_bboxes</span></code> and speed up inference (#5317, #6003, #6369, #6268, #6315)</p></li>
</ul>
</section>
<section id="id46">
<h3>New Features<a class="headerlink" href="#id46" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="http://arxiv.org/abs/2105.01928">QueryInst</a> (#6050)</p></li>
<li><p>Support infinite sampler (#5996)</p></li>
</ul>
</section>
<section id="id47">
<h3>Bug Fixes<a class="headerlink" href="#id47" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix init_weight in fcn_mask_head (#6378)</p></li>
<li><p>Fix type error in imshow_bboxes of RPN (#6386)</p></li>
<li><p>Fix broken colab link in MMDetection Tutorial (#6382)</p></li>
<li><p>Make sure the device and dtype of scale_factor are the same as bboxes (#6374)</p></li>
<li><p>Remove sampling hardcode (#6317)</p></li>
<li><p>Fix RandomAffine bbox coordinate recorrection (#6293)</p></li>
<li><p>Fix init bug of final cls/reg layer in convfc head (#6279)</p></li>
<li><p>Fix img_shape broken in auto_augment (#6259)</p></li>
<li><p>Fix kwargs parameter missing error in two_stage (#6256)</p></li>
</ul>
</section>
<section id="id48">
<h3>Improvements<a class="headerlink" href="#id48" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Unify the interface of stuff head and panoptic head (#6308)</p></li>
<li><p>Polish readme (#6243)</p></li>
<li><p>Add code-spell pre-commit hook and fix a typo (#6306)</p></li>
<li><p>Fix typo (#6245, #6190)</p></li>
<li><p>Fix sampler unit test (#6284)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">forward_dummy</span></code> of YOLACT to enable <code class="docutils literal notranslate"><span class="pre">get_flops</span></code> (#6079)</p></li>
<li><p>Fix link error in the config documentation (#6252)</p></li>
<li><p>Adjust the order to beautify the document (#6195)</p></li>
</ul>
</section>
<section id="refactors">
<h3>Refactors<a class="headerlink" href="#refactors" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Refactor one-stage get_bboxes logic (#5317)</p></li>
<li><p>Refactor ONNX export of One-Stage models (#6003, #6369)</p></li>
<li><p>Refactor dense_head and speedup (#6268)</p></li>
<li><p>Migrate to use prior_generator in training of dense heads (#6315)</p></li>
</ul>
</section>
<section id="id49">
<h3>Contributors<a class="headerlink" href="#id49" title="Permalink to this heading">¶</a></h3>
<p>A total of 18 developers contributed to this release.
Thanks &#64;Boyden, &#64;onnkeat, &#64;st9007a, &#64;vealocia, &#64;yhcao6, &#64;DapangpangX, &#64;yellowdolphin, &#64;cclauss, &#64;kennymckormick,
&#64;pingguokiller, &#64;collinzrj, &#64;AndreaPi, &#64;AronLin, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;jshilong, &#64;RangiLyu, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-17-0-28-9-2021">
<h2>v2.17.0 (28/9/2021)<a class="headerlink" href="#v2-17-0-28-9-2021" title="Permalink to this heading">¶</a></h2>
<section id="id50">
<h3>Highlights<a class="headerlink" href="#id50" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2102.12122">PVT</a> and <a class="reference external" href="https://arxiv.org/abs/2106.13797">PVTv2</a></p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1912.04488">SOLO</a></p></li>
<li><p>Support large scale jittering and New Mask R-CNN baselines</p></li>
<li><p>Speed up <code class="docutils literal notranslate"><span class="pre">YOLOv3</span></code> inference</p></li>
</ul>
</section>
<section id="id51">
<h3>New Features<a class="headerlink" href="#id51" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2102.12122">PVT</a> and <a class="reference external" href="https://arxiv.org/abs/2106.13797">PVTv2</a> (#5780)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1912.04488">SOLO</a> (#5832)</p></li>
<li><p>Support large scale jittering and New Mask R-CNN baselines (#6132)</p></li>
<li><p>Add a general data structrue for the results of models (#5508)</p></li>
<li><p>Added a base class for one-stage instance segmentation (#5904)</p></li>
<li><p>Speed up <code class="docutils literal notranslate"><span class="pre">YOLOv3</span></code> inference (#5991)</p></li>
<li><p>Release Swin Transformer pre-trained models (#6100)</p></li>
<li><p>Support mixed precision training in <code class="docutils literal notranslate"><span class="pre">YOLOX</span></code> (#5983)</p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">val</span></code> workflow in <code class="docutils literal notranslate"><span class="pre">YOLACT</span></code> (#5986)</p></li>
<li><p>Add script to test <code class="docutils literal notranslate"><span class="pre">torchserve</span></code> (#5936)</p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">onnxsim</span></code> with dynamic input shape (#6117)</p></li>
</ul>
</section>
<section id="id52">
<h3>Bug Fixes<a class="headerlink" href="#id52" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix the function naming errors in <code class="docutils literal notranslate"><span class="pre">model_wrappers</span></code> (#5975)</p></li>
<li><p>Fix regression loss bug when the input is an empty tensor (#5976)</p></li>
<li><p>Fix scores not contiguous error in <code class="docutils literal notranslate"><span class="pre">centernet_head</span></code> (#6016)</p></li>
<li><p>Fix missing parameters bug in <code class="docutils literal notranslate"><span class="pre">imshow_bboxes</span></code> (#6034)</p></li>
<li><p>Fix bug in <code class="docutils literal notranslate"><span class="pre">aug_test</span></code> of <code class="docutils literal notranslate"><span class="pre">HTC</span></code> when the length of <code class="docutils literal notranslate"><span class="pre">det_bboxes</span></code> is 0 (#6088)</p></li>
<li><p>Fix empty proposal errors in the training of some two-stage models (#5941)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">dynamic_axes</span></code> parameter error in <code class="docutils literal notranslate"><span class="pre">ONNX</span></code> dynamic shape export (#6104)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">dynamic_shape</span></code> bug of <code class="docutils literal notranslate"><span class="pre">SyncRandomSizeHook</span></code> (#6144)</p></li>
<li><p>Fix the Swin Transformer config link error in the configuration (#6172)</p></li>
</ul>
</section>
<section id="id53">
<h3>Improvements<a class="headerlink" href="#id53" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add filter rules in <code class="docutils literal notranslate"><span class="pre">Mosaic</span></code> transform (#5897)</p></li>
<li><p>Add size divisor in get flops to avoid some potential bugs (#6076)</p></li>
<li><p>Add Chinese translation of <code class="docutils literal notranslate"><span class="pre">docs_zh-CN/tutorials/customize_dataset.md</span></code> (#5915)</p></li>
<li><p>Add Chinese translation of <code class="docutils literal notranslate"><span class="pre">conventions.md</span></code> (#5825)</p></li>
<li><p>Add description of the output of data pipeline (#5886)</p></li>
<li><p>Add dataset information in the README file for <code class="docutils literal notranslate"><span class="pre">PanopticFPN</span></code> (#5996)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">extra_repr</span></code> for <code class="docutils literal notranslate"><span class="pre">DropBlock</span></code> layer to get details in the model printing (#6140)</p></li>
<li><p>Fix CI out of memory and add PyTorch1.9 Python3.9 unit tests (#5862)</p></li>
<li><p>Fix download links error of some model (#6069)</p></li>
<li><p>Improve the generalization of XML dataset (#5943)</p></li>
<li><p>Polish assertion error messages (#6017)</p></li>
<li><p>Remove <code class="docutils literal notranslate"><span class="pre">opencv-python-headless</span></code> dependency by <code class="docutils literal notranslate"><span class="pre">albumentations</span></code> (#5868)</p></li>
<li><p>Check dtype in transform unit tests (#5969)</p></li>
<li><p>Replace the default theme of documentation with PyTorch Sphinx Theme (#6146)</p></li>
<li><p>Update the paper and code fields in the metafile (#6043)</p></li>
<li><p>Support to customize padding value of segmentation map (#6152)</p></li>
<li><p>Support to resize multiple segmentation maps (#5747)</p></li>
</ul>
</section>
<section id="id54">
<h3>Contributors<a class="headerlink" href="#id54" title="Permalink to this heading">¶</a></h3>
<p>A total of 24 developers contributed to this release.
Thanks &#64;morkovka1337, &#64;HarborYuan, &#64;guillaumefrd, &#64;guigarfr, &#64;www516717402, &#64;gaotongxiao, &#64;ypwhs, &#64;MartaYang, &#64;shinya7y, &#64;justiceeem, &#64;zhaojinjian0000, &#64;VVsssssk, &#64;aravind-anantha, &#64;wangbo-zhao, &#64;czczup, &#64;whai362, &#64;czczup, &#64;marijnl, &#64;AronLin, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;jshilong, &#64;RangiLyu, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-16-0-30-8-2021">
<h2>v2.16.0 (30/8/2021)<a class="headerlink" href="#v2-16-0-30-8-2021" title="Permalink to this heading">¶</a></h2>
<section id="id55">
<h3>Highlights<a class="headerlink" href="#id55" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1901.02446">Panoptic FPN</a> and <a class="reference external" href="https://arxiv.org/abs/2103.14030">Swin Transformer</a></p></li>
</ul>
</section>
<section id="id56">
<h3>New Features<a class="headerlink" href="#id56" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1901.02446">Panoptic FPN</a> and release models (#5577, #5902)</p></li>
<li><p>Support Swin Transformer backbone (#5748)</p></li>
<li><p>Release RetinaNet models pre-trained with multi-scale 3x schedule (#5636)</p></li>
<li><p>Add script to convert unlabeled image list to coco format (#5643)</p></li>
<li><p>Add hook to check whether the loss value is valid (#5674)</p></li>
<li><p>Add YOLO anchor optimizing tool (#5644)</p></li>
<li><p>Support export onnx models without post process. (#5851)</p></li>
<li><p>Support classwise evaluation in CocoPanopticDataset (#5896)</p></li>
<li><p>Adapt browse_dataset for concatenated datasets. (#5935)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">PatchEmbed</span></code> and <code class="docutils literal notranslate"><span class="pre">PatchMerging</span></code> with <code class="docutils literal notranslate"><span class="pre">AdaptivePadding</span></code> (#5952)</p></li>
</ul>
</section>
<section id="id57">
<h3>Bug Fixes<a class="headerlink" href="#id57" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix unit tests of YOLOX (#5859)</p></li>
<li><p>Fix lose randomness in <code class="docutils literal notranslate"><span class="pre">imshow_det_bboxes</span></code> (#5845)</p></li>
<li><p>Make output result of <code class="docutils literal notranslate"><span class="pre">ImageToTensor</span></code> contiguous (#5756)</p></li>
<li><p>Fix inference bug when calling <code class="docutils literal notranslate"><span class="pre">regress_by_class</span></code> in RoIHead in some cases (#5884)</p></li>
<li><p>Fix bug in CIoU loss where alpha should not have gradient. (#5835)</p></li>
<li><p>Fix the bug that <code class="docutils literal notranslate"><span class="pre">multiscale_output</span></code> is defined but not used in HRNet (#5887)</p></li>
<li><p>Set the priority of EvalHook to LOW. (#5882)</p></li>
<li><p>Fix a YOLOX bug when applying bbox rescaling in test mode (#5899)</p></li>
<li><p>Fix mosaic coordinate error (#5947)</p></li>
<li><p>Fix dtype of bbox in RandomAffine. (#5930)</p></li>
</ul>
</section>
<section id="id58">
<h3>Improvements<a class="headerlink" href="#id58" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add Chinese version of <code class="docutils literal notranslate"><span class="pre">data_pipeline</span></code> and  (#5662)</p></li>
<li><p>Support to remove state dicts of EMA when publishing models. (#5858)</p></li>
<li><p>Refactor the loss function in HTC and SCNet (#5881)</p></li>
<li><p>Use warnings instead of logger.warning (#5540)</p></li>
<li><p>Use legacy coordinate in metric of VOC (#5627)</p></li>
<li><p>Add Chinese version of customize_losses (#5826)</p></li>
<li><p>Add Chinese version of model_zoo (#5827)</p></li>
</ul>
</section>
<section id="id59">
<h3>Contributors<a class="headerlink" href="#id59" title="Permalink to this heading">¶</a></h3>
<p>A total of 19 developers contributed to this release.
Thanks &#64;ypwhs, &#64;zywvvd, &#64;collinzrj, &#64;OceanPang, &#64;ddonatien, &#64;&#64;haotian-liu, &#64;viibridges, &#64;Muyun99, &#64;guigarfr, &#64;zhaojinjian0000, &#64;jbwang1997,&#64;wangbo-zhao, &#64;xvjiarui, &#64;RangiLyu, &#64;jshilong, &#64;AronLin, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-15-1-11-8-2021">
<h2>v2.15.1 (11/8/2021)<a class="headerlink" href="#v2-15-1-11-8-2021" title="Permalink to this heading">¶</a></h2>
<section id="id60">
<h3>Highlights<a class="headerlink" href="#id60" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2107.08430">YOLOX</a></p></li>
</ul>
</section>
<section id="id61">
<h3>New Features<a class="headerlink" href="#id61" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2107.08430">YOLOX</a>(#5756, #5758, #5760, #5767, #5770, #5774, #5777, #5808, #5828, #5848)</p></li>
</ul>
</section>
<section id="id62">
<h3>Bug Fixes<a class="headerlink" href="#id62" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Update correct SSD models. (#5789)</p></li>
<li><p>Fix casting error in mask structure (#5820)</p></li>
<li><p>Fix MMCV deployment documentation links. (#5790)</p></li>
</ul>
</section>
<section id="id63">
<h3>Improvements<a class="headerlink" href="#id63" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Use dynamic MMCV download link in TorchServe dockerfile (#5779)</p></li>
<li><p>Rename the function <code class="docutils literal notranslate"><span class="pre">upsample_like</span></code> to <code class="docutils literal notranslate"><span class="pre">interpolate_as</span></code> for more general usage (#5788)</p></li>
</ul>
</section>
<section id="id64">
<h3>Contributors<a class="headerlink" href="#id64" title="Permalink to this heading">¶</a></h3>
<p>A total of 14 developers contributed to this release.
Thanks &#64;HAOCHENYE, &#64;xiaohu2015, &#64;HsLOL, &#64;zhiqwang, &#64;Adamdad, &#64;shinya7y, &#64;Johnson-Wang, &#64;RangiLyu, &#64;jshilong, &#64;mmeendez8, &#64;AronLin, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;ZwwWayne</p>
</section>
</section>
<section id="v2-15-0-02-8-2021">
<h2>v2.15.0 (02/8/2021)<a class="headerlink" href="#v2-15-0-02-8-2021" title="Permalink to this heading">¶</a></h2>
<section id="id65">
<h3>Highlights<a class="headerlink" href="#id65" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support adding <a class="reference external" href="https://github.com/open-mmlab/mim">MIM</a> dependencies during pip installation</p></li>
<li><p>Support MobileNetV2 for SSD-Lite and YOLOv3</p></li>
<li><p>Support Chinese Documentation</p></li>
</ul>
</section>
<section id="id66">
<h3>New Features<a class="headerlink" href="#id66" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add function <code class="docutils literal notranslate"><span class="pre">upsample_like</span></code> (#5732)</p></li>
<li><p>Support to output pdf and epub format documentation (#5738)</p></li>
<li><p>Support and release Cascade Mask R-CNN 3x pre-trained models (#5645)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">ignore_index</span></code> to CrossEntropyLoss (#5646)</p></li>
<li><p>Support adding <a class="reference external" href="https://github.com/open-mmlab/mim">MIM</a> dependencies during pip installation (#5676)</p></li>
<li><p>Add MobileNetV2 config and models for YOLOv3 (#5510)</p></li>
<li><p>Support COCO Panoptic Dataset (#5231)</p></li>
<li><p>Support ONNX export of cascade models (#5486)</p></li>
<li><p>Support DropBlock with RetinaNet (#5544)</p></li>
<li><p>Support MobileNetV2 SSD-Lite (#5526)</p></li>
</ul>
</section>
<section id="id67">
<h3>Bug Fixes<a class="headerlink" href="#id67" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix the device of label in multiclass_nms (#5673)</p></li>
<li><p>Fix error of backbone initialization from pre-trained checkpoint in config file (#5603, #5550)</p></li>
<li><p>Fix download links of RegNet pretrained weights (#5655)</p></li>
<li><p>Fix two-stage runtime error given empty proposal (#5559)</p></li>
<li><p>Fix flops count error in DETR (#5654)</p></li>
<li><p>Fix unittest for <code class="docutils literal notranslate"><span class="pre">NumClassCheckHook</span></code> when it is not used. (#5626)</p></li>
<li><p>Fix description bug of using custom dataset (#5546)</p></li>
<li><p>Fix bug of <code class="docutils literal notranslate"><span class="pre">multiclass_nms</span></code> that returns the global indices (#5592)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">valid_mask</span></code> logic error in RPNHead (#5562)</p></li>
<li><p>Fix unit test error of pretrained configs (#5561)</p></li>
<li><p>Fix typo error in anchor_head.py (#5555)</p></li>
<li><p>Fix bug when using dataset wrappers (#5552)</p></li>
<li><p>Fix a typo error in demo/MMDet_Tutorial.ipynb (#5511)</p></li>
<li><p>Fixing crash in <code class="docutils literal notranslate"><span class="pre">get_root_logger</span></code> when <code class="docutils literal notranslate"><span class="pre">cfg.log_level</span></code> is not None (#5521)</p></li>
<li><p>Fix docker version (#5502)</p></li>
<li><p>Fix optimizer parameter error when using <code class="docutils literal notranslate"><span class="pre">IterBasedRunner</span></code> (#5490)</p></li>
</ul>
</section>
<section id="id68">
<h3>Improvements<a class="headerlink" href="#id68" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add unit tests for MMTracking (#5620)</p></li>
<li><p>Add Chinese translation of documentation (#5718, #5618, #5558, #5423, #5593, #5421, #5408. #5369, #5419, #5530, #5531)</p></li>
<li><p>Update resource limit (#5697)</p></li>
<li><p>Update docstring for InstaBoost (#5640)</p></li>
<li><p>Support key <code class="docutils literal notranslate"><span class="pre">reduction_override</span></code> in all loss functions (#5515)</p></li>
<li><p>Use repeatdataset to accelerate CenterNet training (#5509)</p></li>
<li><p>Remove unnecessary code in autoassign (#5519)</p></li>
<li><p>Add documentation about <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> (#5273)</p></li>
</ul>
</section>
<section id="id69">
<h3>Contributors<a class="headerlink" href="#id69" title="Permalink to this heading">¶</a></h3>
<p>A total of 18 developers contributed to this release.
Thanks &#64;OceanPang, &#64;AronLin, &#64;hellock, &#64;Outsider565, &#64;RangiLyu, &#64;ElectronicElephant, &#64;likyoo, &#64;BIGWangYuDong, &#64;hhaAndroid, &#64;noobying, &#64;yyz561, &#64;likyoo,
&#64;zeakey, &#64;ZwwWayne, &#64;ChenyangLiu, &#64;johnson-magic, &#64;qingswu, &#64;BuxianChen</p>
</section>
</section>
<section id="v2-14-0-29-6-2021">
<h2>v2.14.0 (29/6/2021)<a class="headerlink" href="#v2-14-0-29-6-2021" title="Permalink to this heading">¶</a></h2>
<section id="id70">
<h3>Highlights<a class="headerlink" href="#id70" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add <code class="docutils literal notranslate"><span class="pre">simple_test</span></code> to dense heads to improve the consistency of single-stage and two-stage detectors</p></li>
<li><p>Revert the <code class="docutils literal notranslate"><span class="pre">test_mixins</span></code> to single image test to improve efficiency and readability</p></li>
<li><p>Add Faster R-CNN and Mask R-CNN config using multi-scale training with 3x schedule</p></li>
</ul>
</section>
<section id="id71">
<h3>New Features<a class="headerlink" href="#id71" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support pretrained models from MoCo v2 and SwAV (#5286)</p></li>
<li><p>Add Faster R-CNN and Mask R-CNN config using multi-scale training with 3x schedule (#5179, #5233)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">reduction_override</span></code> in MSELoss (#5437)</p></li>
<li><p>Stable support of exporting DETR to ONNX with dynamic shapes and batch inference (#5168)</p></li>
<li><p>Stable support of exporting PointRend to ONNX with dynamic shapes and batch inference (#5440)</p></li>
</ul>
</section>
<section id="id72">
<h3>Bug Fixes<a class="headerlink" href="#id72" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix size mismatch bug in <code class="docutils literal notranslate"><span class="pre">multiclass_nms</span></code> (#4980)</p></li>
<li><p>Fix the import path of <code class="docutils literal notranslate"><span class="pre">MultiScaleDeformableAttention</span></code> (#5338)</p></li>
<li><p>Fix errors in config of GCNet ResNext101 models (#5360)</p></li>
<li><p>Fix Grid-RCNN error when there is no bbox result (#5357)</p></li>
<li><p>Fix errors in <code class="docutils literal notranslate"><span class="pre">onnx_export</span></code> of bbox_head when setting reg_class_agnostic (#5468)</p></li>
<li><p>Fix type error of AutoAssign in the document (#5478)</p></li>
<li><p>Fix web links ending with <code class="docutils literal notranslate"><span class="pre">.md</span></code> (#5315)</p></li>
</ul>
</section>
<section id="id73">
<h3>Improvements<a class="headerlink" href="#id73" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Add <code class="docutils literal notranslate"><span class="pre">simple_test</span></code> to dense heads to improve the consistency of single-stage and two-stage detectors (#5264)</p></li>
<li><p>Add support for mask diagonal flip in TTA (#5403)</p></li>
<li><p>Revert the <code class="docutils literal notranslate"><span class="pre">test_mixins</span></code> to single image test to improve efficiency and readability (#5249)</p></li>
<li><p>Make YOLOv3 Neck more flexible (#5218)</p></li>
<li><p>Refactor SSD to make it more general (#5291)</p></li>
<li><p>Refactor <code class="docutils literal notranslate"><span class="pre">anchor_generator</span></code> and <code class="docutils literal notranslate"><span class="pre">point_generator</span></code> (#5349)</p></li>
<li><p>Allow to configure out the <code class="docutils literal notranslate"><span class="pre">mask_head</span></code> of the HTC algorithm (#5389)</p></li>
<li><p>Delete deprecated warning in FPN (#5311)</p></li>
<li><p>Move <code class="docutils literal notranslate"><span class="pre">model.pretrained</span></code> to <code class="docutils literal notranslate"><span class="pre">model.backbone.init_cfg</span></code> (#5370)</p></li>
<li><p>Make deployment tools more friendly to use (#5280)</p></li>
<li><p>Clarify installation documentation (#5316)</p></li>
<li><p>Add ImageNet Pretrained Models docs (#5268)</p></li>
<li><p>Add FAQ about training loss=nan solution and COCO AP or AR =-1 (# 5312, #5313)</p></li>
<li><p>Change all weight links of http to https (#5328)</p></li>
</ul>
</section>
</section>
<section id="v2-13-0-01-6-2021">
<h2>v2.13.0 (01/6/2021)<a class="headerlink" href="#v2-13-0-01-6-2021" title="Permalink to this heading">¶</a></h2>
<section id="id74">
<h3>Highlights<a class="headerlink" href="#id74" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1904.07850">CenterNet</a>, <a class="reference external" href="https://arxiv.org/abs/2008.10032">Seesaw Loss</a>, <a class="reference external" href="https://arxiv.org/abs/1801.04381">MobileNetV2</a></p></li>
</ul>
</section>
<section id="id75">
<h3>New Features<a class="headerlink" href="#id75" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support paper <a class="reference external" href="https://arxiv.org/abs/1904.07850">Objects as Points</a> (#4602)</p></li>
<li><p>Support paper <a class="reference external" href="https://arxiv.org/abs/2008.10032">Seesaw Loss for Long-Tailed Instance Segmentation (CVPR 2021)</a> (#5128)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1801.04381">MobileNetV2</a> backbone and inverted residual block (#5122)</p></li>
<li><p>Support <a class="reference external" href="https://github.com/open-mmlab/mim">MIM</a> (#5143)</p></li>
<li><p>ONNX exportation with dynamic shapes of CornerNet (#5136)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">mask_soft</span></code> config option to allow non-binary masks (#4615)</p></li>
<li><p>Add PWC metafile (#5135)</p></li>
</ul>
</section>
<section id="id76">
<h3>Bug Fixes<a class="headerlink" href="#id76" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix YOLOv3 FP16 training error (#5172)</p></li>
<li><p>Fix Cacscade R-CNN TTA test error when <code class="docutils literal notranslate"><span class="pre">det_bboxes</span></code> length is 0  (#5221)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">iou_thr</span></code> variable naming errors in VOC recall calculation function (#5195)</p></li>
<li><p>Fix Faster R-CNN performance dropped in ONNX Runtime (#5197)</p></li>
<li><p>Fix DETR dict changed error when using python 3.8 during iteration  (#5226)</p></li>
</ul>
</section>
<section id="id77">
<h3>Improvements<a class="headerlink" href="#id77" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Refactor ONNX export of two stage detector (#5205)</p></li>
<li><p>Replace MMDetection’s EvalHook with MMCV’s EvalHook for consistency  (#4806)</p></li>
<li><p>Update RoI extractor for ONNX (#5194)</p></li>
<li><p>Use better parameter initialization in YOLOv3 head for higher performance (#5181)</p></li>
<li><p>Release new DCN models of Mask R-CNN by mixed-precision training (#5201)</p></li>
<li><p>Update YOLOv3 model weights (#5229)</p></li>
<li><p>Add DetectoRS ResNet-101 model weights (#4960)</p></li>
<li><p>Discard bboxes with sizes equals to <code class="docutils literal notranslate"><span class="pre">min_bbox_size</span></code> (#5011)</p></li>
<li><p>Remove duplicated code in DETR head (#5129)</p></li>
<li><p>Remove unnecessary object in class definition (#5180)</p></li>
<li><p>Fix doc link (#5192)</p></li>
</ul>
</section>
</section>
<section id="v2-12-0-01-5-2021">
<h2>v2.12.0 (01/5/2021)<a class="headerlink" href="#v2-12-0-01-5-2021" title="Permalink to this heading">¶</a></h2>
<section id="id78">
<h3>Highlights<a class="headerlink" href="#id78" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/2007.03496">AutoAssign</a>, <a class="reference external" href="https://arxiv.org/abs/2103.09460">YOLOF</a>, and <a class="reference external" href="https://arxiv.org/abs/2010.04159">Deformable DETR</a></p></li>
<li><p>Stable support of exporting models to ONNX with batched images and dynamic shape (#5039)</p></li>
</ul>
</section>
<section id="id79">
<h3>Backwards Incompatible Changes<a class="headerlink" href="#id79" title="Permalink to this heading">¶</a></h3>
<p>MMDetection is going through big refactoring for more general and convenient usages during the releases from v2.12.0 to v2.15.0 (maybe longer).
In v2.12.0 MMDetection inevitably brings some BC-breakings, including the MMCV dependency, model initialization, model registry, and mask AP evaluation.</p>
<ul class="simple">
<li><p>MMCV version. MMDetection v2.12.0 relies on the newest features in MMCV 1.3.3, including <code class="docutils literal notranslate"><span class="pre">BaseModule</span></code> for unified parameter initialization, model registry, and the CUDA operator <code class="docutils literal notranslate"><span class="pre">MultiScaleDeformableAttn</span></code> for <a class="reference external" href="https://arxiv.org/abs/2010.04159">Deformable DETR</a>. Note that MMCV 1.3.2 already contains all the features used by MMDet but has known issues. Therefore, we recommend users skip MMCV v1.3.2 and use v1.3.3, though v1.3.2 might work for most cases.</p></li>
<li><p>Unified model initialization (#4750). To unify the parameter initialization in OpenMMLab projects, MMCV supports <code class="docutils literal notranslate"><span class="pre">BaseModule</span></code> that accepts <code class="docutils literal notranslate"><span class="pre">init_cfg</span></code> to allow the modules’ parameters initialized in a flexible and unified manner. Now the users need to explicitly call <code class="docutils literal notranslate"><span class="pre">model.init_weights()</span></code> in the training script to initialize the model (as in <a class="reference external" href="https://github.com/open-mmlab/rsidetection/blob/master/tools/train.py#L162">here</a>, previously this was handled by the detector. The models in MMDetection have been re-benchmarked to ensure accuracy based on PR #4750. <strong>The downstream projects should update their code accordingly to use MMDetection v2.12.0</strong>.</p></li>
<li><p>Unified model registry (#5059). To easily use backbones implemented in other OpenMMLab projects, MMDetection migrates to inherit the model registry created in MMCV (#760). In this way, as long as the backbone is supported in an OpenMMLab project and that project also uses the registry in MMCV, users can use that backbone in MMDetection by simply modifying the config without copying the code of that backbone into MMDetection.</p></li>
<li><p>Mask AP evaluation (#4898). Previous versions calculate the areas of masks through the bounding boxes when calculating the mask AP of small, medium, and large instances. To indeed use the areas of masks, we pop the key <code class="docutils literal notranslate"><span class="pre">bbox</span></code> during mask AP calculation. This change does not affect the overall mask AP evaluation and aligns the mask AP of similar models in other projects like Detectron2.</p></li>
</ul>
</section>
<section id="id80">
<h3>New Features<a class="headerlink" href="#id80" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support paper <a class="reference external" href="https://arxiv.org/abs/2007.03496">AutoAssign: Differentiable Label Assignment for Dense Object Detection</a> (#4295)</p></li>
<li><p>Support paper <a class="reference external" href="https://arxiv.org/abs/2103.09460">You Only Look One-level Feature</a> (#4295)</p></li>
<li><p>Support paper <a class="reference external" href="https://arxiv.org/abs/2010.04159">Deformable DETR: Deformable Transformers for End-to-End Object Detection</a> (#4778)</p></li>
<li><p>Support calculating IoU with FP16 tensor in <code class="docutils literal notranslate"><span class="pre">bbox_overlaps</span></code> to save memory and keep speed (#4889)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">__repr__</span></code> in custom dataset to count the number of instances (#4756)</p></li>
<li><p>Add windows support by updating requirements.txt (#5052)</p></li>
<li><p>Stable support of exporting models to ONNX with batched images and dynamic shape, including SSD, FSAF,FCOS, YOLOv3, RetinaNet, Faster R-CNN, and Mask R-CNN (#5039)</p></li>
</ul>
</section>
<section id="id81">
<h3>Improvements<a class="headerlink" href="#id81" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Use MMCV <code class="docutils literal notranslate"><span class="pre">MODEL_REGISTRY</span></code> (#5059)</p></li>
<li><p>Unified parameter initialization for more flexible usage (#4750)</p></li>
<li><p>Rename variable names and fix docstring in anchor head (#4883)</p></li>
<li><p>Support training with empty GT in Cascade RPN (#4928)</p></li>
<li><p>Add more details of usage of <code class="docutils literal notranslate"><span class="pre">test_robustness</span></code> in documentation (#4917)</p></li>
<li><p>Changing to use <code class="docutils literal notranslate"><span class="pre">pycocotools</span></code> instead of <code class="docutils literal notranslate"><span class="pre">mmpycocotools</span></code> to fully support Detectron2 and MMDetection in one environment (#4939)</p></li>
<li><p>Update torch serve dockerfile to support dockers of more versions (#4954)</p></li>
<li><p>Add check for training with single class dataset (#4973)</p></li>
<li><p>Refactor transformer and DETR Head (#4763)</p></li>
<li><p>Update FPG model zoo (#5079)</p></li>
<li><p>More accurate mask AP of small/medium/large instances (#4898)</p></li>
</ul>
</section>
<section id="id82">
<h3>Bug Fixes<a class="headerlink" href="#id82" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix bug in mean_ap.py when calculating mAP by 11 points (#4875)</p></li>
<li><p>Fix error when key <code class="docutils literal notranslate"><span class="pre">meta</span></code> is not in old checkpoints (#4936)</p></li>
<li><p>Fix hanging bug when training with empty GT in VFNet, GFL, and FCOS by changing the place of <code class="docutils literal notranslate"><span class="pre">reduce_mean</span></code> (#4923, #4978, #5058)</p></li>
<li><p>Fix asyncronized inference error and provide related demo (#4941)</p></li>
<li><p>Fix IoU losses dimensionality unmatch error (#4982)</p></li>
<li><p>Fix torch.randperm whtn using PyTorch 1.8 (#5014)</p></li>
<li><p>Fix empty bbox error in <code class="docutils literal notranslate"><span class="pre">mask_head</span></code> when using CARAFE (#5062)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">supplement_mask</span></code> bug when there are zero-size RoIs (#5065)</p></li>
<li><p>Fix testing with empty rois in RoI Heads (#5081)</p></li>
</ul>
</section>
</section>
<section id="v2-11-0-01-4-2021">
<h2>v2.11.0 (01/4/2021)<a class="headerlink" href="#v2-11-0-01-4-2021" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Support new method: <a class="reference external" href="https://arxiv.org/pdf/2102.12252.pdf">Localization Distillation for Object Detection</a></p></li>
<li><p>Support Pytorch2ONNX with batch inference and dynamic shape</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/pdf/2102.12252.pdf">Localization Distillation for Object Detection</a> (#4758)</p></li>
<li><p>Support Pytorch2ONNX with batch inference and dynamic shape for Faster-RCNN and mainstream one-stage detectors (#4796)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Support batch inference in head of RetinaNet (#4699)</p></li>
<li><p>Add batch dimension in second stage of Faster-RCNN (#4785)</p></li>
<li><p>Support batch inference in bbox coder (#4721)</p></li>
<li><p>Add check for <code class="docutils literal notranslate"><span class="pre">ann_ids</span></code> in <code class="docutils literal notranslate"><span class="pre">COCODataset</span></code> to ensure it is unique (#4789)</p></li>
<li><p>support for showing the FPN results (#4716)</p></li>
<li><p>support dynamic shape for grid_anchor (#4684)</p></li>
<li><p>Move pycocotools version check to when it is used (#4880)</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix a bug of TridentNet when doing the batch inference (#4717)</p></li>
<li><p>Fix a bug of Pytorch2ONNX in FASF (#4735)</p></li>
<li><p>Fix a bug when show the image with float type (#4732)</p></li>
</ul>
</section>
<section id="v2-10-0-01-03-2021">
<h2>v2.10.0 (01/03/2021)<a class="headerlink" href="#v2-10-0-01-03-2021" title="Permalink to this heading">¶</a></h2>
<section id="id83">
<h3>Highlights<a class="headerlink" href="#id83" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/2004.03580">FPG</a></p></li>
<li><p>Support ONNX2TensorRT for SSD, FSAF, FCOS, YOLOv3, and Faster R-CNN.</p></li>
</ul>
</section>
<section id="id84">
<h3>New Features<a class="headerlink" href="#id84" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support ONNX2TensorRT for SSD, FSAF, FCOS, YOLOv3, and Faster R-CNN (#4569)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2004.03580">Feature Pyramid Grids (FPG)</a> (#4645)</p></li>
<li><p>Support video demo (#4420)</p></li>
<li><p>Add seed option for sampler (#4665)</p></li>
<li><p>Support to customize type of runner (#4570, #4669)</p></li>
<li><p>Support synchronizing BN buffer in <code class="docutils literal notranslate"><span class="pre">EvalHook</span></code> (#4582)</p></li>
<li><p>Add script for GIF demo (#4573)</p></li>
</ul>
</section>
<section id="id85">
<h3>Bug Fixes<a class="headerlink" href="#id85" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix ConfigDict AttributeError and add Colab link (#4643)</p></li>
<li><p>Avoid crash in empty gt training of GFL head (#4631)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">iou_thrs</span></code> bug in RPN evaluation (#4581)</p></li>
<li><p>Fix syntax error of config when upgrading model version (#4584)</p></li>
</ul>
</section>
<section id="id86">
<h3>Improvements<a class="headerlink" href="#id86" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Refactor unit test file structures (#4600)</p></li>
<li><p>Refactor nms config (#4636)</p></li>
<li><p>Get loading pipeline by checking the class directly rather than through config strings (#4619)</p></li>
<li><p>Add doctests for mask target generation and mask structures (#4614)</p></li>
<li><p>Use deep copy when copying pipeline arguments (#4621)</p></li>
<li><p>Update documentations (#4642, #4650, #4620, #4630)</p></li>
<li><p>Remove redundant code calling <code class="docutils literal notranslate"><span class="pre">import_modules_from_strings</span></code> (#4601)</p></li>
<li><p>Clean deprecated FP16 API (#4571)</p></li>
<li><p>Check whether <code class="docutils literal notranslate"><span class="pre">CLASSES</span></code> is correctly initialized in the initialization of <code class="docutils literal notranslate"><span class="pre">XMLDataset</span></code> (#4555)</p></li>
<li><p>Support batch inference in the inference API (#4462, #4526)</p></li>
<li><p>Clean deprecated warning and fix ‘meta’ error (#4695)</p></li>
</ul>
</section>
</section>
<section id="v2-9-0-01-02-2021">
<h2>v2.9.0 (01/02/2021)<a class="headerlink" href="#v2-9-0-01-02-2021" title="Permalink to this heading">¶</a></h2>
<section id="id87">
<h3>Highlights<a class="headerlink" href="#id87" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/2012.10150">SCNet</a>, <a class="reference external" href="https://arxiv.org/abs/2011.12450">Sparse R-CNN</a></p></li>
<li><p>Move <code class="docutils literal notranslate"><span class="pre">train_cfg</span></code> and <code class="docutils literal notranslate"><span class="pre">test_cfg</span></code> into model in configs</p></li>
<li><p>Support to visualize results based on prediction quality</p></li>
</ul>
</section>
<section id="id88">
<h3>New Features<a class="headerlink" href="#id88" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2012.10150">SCNet</a> (#4356)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2011.12450">Sparse R-CNN</a> (#4219)</p></li>
<li><p>Support evaluate mAP by multiple IoUs (#4398)</p></li>
<li><p>Support concatenate dataset for testing (#4452)</p></li>
<li><p>Support to visualize results based on prediction quality (#4441)</p></li>
<li><p>Add ONNX simplify option to Pytorch2ONNX script (#4468)</p></li>
<li><p>Add hook for checking compatibility of class numbers in heads and datasets (#4508)</p></li>
</ul>
</section>
<section id="id89">
<h3>Bug Fixes<a class="headerlink" href="#id89" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix CPU inference bug of Cascade RPN (#4410)</p></li>
<li><p>Fix NMS error of CornerNet when there is no prediction box (#4409)</p></li>
<li><p>Fix TypeError in CornerNet inference (#4411)</p></li>
<li><p>Fix bug of PAA when training with background images (#4391)</p></li>
<li><p>Fix the error that the window data is not destroyed when <code class="docutils literal notranslate"><span class="pre">out_file</span> <span class="pre">is</span> <span class="pre">not</span> <span class="pre">None</span></code> and <code class="docutils literal notranslate"><span class="pre">show==False</span></code> (#4442)</p></li>
<li><p>Fix order of NMS <code class="docutils literal notranslate"><span class="pre">score_factor</span></code> that will decrease the performance of YOLOv3 (#4473)</p></li>
<li><p>Fix bug in HTC TTA when the number of detection boxes is 0 (#4516)</p></li>
<li><p>Fix resize error in mask data structures (#4520)</p></li>
</ul>
</section>
<section id="id90">
<h3>Improvements<a class="headerlink" href="#id90" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Allow to customize classes in LVIS dataset (#4382)</p></li>
<li><p>Add tutorials for building new models with existing datasets (#4396)</p></li>
<li><p>Add CPU compatibility information in documentation (#4405)</p></li>
<li><p>Add documentation of deprecated <code class="docutils literal notranslate"><span class="pre">ImageToTensor</span></code> for batch inference (#4408)</p></li>
<li><p>Add more details in documentation for customizing dataset (#4430)</p></li>
<li><p>Switch <code class="docutils literal notranslate"><span class="pre">imshow_det_bboxes</span></code> visualization backend from OpenCV to Matplotlib (#4389)</p></li>
<li><p>Deprecate <code class="docutils literal notranslate"><span class="pre">ImageToTensor</span></code> in <code class="docutils literal notranslate"><span class="pre">image_demo.py</span></code> (#4400)</p></li>
<li><p>Move train_cfg/test_cfg into model (#4347, #4489)</p></li>
<li><p>Update docstring for <code class="docutils literal notranslate"><span class="pre">reg_decoded_bbox</span></code> option in bbox heads (#4467)</p></li>
<li><p>Update dataset information in documentation (#4525)</p></li>
<li><p>Release pre-trained R50 and R101 PAA detectors with multi-scale 3x training schedules (#4495)</p></li>
<li><p>Add guidance for speed benchmark (#4537)</p></li>
</ul>
</section>
</section>
<section id="v2-8-0-04-01-2021">
<h2>v2.8.0 (04/01/2021)<a class="headerlink" href="#v2-8-0-04-01-2021" title="Permalink to this heading">¶</a></h2>
<section id="id91">
<h3>Highlights<a class="headerlink" href="#id91" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1909.06720">Cascade RPN</a>, <a class="reference external" href="https://arxiv.org/abs/1901.01892">TridentNet</a></p></li>
</ul>
</section>
<section id="id92">
<h3>New Features<a class="headerlink" href="#id92" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1909.06720">Cascade RPN</a> (#1900)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1901.01892">TridentNet</a> (#3313)</p></li>
</ul>
</section>
<section id="id93">
<h3>Bug Fixes<a class="headerlink" href="#id93" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix bug of show result in async_benchmark (#4367)</p></li>
<li><p>Fix scale factor in MaskTestMixin (#4366)</p></li>
<li><p>Fix but when returning indices in <code class="docutils literal notranslate"><span class="pre">multiclass_nms</span></code> (#4362)</p></li>
<li><p>Fix bug of empirical attention in resnext backbone error (#4300)</p></li>
<li><p>Fix bug of <code class="docutils literal notranslate"><span class="pre">img_norm_cfg</span></code> in FCOS-HRNet models with updated performance and models (#4250)</p></li>
<li><p>Fix invalid checkpoint and log in Mask R-CNN models on Cityscapes dataset (#4287)</p></li>
<li><p>Fix bug in distributed sampler when dataset is too small (#4257)</p></li>
<li><p>Fix bug of ‘PAFPN has no attribute extra_convs_on_inputs’ (#4235)</p></li>
</ul>
</section>
<section id="id94">
<h3>Improvements<a class="headerlink" href="#id94" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Update model url from aws to aliyun (#4349)</p></li>
<li><p>Update ATSS for PyTorch 1.6+ (#4359)</p></li>
<li><p>Update script to install ruby in pre-commit installation (#4360)</p></li>
<li><p>Delete deprecated <code class="docutils literal notranslate"><span class="pre">rsidet.ops</span></code> (#4325)</p></li>
<li><p>Refactor hungarian assigner for more general usage in Sparse R-CNN (#4259)</p></li>
<li><p>Handle scipy import in DETR to reduce package dependencies (#4339)</p></li>
<li><p>Update documentation of usages for config options after MMCV (1.2.3) supports overriding list in config (#4326)</p></li>
<li><p>Update pre-train models of faster rcnn trained on COCO subsets (#4307)</p></li>
<li><p>Avoid zero or too small value for beta in Dynamic R-CNN (#4303)</p></li>
<li><p>Add doccumentation for Pytorch2ONNX (#4271)</p></li>
<li><p>Add deprecated warning FPN arguments (#4264)</p></li>
<li><p>Support returning indices of kept bboxes when using nms (#4251)</p></li>
<li><p>Update type and device requirements when creating tensors <code class="docutils literal notranslate"><span class="pre">GFLHead</span></code> (#4210)</p></li>
<li><p>Update device requirements when creating tensors in <code class="docutils literal notranslate"><span class="pre">CrossEntropyLoss</span></code> (#4224)</p></li>
</ul>
</section>
</section>
<section id="v2-7-0-30-11-2020">
<h2>v2.7.0 (30/11/2020)<a class="headerlink" href="#v2-7-0-30-11-2020" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Support new method: <a class="reference external" href="https://arxiv.org/abs/2005.12872">DETR</a>, <a class="reference external" href="https://arxiv.org/abs/2004.08955">ResNest</a>, Faster R-CNN DC5.</p></li>
<li><p>Support YOLO, Mask R-CNN, and Cascade R-CNN models exportable to ONNX.</p></li>
</ul>
<section id="id95">
<h3>New Features<a class="headerlink" href="#id95" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2005.12872">DETR</a> (#4201, #4206)</p></li>
<li><p>Support to link the best checkpoint in training (#3773)</p></li>
<li><p>Support to override config through options in inference.py (#4175)</p></li>
<li><p>Support YOLO, Mask R-CNN, and Cascade R-CNN models exportable to ONNX (#4087, #4083)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2004.08955">ResNeSt</a> backbone (#2959)</p></li>
<li><p>Support unclip border bbox regression (#4076)</p></li>
<li><p>Add tpfp func in evaluating AP (#4069)</p></li>
<li><p>Support mixed precision training of SSD detector with other backbones (#4081)</p></li>
<li><p>Add Faster R-CNN DC5 models (#4043)</p></li>
</ul>
</section>
<section id="id96">
<h3>Bug Fixes<a class="headerlink" href="#id96" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix bug of <code class="docutils literal notranslate"><span class="pre">gpu_id</span></code> in distributed training mode (#4163)</p></li>
<li><p>Support Albumentations with version higher than 0.5 (#4032)</p></li>
<li><p>Fix num_classes bug in faster rcnn config (#4088)</p></li>
<li><p>Update code in docs/2_new_data_model.md (#4041)</p></li>
</ul>
</section>
<section id="id97">
<h3>Improvements<a class="headerlink" href="#id97" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Ensure DCN offset to have similar type as features in VFNet (#4198)</p></li>
<li><p>Add config links in README files of models (#4190)</p></li>
<li><p>Add tutorials for loss conventions (#3818)</p></li>
<li><p>Add solution to installation issues in 30-series GPUs (#4176)</p></li>
<li><p>Update docker version in get_started.md (#4145)</p></li>
<li><p>Add model statistics and polish some titles in configs README (#4140)</p></li>
<li><p>Clamp neg probability in FreeAnchor (#4082)</p></li>
<li><p>Speed up expanding large images (#4089)</p></li>
<li><p>Fix Pytorch 1.7 incompatibility issues (#4103)</p></li>
<li><p>Update trouble shooting page to resolve segmentation fault (#4055)</p></li>
<li><p>Update aLRP-Loss in project page (#4078)</p></li>
<li><p>Clean duplicated <code class="docutils literal notranslate"><span class="pre">reduce_mean</span></code> function (#4056)</p></li>
<li><p>Refactor Q&amp;A (#4045)</p></li>
</ul>
</section>
</section>
<section id="v2-6-0-1-11-2020">
<h2>v2.6.0 (1/11/2020)<a class="headerlink" href="#v2-6-0-1-11-2020" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Support new method: <a class="reference external" href="https://arxiv.org/abs/2008.13367">VarifocalNet</a>.</p></li>
<li><p>Refactored documentation with more tutorials.</p></li>
</ul>
<section id="id98">
<h3>New Features<a class="headerlink" href="#id98" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support GIoU calculation in <code class="docutils literal notranslate"><span class="pre">BboxOverlaps2D</span></code>, and re-implement <code class="docutils literal notranslate"><span class="pre">giou_loss</span></code> using <code class="docutils literal notranslate"><span class="pre">bbox_overlaps</span></code> (#3936)</p></li>
<li><p>Support random sampling in CPU mode (#3948)</p></li>
<li><p>Support VarifocalNet (#3666, #4024)</p></li>
</ul>
</section>
<section id="id99">
<h3>Bug Fixes<a class="headerlink" href="#id99" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix SABL validating bug in Cascade R-CNN (#3913)</p></li>
<li><p>Avoid division by zero in PAA head when num_pos=0 (#3938)</p></li>
<li><p>Fix temporary directory bug of multi-node testing error (#4034, #4017)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">--show-dir</span></code> option in test script (#4025)</p></li>
<li><p>Fix GA-RetinaNet r50 model url (#3983)</p></li>
<li><p>Update code in docs and fix broken urls (#3947)</p></li>
</ul>
</section>
<section id="id100">
<h3>Improvements<a class="headerlink" href="#id100" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Refactor pytorch2onnx API into <code class="docutils literal notranslate"><span class="pre">rsidet.core.export</span></code> and use <code class="docutils literal notranslate"><span class="pre">generate_inputs_and_wrap_model</span></code> for pytorch2onnx (#3857, #3912)</p></li>
<li><p>Update RPN upgrade scripts for v2.5.0 compatibility (#3986)</p></li>
<li><p>Use mmcv <code class="docutils literal notranslate"><span class="pre">tensor2imgs</span></code> (#4010)</p></li>
<li><p>Update test robustness (#4000)</p></li>
<li><p>Update trouble shooting page (#3994)</p></li>
<li><p>Accelerate PAA training speed (#3985)</p></li>
<li><p>Support batch_size &gt; 1 in validation (#3966)</p></li>
<li><p>Use RoIAlign implemented in MMCV for inference in CPU mode (#3930)</p></li>
<li><p>Documentation refactoring (#4031)</p></li>
</ul>
</section>
</section>
<section id="v2-5-0-5-10-2020">
<h2>v2.5.0 (5/10/2020)<a class="headerlink" href="#v2-5-0-5-10-2020" title="Permalink to this heading">¶</a></h2>
<section id="id101">
<h3>Highlights<a class="headerlink" href="#id101" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1904.02689">YOLACT</a>, <a class="reference external" href="https://arxiv.org/abs/2003.09119">CentripetalNet</a>.</p></li>
<li><p>Add more documentations for easier and more clear usage.</p></li>
</ul>
</section>
<section id="id102">
<h3>Backwards Incompatible Changes<a class="headerlink" href="#id102" title="Permalink to this heading">¶</a></h3>
<p><strong>FP16 related methods are imported from mmcv instead of rsidet. (#3766, #3822)</strong>
Mixed precision training utils in <code class="docutils literal notranslate"><span class="pre">rsidet.core.fp16</span></code> are moved to <code class="docutils literal notranslate"><span class="pre">mmcv.runner</span></code>, including <code class="docutils literal notranslate"><span class="pre">force_fp32</span></code>, <code class="docutils literal notranslate"><span class="pre">auto_fp16</span></code>, <code class="docutils literal notranslate"><span class="pre">wrap_fp16_model</span></code>, and <code class="docutils literal notranslate"><span class="pre">Fp16OptimizerHook</span></code>. A deprecation warning will be raised if users attempt to import those methods from <code class="docutils literal notranslate"><span class="pre">rsidet.core.fp16</span></code>, and will be finally removed in V2.10.0.</p>
<p><strong>[0, N-1] represents foreground classes and N indicates background classes for all models. (#3221)</strong>
Before v2.5.0, the background label for RPN is 0, and N for other heads. Now the behavior is consistent for all models. Thus <code class="docutils literal notranslate"><span class="pre">self.background_labels</span></code> in <code class="docutils literal notranslate"><span class="pre">dense_heads</span></code> is removed and all heads use <code class="docutils literal notranslate"><span class="pre">self.num_classes</span></code> to indicate the class index of background labels.
This change has no effect on the pre-trained models in the v2.x model zoo, but will affect the training of all models with RPN heads. Two-stage detectors whose RPN head uses softmax will be affected because the order of categories is changed.</p>
<p><strong>Only call <code class="docutils literal notranslate"><span class="pre">get_subset_by_classes</span></code> when <code class="docutils literal notranslate"><span class="pre">test_mode=True</span></code> and <code class="docutils literal notranslate"><span class="pre">self.filter_empty_gt=True</span></code> (#3695)</strong>
Function <code class="docutils literal notranslate"><span class="pre">get_subset_by_classes</span></code> in dataset is refactored and only filters out images when <code class="docutils literal notranslate"><span class="pre">test_mode=True</span></code> and <code class="docutils literal notranslate"><span class="pre">self.filter_empty_gt=True</span></code>.
In the original implementation, <code class="docutils literal notranslate"><span class="pre">get_subset_by_classes</span></code> is not related to the flag <code class="docutils literal notranslate"><span class="pre">self.filter_empty_gt</span></code> and will only be called when the classes is set during initialization no matter <code class="docutils literal notranslate"><span class="pre">test_mode</span></code> is <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code>. This brings ambiguous behavior and potential bugs in many cases. After v2.5.0, if <code class="docutils literal notranslate"><span class="pre">filter_empty_gt=False</span></code>, no matter whether the classes are specified in a dataset, the dataset will use all the images in the annotations. If <code class="docutils literal notranslate"><span class="pre">filter_empty_gt=True</span></code> and <code class="docutils literal notranslate"><span class="pre">test_mode=True</span></code>, no matter whether the classes are specified, the dataset will call ``get_subset_by_classes` to check the images and filter out images containing no GT boxes. Therefore, the users should be responsible for the data filtering/cleaning process for the test dataset.</p>
</section>
<section id="id103">
<h3>New Features<a class="headerlink" href="#id103" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Test time augmentation for single stage detectors (#3844, #3638)</p></li>
<li><p>Support to show the name of experiments during training (#3764)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">Shear</span></code>, <code class="docutils literal notranslate"><span class="pre">Rotate</span></code>, <code class="docutils literal notranslate"><span class="pre">Translate</span></code> Augmentation (#3656, #3619, #3687)</p></li>
<li><p>Add image-only transformations including <code class="docutils literal notranslate"><span class="pre">Constrast</span></code>, <code class="docutils literal notranslate"><span class="pre">Equalize</span></code>, <code class="docutils literal notranslate"><span class="pre">Color</span></code>, and <code class="docutils literal notranslate"><span class="pre">Brightness</span></code>. (#3643)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1904.02689">YOLACT</a> (#3456)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2003.09119">CentripetalNet</a> (#3390)</p></li>
<li><p>Support PyTorch 1.6 in docker (#3905)</p></li>
</ul>
</section>
<section id="id104">
<h3>Bug Fixes<a class="headerlink" href="#id104" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Fix the bug of training ATSS when there is no ground truth boxes (#3702)</p></li>
<li><p>Fix the bug of using Focal Loss when there is <code class="docutils literal notranslate"><span class="pre">num_pos</span></code> is 0 (#3702)</p></li>
<li><p>Fix the label index mapping in dataset browser (#3708)</p></li>
<li><p>Fix Mask R-CNN training stuck problem when their is no positive rois (#3713)</p></li>
<li><p>Fix the bug of <code class="docutils literal notranslate"><span class="pre">self.rpn_head.test_cfg</span></code> in <code class="docutils literal notranslate"><span class="pre">RPNTestMixin</span></code> by using <code class="docutils literal notranslate"><span class="pre">self.rpn_head</span></code> in rpn head (#3808)</p></li>
<li><p>Fix deprecated <code class="docutils literal notranslate"><span class="pre">Conv2d</span></code> from mmcv.ops (#3791)</p></li>
<li><p>Fix device bug in RepPoints (#3836)</p></li>
<li><p>Fix SABL validating bug (#3849)</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">https://download.openmmlab.com/mmcv/dist/index.html</span></code> for installing MMCV (#3840)</p></li>
<li><p>Fix nonzero in NMS for PyTorch 1.6.0 (#3867)</p></li>
<li><p>Fix the API change bug of PAA (#3883)</p></li>
<li><p>Fix typo in bbox_flip (#3886)</p></li>
<li><p>Fix cv2 import error of ligGL.so.1 in Dockerfile (#3891)</p></li>
</ul>
</section>
<section id="id105">
<h3>Improvements<a class="headerlink" href="#id105" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p>Change to use <code class="docutils literal notranslate"><span class="pre">mmcv.utils.collect_env</span></code> for collecting environment information to avoid duplicate codes (#3779)</p></li>
<li><p>Update checkpoint file names to v2.0 models in documentation (#3795)</p></li>
<li><p>Update tutorials for changing runtime settings (#3778), modifying loss (#3777)</p></li>
<li><p>Improve the function of <code class="docutils literal notranslate"><span class="pre">simple_test_bboxes</span></code> in SABL (#3853)</p></li>
<li><p>Convert mask to bool before using it as img’s index for robustness and speedup (#3870)</p></li>
<li><p>Improve documentation of modules and dataset customization (#3821)</p></li>
</ul>
</section>
</section>
<section id="v2-4-0-5-9-2020">
<h2>v2.4.0 (5/9/2020)<a class="headerlink" href="#v2-4-0-5-9-2020" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Fix lots of issues/bugs and reorganize the trouble shooting page</p></li>
<li><p>Support new methods <a class="reference external" href="https://arxiv.org/abs/1912.04260">SABL</a>, <a class="reference external" href="https://arxiv.org/abs/1804.02767">YOLOv3</a>, and <a class="reference external" href="https://arxiv.org/abs/2007.08103">PAA Assign</a></p></li>
<li><p>Support Batch Inference</p></li>
<li><p>Start to publish <code class="docutils literal notranslate"><span class="pre">rsidet</span></code> package to PyPI since v2.3.0</p></li>
<li><p>Switch model zoo to download.openmmlab.com</p></li>
</ul>
<p><strong>Backwards Incompatible Changes</strong></p>
<ul class="simple">
<li><p>Support Batch Inference (#3564, #3686, #3705): Since v2.4.0, MMDetection could inference model with multiple images in a single GPU.
This change influences all the test APIs in MMDetection and downstream codebases. To help the users migrate their code, we use <code class="docutils literal notranslate"><span class="pre">replace_ImageToTensor</span></code> (#3686) to convert legacy test data pipelines during dataset initialization.</p></li>
<li><p>Support RandomFlip with horizontal/vertical/diagonal direction (#3608): Since v2.4.0, MMDetection supports horizontal/vertical/diagonal flip in the data augmentation. This influences bounding box, mask, and image transformations in data augmentation process and the process that will map those data back to the original format.</p></li>
<li><p>Migrate to use <code class="docutils literal notranslate"><span class="pre">mmlvis</span></code> and <code class="docutils literal notranslate"><span class="pre">mmpycocotools</span></code> for COCO and LVIS dataset (#3727). The APIs are fully compatible with the original <code class="docutils literal notranslate"><span class="pre">lvis</span></code> and <code class="docutils literal notranslate"><span class="pre">pycocotools</span></code>. Users need to uninstall the existing pycocotools and lvis packages in their environment first and install <code class="docutils literal notranslate"><span class="pre">mmlvis</span></code> &amp; <code class="docutils literal notranslate"><span class="pre">mmpycocotools</span></code>.</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix default mean/std for onnx (#3491)</p></li>
<li><p>Fix coco evaluation and add metric items (#3497)</p></li>
<li><p>Fix typo for install.md (#3516)</p></li>
<li><p>Fix atss when sampler per gpu is 1 (#3528)</p></li>
<li><p>Fix import of fuse_conv_bn (#3529)</p></li>
<li><p>Fix bug of gaussian_target, update unittest of heatmap (#3543)</p></li>
<li><p>Fixed VOC2012 evaluate (#3553)</p></li>
<li><p>Fix scale factor bug of rescale (#3566)</p></li>
<li><p>Fix with_xxx_attributes in base detector (#3567)</p></li>
<li><p>Fix boxes scaling when number is 0 (#3575)</p></li>
<li><p>Fix rfp check when neck config is a list (#3591)</p></li>
<li><p>Fix import of fuse conv bn in benchmark.py (#3606)</p></li>
<li><p>Fix webcam demo (#3634)</p></li>
<li><p>Fix typo and itemize issues in tutorial (#3658)</p></li>
<li><p>Fix error in distributed training when some levels of FPN are not assigned with bounding boxes (#3670)</p></li>
<li><p>Fix the width and height orders of stride in valid flag generation (#3685)</p></li>
<li><p>Fix weight initialization bug in Res2Net DCN (#3714)</p></li>
<li><p>Fix bug in OHEMSampler (#3677)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Support Cutout augmentation (#3521)</p></li>
<li><p>Support evaluation on multiple datasets through ConcatDataset (#3522)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/2007.08103">PAA assign</a> #(3547)</p></li>
<li><p>Support eval metric with pickle results (#3607)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1804.02767">YOLOv3</a> (#3083)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1912.04260">SABL</a> (#3603)</p></li>
<li><p>Support to publish to Pypi in github-action (#3510)</p></li>
<li><p>Support custom imports (#3641)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Refactor common issues in documentation (#3530)</p></li>
<li><p>Add pytorch 1.6 to CI config (#3532)</p></li>
<li><p>Add config to runner meta (#3534)</p></li>
<li><p>Add eval-option flag for testing (#3537)</p></li>
<li><p>Add init_eval to evaluation hook (#3550)</p></li>
<li><p>Add include_bkg in ClassBalancedDataset (#3577)</p></li>
<li><p>Using config’s loading in inference_detector (#3611)</p></li>
<li><p>Add ATSS ResNet-101 models in model zoo (#3639)</p></li>
<li><p>Update urls to download.openmmlab.com (#3665)</p></li>
<li><p>Support non-mask training for CocoDataset (#3711)</p></li>
</ul>
</section>
<section id="v2-3-0-5-8-2020">
<h2>v2.3.0 (5/8/2020)<a class="headerlink" href="#v2-3-0-5-8-2020" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>The CUDA/C++ operators have been moved to <code class="docutils literal notranslate"><span class="pre">mmcv.ops</span></code>. For backward compatibility <code class="docutils literal notranslate"><span class="pre">rsidet.ops</span></code> is kept as warppers of <code class="docutils literal notranslate"><span class="pre">mmcv.ops</span></code>.</p></li>
<li><p>Support new methods <a class="reference external" href="https://arxiv.org/abs/1808.01244">CornerNet</a>, <a class="reference external" href="https://arxiv.org/abs/1911.08287">DIOU</a>/<a class="reference external" href="https://arxiv.org/abs/2005.03572">CIOU</a> loss, and new dataset: <a class="reference external" href="https://arxiv.org/abs/1908.03195">LVIS V1</a></p></li>
<li><p>Provide more detailed colab training tutorials and more complete documentation.</p></li>
<li><p>Support to convert RetinaNet from Pytorch to ONNX.</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix the model initialization bug of DetectoRS (#3187)</p></li>
<li><p>Fix the bug of module names in NASFCOSHead (#3205)</p></li>
<li><p>Fix the filename bug in publish_model.py (#3237)</p></li>
<li><p>Fix the dimensionality bug when <code class="docutils literal notranslate"><span class="pre">inside_flags.any()</span></code> is <code class="docutils literal notranslate"><span class="pre">False</span></code> in dense heads (#3242)</p></li>
<li><p>Fix the bug of forgetting to pass flip directions in <code class="docutils literal notranslate"><span class="pre">MultiScaleFlipAug</span></code> (#3262)</p></li>
<li><p>Fixed the bug caused by default value of <code class="docutils literal notranslate"><span class="pre">stem_channels</span></code> (#3333)</p></li>
<li><p>Fix the bug of model checkpoint loading for CPU inference (#3318, #3316)</p></li>
<li><p>Fix topk bug when box number is smaller than the expected topk number in ATSSAssigner (#3361)</p></li>
<li><p>Fix the gt priority bug in center_region_assigner.py (#3208)</p></li>
<li><p>Fix NaN issue of iou calculation in iou_loss.py (#3394)</p></li>
<li><p>Fix the bug that <code class="docutils literal notranslate"><span class="pre">iou_thrs</span></code> is not actually used during evaluation in coco.py (#3407)</p></li>
<li><p>Fix test-time augmentation of RepPoints (#3435)</p></li>
<li><p>Fix runtimeError caused by incontiguous tensor in Res2Net+DCN (#3412)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1808.01244">CornerNet</a> (#3036)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1911.08287">DIOU</a>/<a class="reference external" href="https://arxiv.org/abs/2005.03572">CIOU</a> loss (#3151)</p></li>
<li><p>Support <a class="reference external" href="https://arxiv.org/abs/1908.03195">LVIS V1</a> dataset (#)</p></li>
<li><p>Support customized hooks in training (#3395)</p></li>
<li><p>Support fp16 training of generalized focal loss (#3410)</p></li>
<li><p>Support to convert RetinaNet from Pytorch to ONNX (#3075)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Support to process ignore boxes in ATSS assigner (#3082)</p></li>
<li><p>Allow to crop images without ground truth in <code class="docutils literal notranslate"><span class="pre">RandomCrop</span></code> (#3153)</p></li>
<li><p>Enable the the <code class="docutils literal notranslate"><span class="pre">Accuracy</span></code> module to set threshold (#3155)</p></li>
<li><p>Refactoring unit tests (#3206)</p></li>
<li><p>Unify the training settings of <code class="docutils literal notranslate"><span class="pre">to_float32</span></code> and <code class="docutils literal notranslate"><span class="pre">norm_cfg</span></code> in RegNets configs (#3210)</p></li>
<li><p>Add colab training tutorials for beginners (#3213, #3273)</p></li>
<li><p>Move CUDA/C++ operators into <code class="docutils literal notranslate"><span class="pre">mmcv.ops</span></code> and keep <code class="docutils literal notranslate"><span class="pre">rsidet.ops</span></code> as warppers for backward compatibility (#3232)(#3457)</p></li>
<li><p>Update installation scripts in documentation (#3290) and dockerfile (#3320)</p></li>
<li><p>Support to set image resize backend (#3392)</p></li>
<li><p>Remove git hash in version file (#3466)</p></li>
<li><p>Check mmcv version to force version compatibility (#3460)</p></li>
</ul>
</section>
<section id="v2-2-0-1-7-2020">
<h2>v2.2.0 (1/7/2020)<a class="headerlink" href="#v2-2-0-1-7-2020" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/2006.02334">DetectoRS</a>, <a class="reference external" href="https://arxiv.org/abs/1912.08193">PointRend</a>, <a class="reference external" href="https://arxiv.org/abs/2006.04388">Generalized Focal Loss</a>, <a class="reference external" href="https://arxiv.org/abs/2004.06002">Dynamic R-CNN</a></p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix FreeAnchor when no gt in image (#3176)</p></li>
<li><p>Clean up deprecated usage of <code class="docutils literal notranslate"><span class="pre">register_module()</span></code> (#3092, #3161)</p></li>
<li><p>Fix pretrain bug in NAS FCOS (#3145)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> in SSD (#3142)</p></li>
<li><p>Fix FCOS warmup (#3119)</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">rstrip</span></code> in <code class="docutils literal notranslate"><span class="pre">tools/publish_model.py</span></code></p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">flip_ratio</span></code> default value in RandomFLip pipeline (#3106)</p></li>
<li><p>Fix cityscapes eval with ms_rcnn (#3112)</p></li>
<li><p>Fix RPN softmax (#3056)</p></li>
<li><p>Fix filename of LVIS&#64;v0.5 (#2998)</p></li>
<li><p>Fix nan loss by filtering out-of-frame gt_bboxes in COCO (#2999)</p></li>
<li><p>Fix bug in FSAF (#3018)</p></li>
<li><p>Add FocalLoss <code class="docutils literal notranslate"><span class="pre">num_classes</span></code> check (#2964)</p></li>
<li><p>Fix PISA Loss when there are no gts (#2992)</p></li>
<li><p>Avoid nan in <code class="docutils literal notranslate"><span class="pre">iou_calculator</span></code> (#2975)</p></li>
<li><p>Prevent possible bugs in loading and transforms caused by shallow copy (#2967)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Add DetectoRS (#3064)</p></li>
<li><p>Support Generalize Focal Loss (#3097)</p></li>
<li><p>Support PointRend (#2752)</p></li>
<li><p>Support Dynamic R-CNN (#3040)</p></li>
<li><p>Add DeepFashion dataset (#2968)</p></li>
<li><p>Implement FCOS training tricks (#2935)</p></li>
<li><p>Use BaseDenseHead as base class for anchor-base heads (#2963)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">with_cp</span></code> for BasicBlock (#2891)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">stem_channels</span></code> argument for ResNet (#2954)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Add anchor free base head (#2867)</p></li>
<li><p>Migrate to github action (#3137)</p></li>
<li><p>Add docstring for datasets, pipelines, core modules and methods (#3130, #3125, #3120)</p></li>
<li><p>Add VOC benchmark (#3060)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">concat</span></code> mode in GRoI (#3098)</p></li>
<li><p>Remove cmd arg <code class="docutils literal notranslate"><span class="pre">autorescale-lr</span></code> (#3080)</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">len(data['img_metas'])</span></code> to indicate <code class="docutils literal notranslate"><span class="pre">num_samples</span></code> (#3073, #3053)</p></li>
<li><p>Switch to EpochBasedRunner (#2976)</p></li>
</ul>
</section>
<section id="v2-1-0-8-6-2020">
<h2>v2.1.0 (8/6/2020)<a class="headerlink" href="#v2-1-0-8-6-2020" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Support new backbones: <a class="reference external" href="https://arxiv.org/abs/2003.13678">RegNetX</a>, <a class="reference external" href="https://arxiv.org/abs/1904.01169">Res2Net</a></p></li>
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1906.04423">NASFCOS</a>, <a class="reference external" href="https://arxiv.org/abs/1904.04821">PISA</a>, <a class="reference external" href="https://arxiv.org/abs/2004.13665">GRoIE</a></p></li>
<li><p>Support new dataset: <a class="reference external" href="https://arxiv.org/abs/1908.03195">LVIS</a></p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Change the CLI argument <code class="docutils literal notranslate"><span class="pre">--validate</span></code> to <code class="docutils literal notranslate"><span class="pre">--no-validate</span></code> to enable validation after training epochs by default. (#2651)</p></li>
<li><p>Add missing cython to docker file (#2713)</p></li>
<li><p>Fix bug in nms cpu implementation (#2754)</p></li>
<li><p>Fix bug when showing mask results (#2763)</p></li>
<li><p>Fix gcc requirement (#2806)</p></li>
<li><p>Fix bug in async test (#2820)</p></li>
<li><p>Fix mask encoding-decoding bugs in test API (#2824)</p></li>
<li><p>Fix bug in test time augmentation (#2858, #2921, #2944)</p></li>
<li><p>Fix a typo in comment of apis/train (#2877)</p></li>
<li><p>Fix the bug of returning None when no gt bboxes are in the original image in <code class="docutils literal notranslate"><span class="pre">RandomCrop</span></code>. Fix the bug that misses to handle <code class="docutils literal notranslate"><span class="pre">gt_bboxes_ignore</span></code>, <code class="docutils literal notranslate"><span class="pre">gt_label_ignore</span></code>, and <code class="docutils literal notranslate"><span class="pre">gt_masks_ignore</span></code> in <code class="docutils literal notranslate"><span class="pre">RandomCrop</span></code>, <code class="docutils literal notranslate"><span class="pre">MinIoURandomCrop</span></code> and <code class="docutils literal notranslate"><span class="pre">Expand</span></code> modules. (#2810)</p></li>
<li><p>Fix bug of <code class="docutils literal notranslate"><span class="pre">base_channels</span></code> of regnet (#2917)</p></li>
<li><p>Fix the bug of logger when loading pre-trained weights in base detector (#2936)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Add IoU models (#2666)</p></li>
<li><p>Add colab demo for inference</p></li>
<li><p>Support class agnostic nms (#2553)</p></li>
<li><p>Add benchmark gathering scripts for development only (#2676)</p></li>
<li><p>Add rsidet-based project links (#2736, #2767, #2895)</p></li>
<li><p>Add config dump in training (#2779)</p></li>
<li><p>Add ClassBalancedDataset (#2721)</p></li>
<li><p>Add res2net backbone (#2237)</p></li>
<li><p>Support RegNetX models (#2710)</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">mmcv.FileClient</span></code> to support different storage backends (#2712)</p></li>
<li><p>Add ClassBalancedDataset (#2721)</p></li>
<li><p>Code Release: Prime Sample Attention in Object Detection (CVPR 2020) (#2626)</p></li>
<li><p>Implement NASFCOS (#2682)</p></li>
<li><p>Add class weight in CrossEntropyLoss (#2797)</p></li>
<li><p>Support LVIS dataset (#2088)</p></li>
<li><p>Support GRoIE (#2584)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Allow different x and y strides in anchor heads. (#2629)</p></li>
<li><p>Make FSAF loss more robust to no gt (#2680)</p></li>
<li><p>Compute pure inference time instead (#2657) and update inference speed (#2730)</p></li>
<li><p>Avoided the possibility that a patch with 0 area is cropped. (#2704)</p></li>
<li><p>Add warnings when deprecated <code class="docutils literal notranslate"><span class="pre">imgs_per_gpu</span></code> is used. (#2700)</p></li>
<li><p>Add a mask rcnn example for config (#2645)</p></li>
<li><p>Update model zoo (#2762, #2866, #2876, #2879, #2831)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">ori_filename</span></code> to img_metas and use it in test show-dir (#2612)</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">img_fields</span></code> to handle multiple images during image transform (#2800)</p></li>
<li><p>Add upsample_cfg support in FPN (#2787)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">['img']</span></code> as default <code class="docutils literal notranslate"><span class="pre">img_fields</span></code> for back compatibility (#2809)</p></li>
<li><p>Rename the pretrained model from <code class="docutils literal notranslate"><span class="pre">open-mmlab://resnet50_caffe</span></code> and <code class="docutils literal notranslate"><span class="pre">open-mmlab://resnet50_caffe_bgr</span></code> to <code class="docutils literal notranslate"><span class="pre">open-mmlab://detectron/resnet50_caffe</span></code> and <code class="docutils literal notranslate"><span class="pre">open-mmlab://detectron2/resnet50_caffe</span></code>. (#2832)</p></li>
<li><p>Added sleep(2) in test.py to reduce hanging problem (#2847)</p></li>
<li><p>Support <code class="docutils literal notranslate"><span class="pre">c10::half</span></code> in CARAFE (#2890)</p></li>
<li><p>Improve documentations (#2918, #2714)</p></li>
<li><p>Use optimizer constructor in mmcv and clean the original implementation in <code class="docutils literal notranslate"><span class="pre">rsidet.core.optimizer</span></code> (#2947)</p></li>
</ul>
</section>
<section id="v2-0-0-6-5-2020">
<h2>v2.0.0 (6/5/2020)<a class="headerlink" href="#v2-0-0-6-5-2020" title="Permalink to this heading">¶</a></h2>
<p>In this release, we made lots of major refactoring and modifications.</p>
<ol class="arabic simple">
<li><p><strong>Faster speed</strong>. We optimize the training and inference speed for common models, achieving up to 30% speedup for training and 25% for inference. Please refer to <a class="reference internal" href="model_zoo.html#comparison-with-detectron2"><span class="std std-doc">model zoo</span></a> for details.</p></li>
<li><p><strong>Higher performance</strong>. We change some default hyperparameters with no additional cost, which leads to a gain of performance for most models. Please refer to <a class="reference internal" href="compatibility.html#training-hyperparameters"><span class="std std-doc">compatibility</span></a> for details.</p></li>
<li><p><strong>More documentation and tutorials</strong>. We add a bunch of documentation and tutorials to help users get started more smoothly. Read it <a class="reference external" href="https://rsidetection.readthedocs.io/en/latest/">here</a>.</p></li>
<li><p><strong>Support PyTorch 1.5</strong>. The support for 1.1 and 1.2 is dropped, and we switch to some new APIs.</p></li>
<li><p><strong>Better configuration system</strong>. Inheritance is supported to reduce the redundancy of configs.</p></li>
<li><p><strong>Better modular design</strong>. Towards the goal of simplicity and flexibility, we simplify some encapsulation while add more other configurable modules like BBoxCoder, IoUCalculator, OptimizerConstructor, RoIHead. Target computation is also included in heads and the call hierarchy is simpler.</p></li>
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1903.00621">FSAF</a> and PAFPN (part of <a class="reference external" href="https://arxiv.org/abs/1803.01534">PAFPN</a>).</p></li>
</ol>
<p><strong>Breaking Changes</strong>
Models training with MMDetection 1.x are not fully compatible with 2.0, please refer to the <a class="reference internal" href="compatibility.html"><span class="doc std std-doc">compatibility doc</span></a> for the details and how to migrate to the new version.</p>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Unify cuda and cpp API for custom ops. (#2277)</p></li>
<li><p>New config files with inheritance. (#2216)</p></li>
<li><p>Encapsulate the second stage into RoI heads. (#1999)</p></li>
<li><p>Refactor GCNet/EmpericalAttention into plugins. (#2345)</p></li>
<li><p>Set low quality match as an option in IoU-based bbox assigners. (#2375)</p></li>
<li><p>Change the codebase’s coordinate system. (#2380)</p></li>
<li><p>Refactor the category order in heads. 0 means the first positive class instead of background now. (#2374)</p></li>
<li><p>Add bbox sampler and assigner registry. (#2419)</p></li>
<li><p>Speed up the inference of RPN. (#2420)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">train_cfg</span></code> and <code class="docutils literal notranslate"><span class="pre">test_cfg</span></code> as class members in all anchor heads. (#2422)</p></li>
<li><p>Merge target computation methods into heads. (#2429)</p></li>
<li><p>Add bbox coder to support different bbox encoding and losses. (#2480)</p></li>
<li><p>Unify the API for regression loss. (#2156)</p></li>
<li><p>Refactor Anchor Generator. (#2474)</p></li>
<li><p>Make <code class="docutils literal notranslate"><span class="pre">lr</span></code> an optional argument for optimizers. (#2509)</p></li>
<li><p>Migrate to modules and methods in MMCV. (#2502, #2511, #2569, #2572)</p></li>
<li><p>Support PyTorch 1.5. (#2524)</p></li>
<li><p>Drop the support for Python 3.5 and use F-string in the codebase. (#2531)</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix the scale factors for resized images without keep the aspect ratio. (#2039)</p></li>
<li><p>Check if max_num &gt; 0 before slicing in NMS. (#2486)</p></li>
<li><p>Fix Deformable RoIPool when there is no instance. (#2490)</p></li>
<li><p>Fix the default value of assigned labels. (#2536)</p></li>
<li><p>Fix the evaluation of Cityscapes. (#2578)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Add deep_stem and avg_down option to ResNet, i.e., support ResNetV1d. (#2252)</p></li>
<li><p>Add L1 loss. (#2376)</p></li>
<li><p>Support both polygon and bitmap for instance masks. (#2353, #2540)</p></li>
<li><p>Support CPU mode for inference. (#2385)</p></li>
<li><p>Add optimizer constructor for complicated configuration of optimizers. (#2397, #2488)</p></li>
<li><p>Implement PAFPN. (#2392)</p></li>
<li><p>Support empty tensor input for some modules. (#2280)</p></li>
<li><p>Support for custom dataset classes without overriding it. (#2408, #2443)</p></li>
<li><p>Support to train subsets of coco dataset. (#2340)</p></li>
<li><p>Add iou_calculator to potentially support more IoU calculation methods. (2405)</p></li>
<li><p>Support class wise mean AP (was removed in the last version). (#2459)</p></li>
<li><p>Add option to save the testing result images. (#2414)</p></li>
<li><p>Support MomentumUpdaterHook. (#2571)</p></li>
<li><p>Add a demo to inference a single image. (#2605)</p></li>
</ul>
</section>
<section id="v1-1-0-24-2-2020">
<h2>v1.1.0 (24/2/2020)<a class="headerlink" href="#v1-1-0-24-2-2020" title="Permalink to this heading">¶</a></h2>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Dataset evaluation is rewritten with a unified api, which is used by both evaluation hooks and test scripts.</p></li>
<li><p>Support new methods: <a class="reference external" href="https://arxiv.org/abs/1905.02188">CARAFE</a>.</p></li>
</ul>
<p><strong>Breaking Changes</strong></p>
<ul class="simple">
<li><p>The new MMDDP inherits from the official DDP, thus the <code class="docutils literal notranslate"><span class="pre">__init__</span></code> api is changed to be the same as official DDP.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">mask_head</span></code> field in HTC config files is modified.</p></li>
<li><p>The evaluation and testing script is updated.</p></li>
<li><p>In all transforms, instance masks are stored as a numpy array shaped (n, h, w) instead of a list of (h, w) arrays, where n is the number of instances.</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix IOU assigners when ignore_iof_thr &gt; 0 and there is no pred boxes. (#2135)</p></li>
<li><p>Fix mAP evaluation when there are no ignored boxes. (#2116)</p></li>
<li><p>Fix the empty RoI input for Deformable RoI Pooling. (#2099)</p></li>
<li><p>Fix the dataset settings for multiple workflows. (#2103)</p></li>
<li><p>Fix the warning related to <code class="docutils literal notranslate"><span class="pre">torch.uint8</span></code> in PyTorch 1.4. (#2105)</p></li>
<li><p>Fix the inference demo on devices other than gpu:0. (#2098)</p></li>
<li><p>Fix Dockerfile. (#2097)</p></li>
<li><p>Fix the bug that <code class="docutils literal notranslate"><span class="pre">pad_val</span></code> is unused in Pad transform. (#2093)</p></li>
<li><p>Fix the albumentation transform when there is no ground truth bbox. (#2032)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Use torch instead of numpy for random sampling. (#2094)</p></li>
<li><p>Migrate to the new MMDDP implementation in MMCV v0.3. (#2090)</p></li>
<li><p>Add meta information in logs. (#2086)</p></li>
<li><p>Rewrite Soft NMS with pytorch extension and remove cython as a dependency. (#2056)</p></li>
<li><p>Rewrite dataset evaluation. (#2042, #2087, #2114, #2128)</p></li>
<li><p>Use numpy array for masks in transforms. (#2030)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Implement “CARAFE: Content-Aware ReAssembly of FEatures”. (#1583)</p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">worker_init_fn()</span></code> in data_loader when seed is set. (#2066, #2111)</p></li>
<li><p>Add logging utils. (#2035)</p></li>
</ul>
</section>
<section id="v1-0-0-30-1-2020">
<h2>v1.0.0 (30/1/2020)<a class="headerlink" href="#v1-0-0-30-1-2020" title="Permalink to this heading">¶</a></h2>
<p>This release mainly improves the code quality and add more docstrings.</p>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Documentation is online now: <a class="reference external" href="https://rsidetection.readthedocs.io">https://rsidetection.readthedocs.io</a>.</p></li>
<li><p>Support new models: <a class="reference external" href="https://arxiv.org/abs/1912.02424">ATSS</a>.</p></li>
<li><p>DCN is now available with the api <code class="docutils literal notranslate"><span class="pre">build_conv_layer</span></code> and <code class="docutils literal notranslate"><span class="pre">ConvModule</span></code> like the normal conv layer.</p></li>
<li><p>A tool to collect environment information is available for trouble shooting.</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix the incompatibility of the latest numpy and pycocotools. (#2024)</p></li>
<li><p>Fix the case when distributed package is unavailable, e.g., on Windows. (#1985)</p></li>
<li><p>Fix the dimension issue for <code class="docutils literal notranslate"><span class="pre">refine_bboxes()</span></code>. (#1962)</p></li>
<li><p>Fix the typo when <code class="docutils literal notranslate"><span class="pre">seg_prefix</span></code> is a list. (#1906)</p></li>
<li><p>Add segmentation map cropping to RandomCrop. (#1880)</p></li>
<li><p>Fix the return value of <code class="docutils literal notranslate"><span class="pre">ga_shape_target_single()</span></code>. (#1853)</p></li>
<li><p>Fix the loaded shape of empty proposals. (#1819)</p></li>
<li><p>Fix the mask data type when using albumentation. (#1818)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Enhance AssignResult and SamplingResult. (#1995)</p></li>
<li><p>Add ability to overwrite existing module in Registry. (#1982)</p></li>
<li><p>Reorganize requirements and make albumentations and imagecorruptions optional. (#1969)</p></li>
<li><p>Check NaN in <code class="docutils literal notranslate"><span class="pre">SSDHead</span></code>. (#1935)</p></li>
<li><p>Encapsulate the DCN in ResNe(X)t into a ConvModule &amp; Conv_layers. (#1894)</p></li>
<li><p>Refactoring for mAP evaluation and support multiprocessing and logging. (#1889)</p></li>
<li><p>Init the root logger before constructing Runner to log more information. (#1865)</p></li>
<li><p>Split <code class="docutils literal notranslate"><span class="pre">SegResizeFlipPadRescale</span></code> into different existing transforms. (#1852)</p></li>
<li><p>Move <code class="docutils literal notranslate"><span class="pre">init_dist()</span></code> to MMCV. (#1851)</p></li>
<li><p>Documentation and docstring improvements. (#1971, #1938, #1869, #1838)</p></li>
<li><p>Fix the color of the same class for mask visualization. (#1834)</p></li>
<li><p>Remove the option <code class="docutils literal notranslate"><span class="pre">keep_all_stages</span></code> in HTC and Cascade R-CNN. (#1806)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Add two test-time options <code class="docutils literal notranslate"><span class="pre">crop_mask</span></code> and <code class="docutils literal notranslate"><span class="pre">rle_mask_encode</span></code> for mask heads. (#2013)</p></li>
<li><p>Support loading grayscale images as single channel. (#1975)</p></li>
<li><p>Implement “Bridging the Gap Between Anchor-based and Anchor-free Detection via Adaptive Training Sample Selection”. (#1872)</p></li>
<li><p>Add sphinx generated docs. (#1859, #1864)</p></li>
<li><p>Add GN support for flops computation. (#1850)</p></li>
<li><p>Collect env info for trouble shooting. (#1812)</p></li>
</ul>
</section>
<section id="v1-0rc1-13-12-2019">
<h2>v1.0rc1 (13/12/2019)<a class="headerlink" href="#v1-0rc1-13-12-2019" title="Permalink to this heading">¶</a></h2>
<p>The RC1 release mainly focuses on improving the user experience, and fixing bugs.</p>
<p><strong>Highlights</strong></p>
<ul class="simple">
<li><p>Support new models: <a class="reference external" href="https://arxiv.org/abs/1904.03797">FoveaBox</a>, <a class="reference external" href="https://arxiv.org/abs/1904.11490">RepPoints</a> and <a class="reference external" href="https://arxiv.org/abs/1909.02466">FreeAnchor</a>.</p></li>
<li><p>Add a Dockerfile.</p></li>
<li><p>Add a jupyter notebook demo and a webcam demo.</p></li>
<li><p>Setup the code style and CI.</p></li>
<li><p>Add lots of docstrings and unit tests.</p></li>
<li><p>Fix lots of bugs.</p></li>
</ul>
<p><strong>Breaking Changes</strong></p>
<ul class="simple">
<li><p>There was a bug for computing COCO-style mAP w.r.t different scales (AP_s, AP_m, AP_l), introduced by #621. (#1679)</p></li>
</ul>
<p><strong>Bug Fixes</strong></p>
<ul class="simple">
<li><p>Fix a sampling interval bug in Libra R-CNN. (#1800)</p></li>
<li><p>Fix the learning rate in SSD300 WIDER FACE. (#1781)</p></li>
<li><p>Fix the scaling issue when <code class="docutils literal notranslate"><span class="pre">keep_ratio=False</span></code>. (#1730)</p></li>
<li><p>Fix typos. (#1721, #1492, #1242, #1108, #1107)</p></li>
<li><p>Fix the shuffle argument in <code class="docutils literal notranslate"><span class="pre">build_dataloader</span></code>. (#1693)</p></li>
<li><p>Clip the proposal when computing mask targets. (#1688)</p></li>
<li><p>Fix the “index out of range” bug for samplers in some corner cases. (#1610, #1404)</p></li>
<li><p>Fix the NMS issue on devices other than GPU:0. (#1603)</p></li>
<li><p>Fix SSD Head and GHM Loss on CPU. (#1578)</p></li>
<li><p>Fix the OOM error when there are too many gt bboxes. (#1575)</p></li>
<li><p>Fix the wrong keyword argument <code class="docutils literal notranslate"><span class="pre">nms_cfg</span></code> in HTC. (#1573)</p></li>
<li><p>Process masks and semantic segmentation in Expand and MinIoUCrop transforms. (#1550, #1361)</p></li>
<li><p>Fix a scale bug in the Non Local op. (#1528)</p></li>
<li><p>Fix a bug in transforms when <code class="docutils literal notranslate"><span class="pre">gt_bboxes_ignore</span></code> is None. (#1498)</p></li>
<li><p>Fix a bug when <code class="docutils literal notranslate"><span class="pre">img_prefix</span></code> is None. (#1497)</p></li>
<li><p>Pass the device argument to <code class="docutils literal notranslate"><span class="pre">grid_anchors</span></code> and <code class="docutils literal notranslate"><span class="pre">valid_flags</span></code>. (#1478)</p></li>
<li><p>Fix the data pipeline for test_robustness. (#1476)</p></li>
<li><p>Fix the argument type of deformable pooling. (#1390)</p></li>
<li><p>Fix the coco_eval when there are only two classes. (#1376)</p></li>
<li><p>Fix a bug in Modulated DeformableConv when deformable_group&gt;1. (#1359)</p></li>
<li><p>Fix the mask cropping in RandomCrop. (#1333)</p></li>
<li><p>Fix zero outputs in DeformConv when not running on cuda:0. (#1326)</p></li>
<li><p>Fix the type issue in Expand. (#1288)</p></li>
<li><p>Fix the inference API. (#1255)</p></li>
<li><p>Fix the inplace operation in Expand. (#1249)</p></li>
<li><p>Fix the from-scratch training config. (#1196)</p></li>
<li><p>Fix inplace add in RoIExtractor which cause an error in PyTorch 1.2. (#1160)</p></li>
<li><p>Fix FCOS when input images has no positive sample. (#1136)</p></li>
<li><p>Fix recursive imports. (#1099)</p></li>
</ul>
<p><strong>Improvements</strong></p>
<ul class="simple">
<li><p>Print the config file and rsidet version in the log. (#1721)</p></li>
<li><p>Lint the code before compiling in travis CI. (#1715)</p></li>
<li><p>Add a probability argument for the <code class="docutils literal notranslate"><span class="pre">Expand</span></code> transform. (#1651)</p></li>
<li><p>Update the PyTorch and CUDA version in the docker file. (#1615)</p></li>
<li><p>Raise a warning when specifying <code class="docutils literal notranslate"><span class="pre">--validate</span></code> in non-distributed training. (#1624, #1651)</p></li>
<li><p>Beautify the mAP printing. (#1614)</p></li>
<li><p>Add pre-commit hook. (#1536)</p></li>
<li><p>Add the argument <code class="docutils literal notranslate"><span class="pre">in_channels</span></code> to backbones. (#1475)</p></li>
<li><p>Add lots of docstrings and unit tests, thanks to <a class="reference external" href="https://github.com/Erotemic">&#64;Erotemic</a>. (#1603, #1517, #1506, #1505, #1491, #1479, #1477, #1475, #1474)</p></li>
<li><p>Add support for multi-node distributed test when there is no shared storage. (#1399)</p></li>
<li><p>Optimize Dockerfile to reduce the image size. (#1306)</p></li>
<li><p>Update new results of HRNet. (#1284, #1182)</p></li>
<li><p>Add an argument <code class="docutils literal notranslate"><span class="pre">no_norm_on_lateral</span></code> in FPN. (#1240)</p></li>
<li><p>Test the compiling in CI. (#1235)</p></li>
<li><p>Move docs to a separate folder. (#1233)</p></li>
<li><p>Add a jupyter notebook demo. (#1158)</p></li>
<li><p>Support different type of dataset for training. (#1133)</p></li>
<li><p>Use int64_t instead of long in cuda kernels. (#1131)</p></li>
<li><p>Support unsquare RoIs for bbox and mask heads. (#1128)</p></li>
<li><p>Manually add type promotion to make compatible to PyTorch 1.2. (#1114)</p></li>
<li><p>Allowing validation dataset for computing validation loss. (#1093)</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">.scalar_type()</span></code> instead of <code class="docutils literal notranslate"><span class="pre">.type()</span></code> to suppress some warnings. (#1070)</p></li>
</ul>
<p><strong>New Features</strong></p>
<ul class="simple">
<li><p>Add an option <code class="docutils literal notranslate"><span class="pre">--with_ap</span></code> to compute the AP for each class. (#1549)</p></li>
<li><p>Implement “FreeAnchor: Learning to Match Anchors for Visual Object Detection”. (#1391)</p></li>
<li><p>Support <a class="reference external" href="https://github.com/albumentations-team/albumentations">Albumentations</a> for augmentations in the data pipeline. (#1354)</p></li>
<li><p>Implement “FoveaBox: Beyond Anchor-based Object Detector”. (#1339)</p></li>
<li><p>Support horizontal and vertical flipping. (#1273, #1115)</p></li>
<li><p>Implement “RepPoints: Point Set Representation for Object Detection”. (#1265)</p></li>
<li><p>Add test-time augmentation to HTC and Cascade R-CNN. (#1251)</p></li>
<li><p>Add a COCO result analysis tool. (#1228)</p></li>
<li><p>Add Dockerfile. (#1168)</p></li>
<li><p>Add a webcam demo. (#1155, #1150)</p></li>
<li><p>Add FLOPs counter. (#1127)</p></li>
<li><p>Allow arbitrary layer order for ConvModule. (#1078)</p></li>
</ul>
</section>
<section id="v1-0rc0-27-07-2019">
<h2>v1.0rc0 (27/07/2019)<a class="headerlink" href="#v1-0rc0-27-07-2019" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Implement lots of new methods and components (Mixed Precision Training, HTC, Libra R-CNN, Guided Anchoring, Empirical Attention, Mask Scoring R-CNN, Grid R-CNN (Plus), GHM, GCNet, FCOS, HRNet, Weight Standardization, etc.). Thank all collaborators!</p></li>
<li><p>Support two additional datasets: WIDER FACE and Cityscapes.</p></li>
<li><p>Refactoring for loss APIs and make it more flexible to adopt different losses and related hyper-parameters.</p></li>
<li><p>Speed up multi-gpu testing.</p></li>
<li><p>Integrate all compiling and installing in a single script.</p></li>
</ul>
</section>
<section id="v0-6-0-14-04-2019">
<h2>v0.6.0 (14/04/2019)<a class="headerlink" href="#v0-6-0-14-04-2019" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Up to 30% speedup compared to the model zoo.</p></li>
<li><p>Support both PyTorch stable and nightly version.</p></li>
<li><p>Replace NMS and SigmoidFocalLoss with Pytorch CUDA extensions.</p></li>
</ul>
</section>
<section id="v0-6rc0-06-02-2019">
<h2>v0.6rc0(06/02/2019)<a class="headerlink" href="#v0-6rc0-06-02-2019" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Migrate to PyTorch 1.0.</p></li>
</ul>
</section>
<section id="v0-5-7-06-02-2019">
<h2>v0.5.7 (06/02/2019)<a class="headerlink" href="#v0-5-7-06-02-2019" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add support for Deformable ConvNet v2. (Many thanks to the authors and <a class="reference external" href="https://github.com/chengdazhi">&#64;chengdazhi</a>)</p></li>
<li><p>This is the last release based on PyTorch 0.4.1.</p></li>
</ul>
</section>
<section id="v0-5-6-17-01-2019">
<h2>v0.5.6 (17/01/2019)<a class="headerlink" href="#v0-5-6-17-01-2019" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add support for Group Normalization.</p></li>
<li><p>Unify RPNHead and single stage heads (RetinaHead, SSDHead) with AnchorHead.</p></li>
</ul>
</section>
<section id="v0-5-5-22-12-2018">
<h2>v0.5.5 (22/12/2018)<a class="headerlink" href="#v0-5-5-22-12-2018" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add SSD for COCO and PASCAL VOC.</p></li>
<li><p>Add ResNeXt backbones and detection models.</p></li>
<li><p>Refactoring for Samplers/Assigners and add OHEM.</p></li>
<li><p>Add VOC dataset and evaluation scripts.</p></li>
</ul>
</section>
<section id="v0-5-4-27-11-2018">
<h2>v0.5.4 (27/11/2018)<a class="headerlink" href="#v0-5-4-27-11-2018" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add SingleStageDetector and RetinaNet.</p></li>
</ul>
</section>
<section id="v0-5-3-26-11-2018">
<h2>v0.5.3 (26/11/2018)<a class="headerlink" href="#v0-5-3-26-11-2018" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add Cascade R-CNN and Cascade Mask R-CNN.</p></li>
<li><p>Add support for Soft-NMS in config files.</p></li>
</ul>
</section>
<section id="v0-5-2-21-10-2018">
<h2>v0.5.2 (21/10/2018)<a class="headerlink" href="#v0-5-2-21-10-2018" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add support for custom datasets.</p></li>
<li><p>Add a script to convert PASCAL VOC annotations to the expected format.</p></li>
</ul>
</section>
<section id="v0-5-1-20-10-2018">
<h2>v0.5.1 (20/10/2018)<a class="headerlink" href="#v0-5-1-20-10-2018" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add BBoxAssigner and BBoxSampler, the <code class="docutils literal notranslate"><span class="pre">train_cfg</span></code> field in config files are restructured.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ConvFCRoIHead</span></code> / <code class="docutils literal notranslate"><span class="pre">SharedFCRoIHead</span></code> are renamed to <code class="docutils literal notranslate"><span class="pre">ConvFCBBoxHead</span></code> / <code class="docutils literal notranslate"><span class="pre">SharedFCBBoxHead</span></code> for consistency.</p></li>
</ul>
</section>
</section>


              </article>
              
            </div>
            <footer>
  
  <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    
    <a href="faq.html" class="btn btn-neutral float-right" title="Frequently Asked Questions" accesskey="n"
      rel="next">Next <img src="_static/images/chevron-right-blue.svg"
        class="next-page"></a>
    
    
    <a href="projects.html" class="btn btn-neutral" title="Projects based on MMDetection" accesskey="p"
      rel="prev"><img src="_static/images/chevron-right-blue.svg" class="previous-page"> Previous</a>
    
  </div>
  

  <hr>

  <div role="contentinfo">
    <p>
      &copy; Copyright 2022, EarthNets.

    </p>
  </div>
  
  <div>
    Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a
      href="https://github.com/rtfd/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the
      Docs</a>.
  </div>
   

</footer>
          </div>
        </div>

        <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              <ul>
<li><a class="reference internal" href="#">Changelog</a><ul>
<li><a class="reference internal" href="#v2-25-1-29-7-2022">v2.25.1 (29/7/2022)</a><ul>
<li><a class="reference internal" href="#bug-fixes">Bug Fixes</a></li>
<li><a class="reference internal" href="#improvements">Improvements</a></li>
<li><a class="reference internal" href="#documents">Documents</a></li>
<li><a class="reference internal" href="#contributors">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-25-0-31-5-2022">v2.25.0 (31/5/2022)</a><ul>
<li><a class="reference internal" href="#highlights">Highlights</a></li>
<li><a class="reference internal" href="#backwards-incompatible-changes">Backwards incompatible changes</a></li>
<li><a class="reference internal" href="#new-features">New Features</a></li>
<li><a class="reference internal" href="#id1">Bug Fixes</a></li>
<li><a class="reference internal" href="#id2">Improvements</a></li>
<li><a class="reference internal" href="#id3">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-24-0-26-4-2022">v2.24.0 (26/4/2022)</a><ul>
<li><a class="reference internal" href="#id4">Highlights</a></li>
<li><a class="reference internal" href="#id5">New Features</a></li>
<li><a class="reference internal" href="#id6">Bug Fixes</a></li>
<li><a class="reference internal" href="#id7">Improvements</a></li>
<li><a class="reference internal" href="#id8">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-23-0-28-3-2022">v2.23.0 (28/3/2022)</a><ul>
<li><a class="reference internal" href="#id9">Highlights</a></li>
<li><a class="reference internal" href="#id10">New Features</a></li>
<li><a class="reference internal" href="#id11">Bug Fixes</a></li>
<li><a class="reference internal" href="#id12">Improvements</a></li>
<li><a class="reference internal" href="#id13">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-22-0-24-2-2022">v2.22.0 (24/2/2022)</a><ul>
<li><a class="reference internal" href="#id14">Highlights</a></li>
<li><a class="reference internal" href="#id15">New Features</a></li>
<li><a class="reference internal" href="#breaking-changes">Breaking Changes</a></li>
<li><a class="reference internal" href="#id16">Bug Fixes</a></li>
<li><a class="reference internal" href="#id17">Improvements</a></li>
<li><a class="reference internal" href="#id18">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-21-0-8-2-2022">v2.21.0 (8/2/2022)</a></li>
<li><a class="reference internal" href="#id19">Breaking Changes</a><ul>
<li><a class="reference internal" href="#id20">New Features</a></li>
<li><a class="reference internal" href="#id21">Bug Fixes</a></li>
<li><a class="reference internal" href="#id22">Improvements</a></li>
<li><a class="reference internal" href="#id23">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-20-0-27-12-2021">v2.20.0 (27/12/2021)</a><ul>
<li><a class="reference internal" href="#id24">New Features</a></li>
<li><a class="reference internal" href="#id25">Bug Fixes</a></li>
<li><a class="reference internal" href="#id26">Improvements</a></li>
<li><a class="reference internal" href="#id27">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-19-1-14-12-2021">v2.19.1 (14/12/2021)</a><ul>
<li><a class="reference internal" href="#id28">New Features</a></li>
<li><a class="reference internal" href="#id29">Bug Fixes</a></li>
<li><a class="reference internal" href="#id30">Improvements</a></li>
<li><a class="reference internal" href="#id31">Documents</a></li>
<li><a class="reference internal" href="#id32">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-19-0-29-11-2021">v2.19.0 (29/11/2021)</a><ul>
<li><a class="reference internal" href="#id33">Highlights</a></li>
<li><a class="reference internal" href="#id34">New Features</a></li>
<li><a class="reference internal" href="#id35">Bug Fixes</a></li>
<li><a class="reference internal" href="#id36">Improvements</a></li>
<li><a class="reference internal" href="#id37">Documents</a></li>
<li><a class="reference internal" href="#id38">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-18-1-15-11-2021">v2.18.1 (15/11/2021)</a><ul>
<li><a class="reference internal" href="#id39">Highlights</a></li>
<li><a class="reference internal" href="#id40">New Features</a></li>
<li><a class="reference internal" href="#id41">Bug Fixes</a></li>
<li><a class="reference internal" href="#id42">Improvements</a></li>
<li><a class="reference internal" href="#id43">Documents</a></li>
<li><a class="reference internal" href="#id44">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-18-0-27-10-2021">v2.18.0 (27/10/2021)</a><ul>
<li><a class="reference internal" href="#id45">Highlights</a></li>
<li><a class="reference internal" href="#id46">New Features</a></li>
<li><a class="reference internal" href="#id47">Bug Fixes</a></li>
<li><a class="reference internal" href="#id48">Improvements</a></li>
<li><a class="reference internal" href="#refactors">Refactors</a></li>
<li><a class="reference internal" href="#id49">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-17-0-28-9-2021">v2.17.0 (28/9/2021)</a><ul>
<li><a class="reference internal" href="#id50">Highlights</a></li>
<li><a class="reference internal" href="#id51">New Features</a></li>
<li><a class="reference internal" href="#id52">Bug Fixes</a></li>
<li><a class="reference internal" href="#id53">Improvements</a></li>
<li><a class="reference internal" href="#id54">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-16-0-30-8-2021">v2.16.0 (30/8/2021)</a><ul>
<li><a class="reference internal" href="#id55">Highlights</a></li>
<li><a class="reference internal" href="#id56">New Features</a></li>
<li><a class="reference internal" href="#id57">Bug Fixes</a></li>
<li><a class="reference internal" href="#id58">Improvements</a></li>
<li><a class="reference internal" href="#id59">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-15-1-11-8-2021">v2.15.1 (11/8/2021)</a><ul>
<li><a class="reference internal" href="#id60">Highlights</a></li>
<li><a class="reference internal" href="#id61">New Features</a></li>
<li><a class="reference internal" href="#id62">Bug Fixes</a></li>
<li><a class="reference internal" href="#id63">Improvements</a></li>
<li><a class="reference internal" href="#id64">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-15-0-02-8-2021">v2.15.0 (02/8/2021)</a><ul>
<li><a class="reference internal" href="#id65">Highlights</a></li>
<li><a class="reference internal" href="#id66">New Features</a></li>
<li><a class="reference internal" href="#id67">Bug Fixes</a></li>
<li><a class="reference internal" href="#id68">Improvements</a></li>
<li><a class="reference internal" href="#id69">Contributors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-14-0-29-6-2021">v2.14.0 (29/6/2021)</a><ul>
<li><a class="reference internal" href="#id70">Highlights</a></li>
<li><a class="reference internal" href="#id71">New Features</a></li>
<li><a class="reference internal" href="#id72">Bug Fixes</a></li>
<li><a class="reference internal" href="#id73">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-13-0-01-6-2021">v2.13.0 (01/6/2021)</a><ul>
<li><a class="reference internal" href="#id74">Highlights</a></li>
<li><a class="reference internal" href="#id75">New Features</a></li>
<li><a class="reference internal" href="#id76">Bug Fixes</a></li>
<li><a class="reference internal" href="#id77">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-12-0-01-5-2021">v2.12.0 (01/5/2021)</a><ul>
<li><a class="reference internal" href="#id78">Highlights</a></li>
<li><a class="reference internal" href="#id79">Backwards Incompatible Changes</a></li>
<li><a class="reference internal" href="#id80">New Features</a></li>
<li><a class="reference internal" href="#id81">Improvements</a></li>
<li><a class="reference internal" href="#id82">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-11-0-01-4-2021">v2.11.0 (01/4/2021)</a></li>
<li><a class="reference internal" href="#v2-10-0-01-03-2021">v2.10.0 (01/03/2021)</a><ul>
<li><a class="reference internal" href="#id83">Highlights</a></li>
<li><a class="reference internal" href="#id84">New Features</a></li>
<li><a class="reference internal" href="#id85">Bug Fixes</a></li>
<li><a class="reference internal" href="#id86">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-9-0-01-02-2021">v2.9.0 (01/02/2021)</a><ul>
<li><a class="reference internal" href="#id87">Highlights</a></li>
<li><a class="reference internal" href="#id88">New Features</a></li>
<li><a class="reference internal" href="#id89">Bug Fixes</a></li>
<li><a class="reference internal" href="#id90">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-8-0-04-01-2021">v2.8.0 (04/01/2021)</a><ul>
<li><a class="reference internal" href="#id91">Highlights</a></li>
<li><a class="reference internal" href="#id92">New Features</a></li>
<li><a class="reference internal" href="#id93">Bug Fixes</a></li>
<li><a class="reference internal" href="#id94">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-7-0-30-11-2020">v2.7.0 (30/11/2020)</a><ul>
<li><a class="reference internal" href="#id95">New Features</a></li>
<li><a class="reference internal" href="#id96">Bug Fixes</a></li>
<li><a class="reference internal" href="#id97">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-6-0-1-11-2020">v2.6.0 (1/11/2020)</a><ul>
<li><a class="reference internal" href="#id98">New Features</a></li>
<li><a class="reference internal" href="#id99">Bug Fixes</a></li>
<li><a class="reference internal" href="#id100">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-5-0-5-10-2020">v2.5.0 (5/10/2020)</a><ul>
<li><a class="reference internal" href="#id101">Highlights</a></li>
<li><a class="reference internal" href="#id102">Backwards Incompatible Changes</a></li>
<li><a class="reference internal" href="#id103">New Features</a></li>
<li><a class="reference internal" href="#id104">Bug Fixes</a></li>
<li><a class="reference internal" href="#id105">Improvements</a></li>
</ul>
</li>
<li><a class="reference internal" href="#v2-4-0-5-9-2020">v2.4.0 (5/9/2020)</a></li>
<li><a class="reference internal" href="#v2-3-0-5-8-2020">v2.3.0 (5/8/2020)</a></li>
<li><a class="reference internal" href="#v2-2-0-1-7-2020">v2.2.0 (1/7/2020)</a></li>
<li><a class="reference internal" href="#v2-1-0-8-6-2020">v2.1.0 (8/6/2020)</a></li>
<li><a class="reference internal" href="#v2-0-0-6-5-2020">v2.0.0 (6/5/2020)</a></li>
<li><a class="reference internal" href="#v1-1-0-24-2-2020">v1.1.0 (24/2/2020)</a></li>
<li><a class="reference internal" href="#v1-0-0-30-1-2020">v1.0.0 (30/1/2020)</a></li>
<li><a class="reference internal" href="#v1-0rc1-13-12-2019">v1.0rc1 (13/12/2019)</a></li>
<li><a class="reference internal" href="#v1-0rc0-27-07-2019">v1.0rc0 (27/07/2019)</a></li>
<li><a class="reference internal" href="#v0-6-0-14-04-2019">v0.6.0 (14/04/2019)</a></li>
<li><a class="reference internal" href="#v0-6rc0-06-02-2019">v0.6rc0(06/02/2019)</a></li>
<li><a class="reference internal" href="#v0-5-7-06-02-2019">v0.5.7 (06/02/2019)</a></li>
<li><a class="reference internal" href="#v0-5-6-17-01-2019">v0.5.6 (17/01/2019)</a></li>
<li><a class="reference internal" href="#v0-5-5-22-12-2018">v0.5.5 (22/12/2018)</a></li>
<li><a class="reference internal" href="#v0-5-4-27-11-2018">v0.5.4 (27/11/2018)</a></li>
<li><a class="reference internal" href="#v0-5-3-26-11-2018">v0.5.3 (26/11/2018)</a></li>
<li><a class="reference internal" href="#v0-5-2-21-10-2018">v0.5.2 (21/10/2018)</a></li>
<li><a class="reference internal" href="#v0-5-1-20-10-2018">v0.5.1 (20/10/2018)</a></li>
</ul>
</li>
</ul>

            </div>
          </div>
        </div>
    </section>
  </div>

  


  

  
  <script type="text/javascript" id="documentation_options" data-url_root="./"
    src="_static/documentation_options.js"></script>
  <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
  <script src="_static/jquery.js"></script>
  <script src="_static/underscore.js"></script>
  <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
  <script src="_static/doctools.js"></script>
  <script src="_static/clipboard.min.js"></script>
  <script src="_static/copybutton.js"></script>
  

  

  <script type="text/javascript" src="_static/js/vendor/popper.min.js"></script>
  <script type="text/javascript" src="_static/js/vendor/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

  <!-- Begin Footer -->

  <div class="container-fluid docs-tutorials-resources" id="docs-tutorials-resources">
  </div>

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="https://rsi-detection.readthedocs.io/en/latest/" aria-label="OpenMMLab"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection/blob/main/demo/rsi_detection_tutorial.ipynb" target="_blank">Tutorial</a>
          </li>
          <li>
            <a href="https://github.com/EarthNets/RSI-Detection" target="_blank">GitHub</a>
          </li>
          <li class="resources-mobile-menu-title" >
            About
          </li>
          <ul class="resources-mobile-menu-items">
            <li>
              <a href="https://github.com/open-mmlab/mmcv" target="_blank">MMCV</a>
            </li>
          </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="_static/js/vendor/anchor.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>