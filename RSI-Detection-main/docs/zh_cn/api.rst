rsidet.apis
--------------
.. automodule:: rsidet.apis
    :members:

rsidet.core
--------------

anchor
^^^^^^^^^^
.. automodule:: rsidet.core.anchor
    :members:

bbox
^^^^^^^^^^
.. automodule:: rsidet.core.bbox
    :members:

export
^^^^^^^^^^
.. automodule:: rsidet.core.export
    :members:

mask
^^^^^^^^^^
.. automodule:: rsidet.core.mask
    :members:

evaluation
^^^^^^^^^^
.. automodule:: rsidet.core.evaluation
    :members:

post_processing
^^^^^^^^^^^^^^^
.. automodule:: rsidet.core.post_processing
    :members:

utils
^^^^^^^^^^
.. automodule:: rsidet.core.utils
    :members:

rsidet.datasets
--------------

datasets
^^^^^^^^^^
.. automodule:: rsidet.datasets
    :members:

pipelines
^^^^^^^^^^
.. automodule:: rsidet.datasets.pipelines
    :members:

samplers
^^^^^^^^^^
.. automodule:: rsidet.datasets.samplers
    :members:

api_wrappers
^^^^^^^^^^
.. automodule:: rsidet.datasets.api_wrappers
    :members:

rsidet.models
--------------

detectors
^^^^^^^^^^
.. automodule:: rsidet.models.detectors
    :members:

backbones
^^^^^^^^^^
.. automodule:: rsidet.models.backbones
    :members:

necks
^^^^^^^^^^^^
.. automodule:: rsidet.models.necks
    :members:

dense_heads
^^^^^^^^^^^^
.. automodule:: rsidet.models.dense_heads
    :members:

roi_heads
^^^^^^^^^^
.. automodule:: rsidet.models.roi_heads
    :members:

losses
^^^^^^^^^^
.. automodule:: rsidet.models.losses
    :members:

utils
^^^^^^^^^^
.. automodule:: rsidet.models.utils
    :members:

rsidet.utils
--------------
.. automodule::rsidet.utils
    :members:
