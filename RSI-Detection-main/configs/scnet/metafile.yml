Collections:
  - Name: SCNet
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - ResNet
        - SCNet
    Paper:
      URL: https://arxiv.org/abs/2012.10150
      Title: 'SCNet: Training Inference Sample Consistency for Instance Segmentation'
    README: configs/scnet/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.9.0/rsidet/models/detectors/scnet.py#L6
      Version: v2.9.0

Models:
  - Name: scnet_r50_fpn_1x_coco
    In Collection: SCNet
    Config: configs/scnet/scnet_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 161.29
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.2
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scnet/scnet_r50_fpn_1x_coco/scnet_r50_fpn_1x_coco-c3f09857.pth

  - Name: scnet_r50_fpn_20e_coco
    In Collection: SCNet
    Config: configs/scnet/scnet_r50_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 161.29
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scnet/scnet_r50_fpn_20e_coco/scnet_r50_fpn_20e_coco-a569f645.pth

  - Name: scnet_r101_fpn_20e_coco
    In Collection: SCNet
    Config: configs/scnet/scnet_r101_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 8.9
      inference time (ms/im):
        - value: 172.41
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scnet/scnet_r101_fpn_20e_coco/scnet_r101_fpn_20e_coco-294e312c.pth

  - Name: scnet_x101_64x4d_fpn_20e_coco
    In Collection: SCNet
    Config: configs/scnet/scnet_x101_64x4d_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 13.2
      inference time (ms/im):
        - value: 204.08
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 47.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 42.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/scnet/scnet_x101_64x4d_fpn_20e_coco/scnet_x101_64x4d_fpn_20e_coco-fb09dec9.pth
