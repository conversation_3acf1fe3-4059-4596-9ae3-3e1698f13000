Collections:
  - Name: <PERSON><PERSON><PERSON>
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - Convolution
        - ResNet
    Paper: https://arxiv.org/abs/1912.04488
    README: configs/solo/README.md

Models:
  - Name: decoupled_solo_r50_fpn_1x_coco
    In Collection: SOLO
    Config: configs/solo/decoupled_solo_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.8
      Epochs: 12
    inference time (ms/im):
      - value: 116.4
        hardware: V100
        backend: PyTorch
        batch size: 1
        mode: FP32
        resolution: (1333, 800)
    Results:
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 33.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/solo/decoupled_solo_r50_fpn_1x_coco/decoupled_solo_r50_fpn_1x_coco_20210820_233348-6337c589.pth

  - Name: decoupled_solo_r50_fpn_3x_coco
    In Collection: SOLO
    Config: configs/solo/decoupled_solo_r50_fpn_3x_coco.py
    Metadata:
      Training Memory (GB): 7.9
      Epochs: 36
    inference time (ms/im):
      - value: 117.2
        hardware: V100
        backend: PyTorch
        batch size: 1
        mode: FP32
        resolution: (1333, 800)
    Results:
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/solo/decoupled_solo_r50_fpn_3x_coco/decoupled_solo_r50_fpn_3x_coco_20210821_042504-7b3301ec.pth

  - Name: decoupled_solo_light_r50_fpn_3x_coco
    In Collection: SOLO
    Config: configs/solo/decoupled_solo_light_r50_fpn_3x_coco.py
    Metadata:
      Training Memory (GB): 2.2
      Epochs: 36
    inference time (ms/im):
      - value: 35.0
        hardware: V100
        backend: PyTorch
        batch size: 1
        mode: FP32
        resolution: (852, 512)
    Results:
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 32.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/solo/decoupled_solo_light_r50_fpn_3x_coco/decoupled_solo_light_r50_fpn_3x_coco_20210906_142703-e70e226f.pth

  - Name: solo_r50_fpn_3x_coco
    In Collection: SOLO
    Config: configs/solo/solo_r50_fpn_3x_coco.py
    Metadata:
      Training Memory (GB): 7.4
      Epochs: 36
    inference time (ms/im):
      - value: 94.2
        hardware: V100
        backend: PyTorch
        batch size: 1
        mode: FP32
        resolution: (1333, 800)
    Results:
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 35.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/solo/solo_r50_fpn_3x_coco/solo_r50_fpn_3x_coco_20210901_012353-11d224d7.pth

  - Name: solo_r50_fpn_1x_coco
    In Collection: SOLO
    Config: configs/solo/solo_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.0
      Epochs: 12
    inference time (ms/im):
      - value: 95.1
        hardware: V100
        backend: PyTorch
        batch size: 1
        mode: FP32
        resolution: (1333, 800)
    Results:
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 33.1
    Weights: https://download.openmmlab.com/rsidetection/v2.0/solo/solo_r50_fpn_1x_coco/solo_r50_fpn_1x_coco_20210821_035055-2290a6b8.pth
