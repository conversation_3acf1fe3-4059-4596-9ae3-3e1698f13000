Collections:
  - Name: FreeAnchor
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FreeAnchor
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/1909.02466
      Title: 'FreeAnchor: Learning to Match Anchors for Visual Object Detection'
    README: configs/free_anchor/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.0.0/rsidet/models/dense_heads/free_anchor_retina_head.py#L10
      Version: v2.0.0

Models:
  - Name: retinanet_free_anchor_r50_fpn_1x_coco
    In Collection: FreeAnchor
    Config: configs/free_anchor/retinanet_free_anchor_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.9
      inference time (ms/im):
        - value: 54.35
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/free_anchor/retinanet_free_anchor_r50_fpn_1x_coco/retinanet_free_anchor_r50_fpn_1x_coco_20200130-0f67375f.pth

  - Name: retinanet_free_anchor_r101_fpn_1x_coco
    In Collection: FreeAnchor
    Config: configs/free_anchor/retinanet_free_anchor_r101_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.8
      inference time (ms/im):
        - value: 67.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/free_anchor/retinanet_free_anchor_r101_fpn_1x_coco/retinanet_free_anchor_r101_fpn_1x_coco_20200130-358324e6.pth

  - Name: retinanet_free_anchor_x101_32x4d_fpn_1x_coco
    In Collection: FreeAnchor
    Config: configs/free_anchor/retinanet_free_anchor_x101_32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.1
      inference time (ms/im):
        - value: 90.09
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/free_anchor/retinanet_free_anchor_x101_32x4d_fpn_1x_coco/retinanet_free_anchor_x101_32x4d_fpn_1x_coco_20200130-d4846968.pth
