Collections:
  - Name: YOLO<PERSON>
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Dilated Encoder
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/2103.09460
      Title: 'You Only Look One-level Feature'
    README: configs/yolof/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.12.0/rsidet/models/detectors/yolof.py#L6
      Version: v2.12.0

Models:
  - Name: yolof_r50_c5_8x8_1x_coco
    In Collection: YOLOF
    Config: configs/yolof/yolof_r50_c5_8x8_1x_coco.py
    Metadata:
      Training Memory (GB): 8.3
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 37.5
    Weights: https://download.openmmlab.com/rsidetection/v2.0/yolof/yolof_r50_c5_8x8_1x_coco/yolof_r50_c5_8x8_1x_coco_20210425_024427-8e864411.pth
