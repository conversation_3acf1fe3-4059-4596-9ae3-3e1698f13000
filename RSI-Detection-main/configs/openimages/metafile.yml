Models:
  - Name: faster_rcnn_r50_fpn_32x2_1x_openimages
    In Collection: Faster R-CNN
    Config: configs/openimages/faster_rcnn_r50_fpn_32x2_1x_openimages.py
    Metadata:
      Training Memory (GB): 7.7
      Epochs: 12
      Training Data: Open Images v6
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images v6
        Metrics:
          box AP: 51.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/faster_rcnn_r50_fpn_32x2_1x_openimages/faster_rcnn_r50_fpn_32x2_1x_openimages_20211130_231159-e87ab7ce.pth

  - Name: retinanet_r50_fpn_32x2_1x_openimages
    In Collection: RetinaNet
    Config: configs/openimages/retinanet_r50_fpn_32x2_1x_openimages.py
    Metadata:
      Training Memory (GB): 6.6
      Epochs: 12
      Training Data: Open Images v6
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images v6
        Metrics:
          box AP: 61.5
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/retinanet_r50_fpn_32x2_1x_openimages/retinanet_r50_fpn_32x2_1x_openimages_20211223_071954-d2ae5462.pth

  - Name: ssd300_32x8_36e_openimages
    In Collection: SSD
    Config: configs/openimages/ssd300_32x8_36e_openimages.py
    Metadata:
      Training Memory (GB): 10.8
      Epochs: 36
      Training Data: Open Images v6
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images v6
        Metrics:
          box AP: 35.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/ssd300_32x8_36e_openimages/ssd300_32x8_36e_openimages_20211224_000232-dce93846.pth

  - Name: faster_rcnn_r50_fpn_32x2_1x_openimages_challenge
    In Collection: Faster R-CNN
    Config: configs/openimages/faster_rcnn_r50_fpn_32x2_1x_openimages_challenge.py
    Metadata:
      Training Memory (GB): 7.7
      Epochs: 12
      Training Data: Open Images Challenge 2019
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images Challenge 2019
        Metrics:
          box AP: 54.9
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/faster_rcnn_r50_fpn_32x2_1x_openimages_challenge/faster_rcnn_r50_fpn_32x2_1x_openimages_challenge_20220114_045100-0e79e5df.pth

  - Name: faster_rcnn_r50_fpn_32x2_cas_1x_openimages
    In Collection: Faster R-CNN
    Config: configs/openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages.py
    Metadata:
      Training Memory (GB): 7.7
      Epochs: 12
      Training Data: Open Images Challenge 2019
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images Challenge 2019
        Metrics:
          box AP: 60.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages_20220306_202424-98c630e5.pth

  - Name: faster_rcnn_r50_fpn_32x2_cas_1x_openimages_challenge
    In Collection: Faster R-CNN
    Config: configs/openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages_challenge.py
    Metadata:
      Training Memory (GB): 7.1
      Epochs: 12
      Training Data: Open Images Challenge 2019
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
    Results:
      - Task: Object Detection
        Dataset: Open Images Challenge 2019
        Metrics:
          box AP: 65.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/openimages/faster_rcnn_r50_fpn_32x2_cas_1x_openimages_challenge/faster_rcnn_r50_fpn_32x2_cas_1x_openimages_challenge_20220221_192021-34c402d9.pth
