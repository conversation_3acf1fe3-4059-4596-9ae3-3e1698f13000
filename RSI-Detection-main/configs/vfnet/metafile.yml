Collections:
  - Name: VFNet
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - ResNet
        - Varifocal Loss
    Paper:
      URL: https://arxiv.org/abs/2008.13367
      Title: 'VarifocalNet: An IoU-aware Dense Object Detector'
    README: configs/vfnet/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.6.0/rsidet/models/detectors/vfnet.py#L6
      Version: v2.6.0

Models:
  - Name: vfnet_r50_fpn_1x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r50_fpn_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r50_fpn_1x_coco/vfnet_r50_fpn_1x_coco_20201027-38db6f58.pth

  - Name: vfnet_r50_fpn_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r50_fpn_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.8
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r50_fpn_mstrain_2x_coco/vfnet_r50_fpn_mstrain_2x_coco_20201027-7cc75bd2.pth

  - Name: vfnet_r50_fpn_mdconv_c3-c5_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r50_fpn_mdconv_c3-c5_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 48.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r50_fpn_mdconv_c3-c5_mstrain_2x_coco/vfnet_r50_fpn_mdconv_c3-c5_mstrain_2x_coco_20201027pth-6879c318.pth

  - Name: vfnet_r101_fpn_1x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r101_fpn_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r101_fpn_1x_coco/vfnet_r101_fpn_1x_coco_20201027pth-c831ece7.pth

  - Name: vfnet_r101_fpn_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r101_fpn_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r101_fpn_mstrain_2x_coco/vfnet_r101_fpn_mstrain_2x_coco_20201027pth-4a5d53f1.pth

  - Name: vfnet_r101_fpn_mdconv_c3-c5_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_r101_fpn_mdconv_c3-c5_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 49.2
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_r101_fpn_mdconv_c3-c5_mstrain_2x_coco/vfnet_r101_fpn_mdconv_c3-c5_mstrain_2x_coco_20201027pth-7729adb5.pth

  - Name: vfnet_x101_32x4d_fpn_mdconv_c3-c5_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_x101_32x4d_fpn_mdconv_c3-c5_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 50.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_x101_32x4d_fpn_mdconv_c3-c5_mstrain_2x_coco/vfnet_x101_32x4d_fpn_mdconv_c3-c5_mstrain_2x_coco_20201027pth-d300a6fc.pth

  - Name: vfnet_x101_64x4d_fpn_mdconv_c3-c5_mstrain_2x_coco
    In Collection: VFNet
    Config: configs/vfnet/vfnet_x101_64x4d_fpn_mdconv_c3-c5_mstrain_2x_coco.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 50.8
    Weights: https://download.openmmlab.com/rsidetection/v2.0/vfnet/vfnet_x101_64x4d_fpn_mdconv_c3-c5_mstrain_2x_coco/vfnet_x101_64x4d_fpn_mdconv_c3-c5_mstrain_2x_coco_20201027pth-b5f6da5e.pth
