Collections:
  - Name: Seesaw Loss
    Metadata:
      Training Data: LVIS
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Softmax
        - RPN
        - Convolution
        - Dense Connections
        - FPN
        - ResNet
        - RoIAlign
        - Seesaw Loss
    Paper:
      URL: https://arxiv.org/abs/2008.10032
      Title: 'Seesaw Loss for Long-Tailed Instance Segmentation'
    README: configs/seesaw_loss/README.md

Models:
  - Name: mask_rcnn_r50_fpn_random_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: seesaw_loss/mask_rcnn_r50_fpn_random_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 25.6
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 25.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r50_fpn_random_seesaw_loss_mstrain_2x_lvis_v1-a698dd3d.pth
  - Name: mask_rcnn_r50_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: seesaw_loss/mask_rcnn_r50_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 25.6
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 25.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r50_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-a1c11314.pth
  - Name: mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: seesaw_loss/mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 27.4
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 26.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1-8e6e6dd5.pth
  - Name: mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: seesaw_loss/mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 27.2
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 27.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-a0b59c42.pth
  - Name: mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 27.6
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 26.4
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1-392a804b.pth
  - Name: mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 27.6
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 26.8
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r50_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-cd0f6a12.pth
  - Name: mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 28.9
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 27.6
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1-e68eb464.pth
  - Name: mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 28.9
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 28.2
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-1d817139.pth
  - Name: cascade_mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/cascade_mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 33.1
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 29.2
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/cascade_mask_rcnn_r101_fpn_random_seesaw_loss_mstrain_2x_lvis_v1-71e2215e.pth
  - Name: cascade_mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/cascade_mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 33.0
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 30.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/cascade_mask_rcnn_r101_fpn_random_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-8b5a6745.pth
  - Name: cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 30.0
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 29.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_mstrain_2x_lvis_v1-5d8ca2a4.pth
  - Name: cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1
    In Collection: Seesaw Loss
    Config: configs/seesaw_loss/cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 32.8
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 30.1
    Weights: https://download.openmmlab.com/rsidetection/v2.0/seesaw_loss/cascade_mask_rcnn_r101_fpn_sample1e-3_seesaw_loss_normed_mask_mstrain_2x_lvis_v1-c8551505.pth
