Collections:
  - Name: PAFPN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - PAFPN
    Paper:
      URL: https://arxiv.org/abs/1803.01534
      Title: 'Path Aggregation Network for Instance Segmentation'
    README: configs/pafpn/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.0.0/rsidet/models/necks/pafpn.py#L11
      Version: v2.0.0

Models:
  - Name: faster_rcnn_r50_pafpn_1x_coco
    In Collection: PAFPN
    Config: configs/pafpn/faster_rcnn_r50_pafpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.0
      inference time (ms/im):
        - value: 58.14
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 37.5
    Weights: https://download.openmmlab.com/rsidetection/v2.0/pafpn/faster_rcnn_r50_pafpn_1x_coco/faster_rcnn_r50_pafpn_1x_coco_bbox_mAP-0.375_20200503_105836-b7b4b9bd.pth
