Collections:
  - Name: <PERSON><PERSON><PERSON><PERSON>
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Generic RoI Extractor
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: https://arxiv.org/abs/2004.13665
      Title: 'A novel Region of Interest Extraction Layer for Instance Segmentation'
    README: configs/groie/README.md
    Code:
      URL: https://github.com/open-mmlab/rsidetection/blob/v2.1.0/rsidet/models/roi_heads/roi_extractors/groie.py#L15
      Version: v2.1.0

Models:
  - Name: faster_rcnn_r50_fpn_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/faster_rcnn_r50_fpn_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.3
    Weights: https://download.openmmlab.com/rsidetection/v2.0/groie/faster_rcnn_r50_fpn_groie_1x_coco/faster_rcnn_r50_fpn_groie_1x_coco_20200604_211715-66ee9516.pth

  - Name: grid_rcnn_r50_fpn_gn-head_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/grid_rcnn_r50_fpn_gn-head_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.1
    Weights: https://download.openmmlab.com/rsidetection/v2.0/groie/grid_rcnn_r50_fpn_gn-head_groie_1x_coco/grid_rcnn_r50_fpn_gn-head_groie_1x_coco_20200605_202059-4b75d86f.pth

  - Name: mask_rcnn_r50_fpn_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask_rcnn_r50_fpn_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.0
    Weights: https://download.openmmlab.com/rsidetection/v2.0/groie/mask_rcnn_r50_fpn_groie_1x_coco/mask_rcnn_r50_fpn_groie_1x_coco_20200604_211715-50d90c74.pth

  - Name: mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:   37.8
    Weights: https://download.openmmlab.com/rsidetection/v2.0/groie/mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco/mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco_20200604_211715-42eb79e1.pth

  - Name: mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.7
    Weights: https://download.openmmlab.com/rsidetection/v2.0/groie/mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco/mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco_20200607_224507-8daae01c.pth
