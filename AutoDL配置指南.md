# ST-FSOD 在 AutoDL 平台配置指南

## 🚀 AutoDL 平台优势

- **GPU 资源**: RTX 3090/4090/A100 等高性能显卡
- **预装环境**: 常用深度学习框架预装
- **镜像分享**: 配置完成后可制作镜像分享
- **按需付费**: 灵活的计费模式

## 📋 第一步：创建实例

### 1.1 选择镜像
```
推荐镜像: PyTorch 2.0.0 + Python 3.9 + CUDA 11.8
或者: PyTorch 1.13.1 + Python 3.8 + CUDA 11.7
```

### 1.2 硬件配置
```
GPU: RTX 3090 (24GB) 或更高
CPU: 8核心以上
内存: 32GB 以上
硬盘: 50GB 以上
```

## 🔧 第二步：环境配置

### 2.1 连接到实例
```bash
# 通过 AutoDL 提供的 SSH 或 JupyterLab 连接
# 或使用网页终端
```

### 2.2 创建项目环境
```bash
# 创建新的 conda 环境
conda create -n st_fsod python=3.9 -y
conda activate st_fsod

# 设置清华镜像源（加速下载）
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
```

### 2.3 安装 PyTorch
```bash
# 根据 CUDA 版本选择对应的 PyTorch
# CUDA 11.8
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# 验证安装
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA:', torch.cuda.is_available())"
```

### 2.4 安装 mmcv-full
```bash
# 安装 openmim
pip install openmim

# 使用 mim 安装 mmcv-full（自动处理版本兼容）
mim install mmcv-full==1.7.1

# 验证安装
python -c "import mmcv; print('mmcv version:', mmcv.__version__)"
```

## 📦 第三步：安装项目依赖

### 3.1 克隆项目
```bash
# 在 /root 目录下克隆项目
cd /root
git clone https://github.com/zhu-xlab/ST-FSOD.git
cd ST-FSOD
```

### 3.2 安装 Dataset4EO
```bash
# 克隆 Dataset4EO
git clone https://github.com/EarthNets/Dataset4EO.git
cd Dataset4EO
pip install -e .
cd ..
```

### 3.3 安装 RSI-Detection
```bash
# 克隆 RSI-Detection
git clone https://github.com/EarthNets/RSI-Detection.git
cd RSI-Detection
pip install -e .
cd ..
```

### 3.4 安装项目本身
```bash
# 安装 ST-FSOD
pip install -e .

# 安装其他依赖
pip install -r requirements/runtime.txt
```

## 💾 第四步：数据集配置

### 4.1 创建数据集目录
```bash
# 在 AutoDL 数据盘创建数据集目录
mkdir -p /root/autodl-tmp/datasets
cd /root/autodl-tmp/datasets
```

### 4.2 下载数据集
```bash
# DIOR 数据集
wget https://gcheng-nwpu.github.io/DIOR/DIOR.zip
unzip DIOR.zip

# iSAID 数据集（需要注册下载）
# 按照官方说明下载并预处理
```

### 4.3 修改配置文件
```bash
# 编辑数据集路径配置
cd /root/ST-FSOD
nano dataset_config.py
```

```python
# 修改为 AutoDL 路径
DIOR_DATA_ROOT = "/root/autodl-tmp/datasets/DIOR"
ISAID_DATA_ROOT = "/root/autodl-tmp/datasets/iSAID_patches"
NWPU_DATA_ROOT = "/root/autodl-tmp/datasets/NWPU-VHR10"
```

## 🏃 第五步：运行项目

### 5.1 快速测试
```bash
# 激活环境
conda activate st_fsod
cd /root/ST-FSOD

# 运行配置验证
python validate_training_scripts.py

# 测试环境
python -c "
import torch
import mmcv
import rsifewshot
print('✅ 环境配置成功!')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
print(f'GPU: {torch.cuda.get_device_name(0)}')
"
```

### 5.2 开始训练
```bash
# 使用一键运行脚本
python run_st_fsod.py --dataset dior --split 1 --shots 3shot

# 或使用传统方式
python tools/detection/train.py configs/st_fsod/dior/split1/st-fsod_maskrcnn_r101_40k_dior-split1_base-training.py
```

## 🎯 第六步：制作镜像

### 6.1 清理环境
```bash
# 清理缓存
conda clean --all
pip cache purge

# 删除不必要的文件
rm -rf /root/.cache
rm -rf /tmp/*
```

### 6.2 保存镜像
1. 在 AutoDL 控制台点击"保存镜像"
2. 填写镜像名称：`ST-FSOD-Ready`
3. 添加描述：`ST-FSOD 完整环境，包含所有依赖`
4. 选择"公开镜像"（如果要分享）

### 6.3 分享镜像
```
镜像名称: ST-FSOD-Ready
包含内容:
- Python 3.9 + PyTorch 2.0
- mmcv-full 1.7.1
- Dataset4EO + RSI-Detection
- ST-FSOD 项目代码
- 预配置的数据集路径

使用方法:
1. 创建 AutoDL 实例时选择此镜像
2. 上传数据集到 /root/autodl-tmp/datasets/
3. 激活环境: conda activate st_fsod
4. 开始训练: python run_st_fsod.py --dataset dior --split 1 --shots 3shot
```

## ⚡ 优化建议

### 性能优化
```bash
# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export OMP_NUM_THREADS=8

# 调整 batch size（根据 GPU 显存）
# RTX 3090 (24GB): batch_size=8-16
# RTX 4090 (24GB): batch_size=8-16
# A100 (40GB): batch_size=16-32
```

### 监控训练
```bash
# 使用 tmux 保持训练会话
tmux new-session -d -s training
tmux send-keys -t training "conda activate st_fsod" Enter
tmux send-keys -t training "cd /root/ST-FSOD" Enter
tmux send-keys -t training "python run_st_fsod.py --dataset dior --split 1 --shots 3shot" Enter

# 查看训练状态
tmux attach -t training
```

## 🔍 故障排除

### 常见问题
1. **CUDA 版本不匹配**: 重新安装对应版本的 PyTorch
2. **内存不足**: 减少 batch_size 或使用梯度累积
3. **数据集路径错误**: 检查 dataset_config.py 配置
4. **权限问题**: 使用 `chmod +x` 给脚本执行权限

### 日志查看
```bash
# 查看训练日志
tail -f work_dirs/*/训练日志.log

# 查看 GPU 使用情况
nvidia-smi -l 1
```

## 📞 技术支持

- **AutoDL 官方文档**: https://www.autodl.com/docs
- **ST-FSOD 项目**: https://github.com/zhu-xlab/ST-FSOD
- **问题反馈**: 通过 GitHub Issues 或 AutoDL 客服

---

**配置完成后，您就可以在 AutoDL 上高效地运行 ST-FSOD 项目了！**
